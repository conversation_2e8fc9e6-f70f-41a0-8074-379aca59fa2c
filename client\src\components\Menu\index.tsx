import './index.scss';
import MuiMenu, { MenuProps } from '@mui/material/Menu';

const Menu = ({ size, ...props }: Props) => (
  <MuiMenu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    className={size}
    {...props}
  />
);

type Props = MenuProps & {
  size?: 'small' | 'large';
};

export default Menu;
