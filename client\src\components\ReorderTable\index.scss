.reorder-table {
  width: 100%;
  height: 100%;
  position: relative;

  .Sdk-MuiTable-root {
    overflow-y: auto;
    overflow-x: hidden;
  }

  .Sdk-MuiFormHelperText-root {
    margin-left: 0;
  }

  .reorder-table__visibility-select {
    width: 116px;
    height: 30.25px;
  }

  .Sdk-MuiSelect-select {
    padding: 6px 14px 10px 14px;
  }

  .Sdk-MuiPaper-root {
    box-shadow: none;
    height: 100%;

    .Sdk-MuiTableContainer-root {
      height: 100%;

      table {
        height: 100%;
      }
    }
  }

  .Sdk-MuiInputBase-root {
    border-radius: 4px;
    background-color: var(--background-input);

    input {
      padding: 8px 14px;
    }

    .Sdk-MuiInputBase-input {
      font-weight: 400;
      font-size: 14px;
      color: var(--text-primary);
    }

    &.Mui-error {
      border: solid 1px var(--status-red);
    }
  }

  .reorder-table__color-picker {
    left: -8px;
    height: 21px;
  }

  .Mui-TableHeadCell-ResizeHandle-Wrapper {
    display: none;
  }

  .Sdk-MuiTableRow-root {
    // For some reason this style is !important in Sdk-Mui
    background-color: var(--background-table) !important;
    //border-top: solid 2px var(--row-border-table);

    &:first-child {
      border-top: none;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  &.edit-mode {
    .reorder-table__color-picker {
      left: -2px;
      height: 30.25px;
    }

    .Sdk-MuiTableCell-root {
      svg {
        &.reorder-table__error-icon {
          path {
            fill: var(--status-red);
          }
        }
      }

      svg :not(.reorder-table__drag-handle, .Sdk-MuiCircularProgress-circle) {
        fill: var(--text-primary);
      }
    }

    .Sdk-MuiCircularProgress-root {
      color: var(--text-primary);
    }
  }

  .is-loading {
    top: 50%;
    left: 50%;
    position: absolute;
  }

  .Sdk-MuiTableRow-root {
    th {
      transition: none;

      &:first-child {
        opacity: 0;
      }

      &:last-child {
        opacity: 0;
      }
    }

    .color-header {
      .Mui-TableHeadCell-Content {
        display: flex;
        justify-content: center;

        .Mui-TableHeadCell-Content-Actions {
          display: none;
        }
      }
    }

    .Tag-name-header, .Tag-name-body {
      padding-left: 1.75rem;
    }

    td {
      transition: none;

      &:first-child {
        text-align: center;
        justify-content: center;
        align-items: center;
        right: -13px;
      }
    }
  }
}

.error-column {
  color: var(--status-red);
}

.delete-dialog__description-tag {
  font-size: 16px;
}

.row-deleted {
  background-color: var(--background-table) !important;
  opacity: 0.5;
}

.cell-deleted {
  pointer-events: none;
}

.reorder-table__visibility-menu {
  width: 144px;

  .Sdk-MuiMenuItem-root {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;

    & > span {
      width: 60%;
    }
  }
}