//  Start by putting custom Cypress.Commands.add() methods here.
import 'cypress-file-upload';
import {
  SettingDatatTestSelector,
  SettingsGraphql,
} from './helperFunction/settingsHelper';
import {
  dynamicGraphqlQuery,
  Graphql,
} from './helperFunction/caseManagerHelper';
import { SearchDataTestSelector } from './helperFunction/searchResultsHelper';
import { CaseStatus, CaseTag } from '../../src/types/types';
import { DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { ordinalToNumber } from './helperFunction/helper';

Cypress.Commands.add('LoginToApp', () =>
  cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: Cypress.env('username'),
        password: Cypress.env('password'),
      },
    })
    .then((userInfo) => {
      console.log('=====', userInfo.body);
      // Cypress.env('orgId', userInfo.body.organization.organizationId);
      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    })
);

Cypress.Commands.add('loginAsUser', (userKey: string) => {
  const users = Cypress.env('users');
  if (!users) {
    throw new Error(
      "Cypress environment variable 'users' is not defined. Check cypress.env.json."
    );
  }

  const userCredentials = users[userKey];
  if (
    !userCredentials ||
    !userCredentials.username ||
    !userCredentials.password
  ) {
    throw new Error(
      `User key "${userKey}" not found or incomplete in Cypress env 'users'. Check cypress.env.json.`
    );
  }

  return cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: userCredentials.username,
        password: userCredentials.password,
      },
    })
    .then((userInfo) => {
      expect(userInfo.status).to.eq(200);

      console.log('=====', userInfo.body);

      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    });
});

Cypress.Commands.add('LoginLandingPage', () => {
  const url = Cypress.env('visitUrl');

  cy.LoginToApp();
  cy.visit(url);
});

Cypress.Commands.add('Graphql', (query, variables = {}) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/v3/graphql`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: {
      query,
      variables,
    },
  }).then((res) => cy.wrap(res));
});

Cypress.Commands.add(
  'repeat',
  ({ action, times }: { action: unknown; times: number }) => {
    if (typeof action === 'function') {
      Array.from({ length: times }, () => action());
    }
  }
);

Cypress.Commands.add(
  'awaitNetworkResponseCode',
  ({
    alias,
    code,
    repeat = 1,
    timeout = 45000,
  }: {
    alias: string;
    code: number;
    repeat?: number;
    timeout?: number;
  }) => {
    cy.repeat({
      action: cy
        .wait(`${alias}`, { timeout })
        .its('response.statusCode')
        .should('eq', code),
      times: repeat,
    });
    // cy.assertNoLoading();
  }
);

Cypress.Commands.add(
  'filterByAria',
  { prevSubject: true },
  (subject: string, attr: string, value: string) => {
    cy.wrap(subject).filter(`[aria-${attr}="${value}"]`);
  }
);

Cypress.Commands.add('getByRoles', function (role: string) {
  const filters =
    role.split(':').length > 1 ? ':' + role.split(':').slice(1).join(':') : '';

  if (filters === '') {
    this.role = role;
  } else {
    this.role = role.split(':')[0];
  }

  return cy.get(`[role="${this.role}"]${filters}`);
});

Cypress.Commands.add(
  'interceptGraphQLQuery',
  (query: string, alias: string) => {
    cy.intercept('POST', /v3\/graphql/, (req) => {
      if (req.body.query === query) {
        req.alias = alias;
      }
    });
  }
);

Cypress.Commands.add(
  'getDataIdCy',
  ({
    idAlias,
    options = {},
  }: {
    idAlias: string;
    options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
  }) => {
    const matches = idAlias.replace(/@/, '').split(' > ');

    const tagName = matches[0];
    const childCombinators: string | string[] =
      matches.slice(1).join(' > ') ?? '';
    const withChildCombinators =
      childCombinators.length > 0 ? ` > ${childCombinators}` : '';

    return cy.get(`[data-testid="${tagName}"]${withChildCombinators}`, options);
  }
);

Cypress.Commands.add(
  'SelectFile',
  ({ fileName, selector }: { fileName: string; selector: string }) => {
    cy.fixture(fileName, 'binary')
      .then(Cypress.Blob.binaryStringToBlob)
      .then(async (fileContent) => {
        const arrayBuffer = await fileContent.arrayBuffer();
        const blob = new Blob([arrayBuffer], { type: 'video/mp4' });
        cy.get(selector).attachFile({
          fileContent: blob,
          fileName,
          mimeType: 'video/mp4',
        });
      });

    cy.contains(fileName).should('exist');
  }
);

Cypress.Commands.add('getState', () => {
  cy.window().its('store').invoke('getState');
});

Cypress.Commands.add('assertNoLoading', () => {
  Cypress.config('defaultCommandTimeout', 30000);

  cy.getState().should((state) => {
    expect(state.caseManager.cases.status).to.not.equal('loading');
    expect(state.caseManager.selectedCase.status).to.not.equal('loading');
    expect(state.user.isFetching).to.equal(false);
    expect(state.user.isFetchingApplications).to.equal(false);
    expect(state.user.isLoggingIn).to.equal(false);
  });
  cy.getDataIdCy({ idAlias: 'table-body_loading-icon' }).should('not.exist');
});

Cypress.Commands.add('deleteStatusByName', (statusTable: DataTable) => {
  const statusNamesToDelete: string[] = statusTable
    .hashes()
    .map((row) => row.statusName);

  cy.Graphql(SettingsGraphql.FetchSettingStatuses, {}).then((res) => {
    const records: { data: { statuses: CaseStatus[] } }[] =
      res.body?.data?.structuredDataObjects?.records || [];

    cy.log('Fetched records:', records);
    cy.log('Statuses to delete:', statusNamesToDelete);

    const updatedRecords: { data: { statuses: CaseStatus[] } }[] = records.map(
      (record) => ({
        ...record,
        data: {
          ...record.data,
          statuses: record.data.statuses.filter(
            (status) => !statusNamesToDelete.includes(status.label)
          ),
        },
      })
    );

    cy.log('Updated records:', updatedRecords);

    if (updatedRecords.length > 0) {
      const variables = {
        schemaId: '1ef079a4-2f66-4946-955d-ce3665af0c9d',
        data: {
          createdBy: '66c0515f-a43c-4dd4-86e4-b1e022337718',
          statuses: updatedRecords.flatMap(
            (record): CaseStatus[] => record.data.statuses
          ),
        },
      };

      cy.Graphql(SettingsGraphql.DeleteQuery, variables);
    }
  });
});

Cypress.Commands.add('deleteTagByName', (tagTable: DataTable) => {
  const tagNames: string[] = tagTable.hashes().map((row) => row.tagName);

  cy.Graphql(SettingsGraphql.FetchSettingTag, {}).then((res) => {
    const records: { data: { tags: CaseTag[] } }[] =
      res.body?.data?.structuredDataObjects?.records || [];

    cy.log('Fetched records:', records);
    cy.log('Tag names to delete:', tagNames);

    const updatedRecords: { data: { tags: CaseTag[] } }[] = records.map(
      (record) => ({
        ...record,
        data: {
          ...record.data,
          tags: record.data.tags.filter((tag) => !tagNames.includes(tag.label)),
        },
      })
    );

    cy.log('Updated records:', updatedRecords);

    if (updatedRecords.length > 0) {
      const variables = {
        schemaId: 'a30fa449-aeb7-4945-95de-b47130f1d9ed',
        data: {
          createdBy: '34bba39f-21d9-4e1a-9e1c-7bbc76113e41',
          tags: updatedRecords.flatMap((record): CaseTag[] => record.data.tags),
        },
      };

      cy.Graphql(SettingsGraphql.DeleteQuery, variables);
    }
  });
});

Cypress.Commands.add('deleteCaseById', (caseId: string) => {
  cy.Graphql(Graphql.FetchCases, {
    search: {
      index: ['mine'],
      type: 'fa0c925d-01c6-4f28-aab4-1ae49a3ba9f8',
      limit: 500,
      offset: 0,
      sort: [
        { field: 'caseDate', order: 'desc' },
        { field: 'createdDateTime', order: 'desc' },
      ],
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'exists',
            name: 'toBeDeletedTime',
            gt: '2024-01-01T00:00:00.000Z',
            not: true,
          },
        ],
      },
    },
  }).then((res) => {
    const records = res.body?.data?.searchMedia?.jsondata?.results;
    const matchingRecord = records.find(
      (record: { caseId: string }) => record.caseId === caseId
    );
    if (matchingRecord) {
      cy.Graphql(dynamicGraphqlQuery.DeleteCase(matchingRecord.id), {}).then(
        (res2) => {
          const itemTobeDeleted = res2.body.data.structuredDataObject;
          expect(itemTobeDeleted.id).to.equal(matchingRecord.id);
          itemTobeDeleted.data.toBeDeletedTime =
            itemTobeDeleted.data.modifiedDateTime;
          cy.Graphql(Graphql.DeleteCase, itemTobeDeleted);
        }
      );
    }
  });
});

Cypress.Commands.add(
  'assertTableColumnSorted',
  (label: string, orderBy: string) => {
    cy.getDataIdCy({ idAlias: `sort-label-${label}` }).then(($th) => {
      const columnIndex = $th.index();

      cy.get('[data-testid^="table-row-"]').then(($rows) => {
        const texts: string[] = [];

        Cypress._.each($rows, ($row) => {
          const cellText = Cypress.$($row)
            .find(`.table-cell:nth-child(${columnIndex + 1})`)
            .text()
            .trim();
          texts.push(cellText);
        });
        let sortedTexts: string[] = [...texts];

        if (label === 'caseDate') {
          const dates: Date[] = texts.map((text) => new Date(text));
          const sortedDates = [...dates].sort(
            (a, b) => a.getTime() - b.getTime()
          );

          if (orderBy === 'z-a') {
            sortedDates.reverse();
          }

          sortedTexts = sortedDates.map((date) => {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${month}/${day}/${year}`;
          });
        } else {
          sortedTexts.sort();

          if (orderBy === 'z-a') {
            sortedTexts.reverse();
          }
        }

        expect(texts).to.deep.equal(sortedTexts);
      });
    });
  }
);

Cypress.Commands.add(
  'dragToChangePosition',
  (currentPosition: number, targetPosition: number) => {
    const tableRowId = '[data-testid^="reorder-table-row-"]';
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.DragButton })
      .eq(currentPosition - 1)
      .trigger('mousedown', { button: 0 });
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.DragButton })
      .eq(currentPosition - 1)
      .trigger('dragstart', {
        dataTransfer: new DataTransfer(),
      });

    cy.get(tableRowId)
      .eq(targetPosition - 1)
      .trigger('dragenter');
    cy.get(tableRowId)
      .eq(targetPosition - 1)
      .trigger('dragover', {
        force: true,
        dataTransfer: new DataTransfer(),
      });

    cy.get(tableRowId)
      .eq(targetPosition - 1)
      .trigger('drop', {
        force: true,
        dataTransfer: new DataTransfer(),
      });

    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.DragButton })
      .first()
      .trigger('dragend');
  }
);

Cypress.Commands.add(
  'scrollFileAndDoubleClickUntilFound',
  (containerAlias: string, searchText: string, maxAttempts = 30) => {
    const scrollAndDoubleClick = (
      attempts = 0
    ): Cypress.Chainable<JQuery<HTMLElement>> => {
      if (attempts >= maxAttempts) {
        throw new Error(
          `Element with text "${searchText}" not found after ${maxAttempts} scroll attempts within container "${containerAlias}".`
        );
      }

      return cy.get(containerAlias).within(() =>
        cy.get('[data-test="name"]').then(($names) => {
          const element = $names.filter(`:contains("${searchText}")`).first();

          if (element.length) {
            cy.wrap(element).as('expected-row');
            cy.wrap(element).scrollIntoView();
            return cy.wrap(element).dblclick({ force: true, timeout: 3000 });
          } else {
            cy.get(containerAlias).scrollTo('bottom', {
              duration: 1000,
              easing: 'linear',
              timeout: 2000,
            });
            return scrollAndDoubleClick(attempts + 1);
          }
        })
      );
    };

    return scrollAndDoubleClick(0);
  }
);

Cypress.Commands.add(
  'checkItemsByLabelText',
  <K extends string, T extends Record<K, string>>(
    sectionTitlePattern: RegExp,
    items: T[],
    itemKey: K,
    isReset?: boolean
  ) => {
    cy.contains(sectionTitlePattern)
      .first()
      .parentsUntil(`[data-testid=${SearchDataTestSelector.SearchFilter}]`)
      .last()
      .find('label:visible')
      .then(($labels) => {
        if (isReset) {
          cy.wrap($labels).each(($labelEl) => {
            const $input = Cypress.$($labelEl).prev('input[type="checkbox"]');
            if ($input.length > 0 && $input.prop('checked')) {
              cy.wrap($labelEl).click();
            }
          });
        } else {
          const matchingLabels = $labels.filter((_, element) => {
            const labelText = Cypress.$(element).text().trim();
            return items.some((item) => String(item[itemKey]) === labelText);
          });
          if (matchingLabels.length > 0) {
            cy.wrap(matchingLabels).each(($labelEl) => {
              cy.wrap($labelEl).click();
            });
          }
        }
      });
  }
);

Cypress.Commands.add('pressKeyOnTable', (key: string) => {
  cy.get('[data-testid^="table-row-"]')
    .first()
    .parents('[tabindex="0"]')
    .as('table-container');
  cy.get('@table-container').focus();
  cy.get('@table-container').trigger('keydown', { key });
});

Cypress.Commands.add('verifyTableRowSelected', (position?: string) => {
  cy.log(position.toString());
  cy.log(ordinalToNumber(position).toString());
  cy.get('[data-testid^="table-row-"]')
    .eq(ordinalToNumber(position) - 1)
    .as('currentRow');
  cy.get('@currentRow')
    .should('have.css', 'box-shadow')
    .and('not.equal', 'none');
  cy.get('@currentRow').should('have.class', 'selected');
});
