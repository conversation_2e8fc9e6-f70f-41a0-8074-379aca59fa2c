.file-card-header {
  display: flex;
  justify-content: space-between;
  padding: 10px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--background-secondary);

    &.grouped-view-header {
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin-bottom: 11px;
    }

    .file-card-header__actions-wrapper {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .file-card-header__checkbox-label {
      display: flex;
      align-items: center;
      color: var(--text-primary);
      min-width: fit-content;
      padding: 6px;
      border-radius: 4px;

      &.empty-result-disabled {
        opacity: 0.6;
      }
    }

    .file-card-header__blur-images-wrapper {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .file-card-header__blur-label {
      font-size: 12px;
    }
}
