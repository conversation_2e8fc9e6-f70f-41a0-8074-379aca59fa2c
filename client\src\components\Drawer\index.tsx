import './index.scss';
import MuiDrawer from '@mui/material/Drawer';
import { ReactNode } from 'react';

const Drawer = ({
  open,
  anchor = 'right',
  width = 500,
  right = 0,
  onClose,
  children,
  persistent = false,
}: Props) => (
  <MuiDrawer
    open={open}
    anchor={anchor}
    onClose={onClose}
    className="drawerContainer"
    sx={{ right: right > 0 ? right - 1 : 0 }}
    classes={{
      paper: 'drawer__paper-container',
      paperAnchorRight: 'drawer__paper-anchor-right',
    }}
    variant={persistent ? 'persistent' : 'temporary'}
  >
    <div className="drawer__content" style={{ width }}>
      {children}
    </div>
  </MuiDrawer>
);

interface Props {
  open: boolean;
  width: number;
  right?: number;
  onClose: () => void;
  children?: ReactNode;
  persistent?: boolean;
  anchor?: 'left' | 'top' | 'right' | 'bottom';
}

export default Drawer;
