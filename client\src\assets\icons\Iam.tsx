import createSvgIcon from './lib/createSvgIcon';

export const Iam = createSvgIcon(
  <svg
    width="18"
    height="20"
    viewBox="0 0 18 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M14.8097 2.47C14.7297 2.47 14.6497 2.45 14.5797 2.41C12.6597 1.42 10.9997 1 8.99967 1C7.02967 1 5.14967 1.47 3.43967 2.41C3.19967 2.54 2.89967 2.45 2.75967 2.21C2.62967 1.97 2.71967 1.66 2.95967 1.53C4.81967 0.5 6.85967 0 8.99967 0C11.1397 0 12.9997 0.47 15.0397 1.5C15.2897 1.65 15.3797 1.95 15.2497 2.19C15.1597 2.37 14.9997 2.47 14.8097 2.47ZM0.499668 7.72C0.399668 7.72 0.299668 7.69 0.209668 7.63C-0.000331849 7.47 -0.070332 7.16 0.089668 6.93C1.07967 5.53 2.33967 4.43 3.83967 3.66C6.99967 2.04 10.9997 2.03 14.1497 3.65C15.6497 4.42 16.9097 5.5 17.8997 6.9C18.0597 7.12 17.9997 7.44 17.7797 7.6C17.5497 7.76 17.2397 7.71 17.0797 7.5C16.1797 6.22 15.0397 5.23 13.6897 4.54C10.8197 3.07 7.14967 3.07 4.28967 4.55C2.92967 5.25 1.78967 6.25 0.889668 7.5C0.809668 7.65 0.659668 7.72 0.499668 7.72ZM6.74967 19.79C6.61967 19.79 6.49967 19.74 6.39967 19.64C5.52967 18.77 5.05967 18.21 4.38967 17C3.69967 15.77 3.33967 14.27 3.33967 12.66C3.33967 9.69 5.87967 7.27 8.99967 7.27C12.1197 7.27 14.6597 9.69 14.6597 12.66C14.6597 12.7926 14.607 12.9198 14.5132 13.0136C14.4195 13.1073 14.2923 13.16 14.1597 13.16C14.0271 13.16 13.8999 13.1073 13.8061 13.0136C13.7123 12.9198 13.6597 12.7926 13.6597 12.66C13.6597 10.24 11.5697 8.27 8.99967 8.27C6.42967 8.27 4.33967 10.24 4.33967 12.66C4.33967 14.1 4.65967 15.43 5.26967 16.5C5.90967 17.66 6.34967 18.15 7.11967 18.93C7.30967 19.13 7.30967 19.44 7.11967 19.64C6.99967 19.74 6.87967 19.79 6.74967 19.79ZM13.9197 17.94C12.7297 17.94 11.6797 17.64 10.8197 17.05C9.32967 16.04 8.43967 14.4 8.43967 12.66C8.43967 12.5274 8.49235 12.4002 8.58611 12.3064C8.67988 12.2127 8.80706 12.16 8.93967 12.16C9.07228 12.16 9.19945 12.2127 9.29322 12.3064C9.38699 12.4002 9.43967 12.5274 9.43967 12.66C9.43967 14.07 10.1597 15.4 11.3797 16.22C12.0897 16.7 12.9197 16.93 13.9197 16.93C14.1597 16.93 14.5597 16.9 14.9597 16.83C15.2297 16.78 15.4997 16.96 15.5397 17.24C15.5897 17.5 15.4097 17.77 15.1297 17.82C14.5597 17.93 14.0597 17.94 13.9197 17.94ZM11.9097 20C11.8697 20 11.8197 20 11.7797 20C10.1897 19.54 9.14967 18.95 8.05967 17.88C6.65967 16.5 5.88967 14.64 5.88967 12.66C5.88967 11.04 7.26967 9.72 8.96967 9.72C10.6697 9.72 12.0497 11.04 12.0497 12.66C12.0497 13.73 12.9997 14.6 14.1297 14.6C15.2797 14.6 16.2097 13.73 16.2097 12.66C16.2097 8.89 12.9597 5.83 8.95967 5.83C6.11967 5.83 3.49967 7.41 2.34967 9.86C1.95967 10.67 1.75967 11.62 1.75967 12.66C1.75967 13.44 1.82967 14.67 2.42967 16.27C2.52967 16.53 2.39967 16.82 2.13967 16.91C1.87967 17 1.58967 16.87 1.49967 16.62C0.999668 15.31 0.769668 14 0.769668 12.66C0.769668 11.46 0.999668 10.37 1.44967 9.42C2.77967 6.63 5.72967 4.82 8.95967 4.82C13.4997 4.82 17.2097 8.33 17.2097 12.65C17.2097 14.27 15.8297 15.59 14.1297 15.59C12.4297 15.59 11.0497 14.27 11.0497 12.65C11.0497 11.58 10.1197 10.71 8.96967 10.71C7.81967 10.71 6.88967 11.58 6.88967 12.65C6.88967 14.36 7.54967 15.96 8.75967 17.16C9.70967 18.1 10.6197 18.62 12.0297 19C12.2997 19.08 12.4497 19.36 12.3797 19.62C12.3297 19.85 12.1197 20 11.9097 20Z" />
  </svg>,
  'Iam'
);
