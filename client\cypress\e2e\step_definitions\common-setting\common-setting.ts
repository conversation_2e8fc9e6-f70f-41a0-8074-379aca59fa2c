import { When, Then, Given } from '@badeball/cypress-cucumber-preprocessor';
import { settingPage } from '../../../pages/settingPage';
import { SettingsType } from '../../../support/helperFunction/settingsHelper';
import { ordinalToNumber } from '../../../support/helperFunction/helper';
import '../common/common.step';

Given('The user is on Settings screen', () => {
  settingPage.visit();
});

When('The user clicks on the Add button', () => {
  settingPage.clickAddNewButton();
});

When('The user clicks Save Changes button', () => {
  settingPage.clickEditButton();
});

When('The user changes the visibility to {string}', (VisibilityBtn: string) => {
  settingPage.clickEditButton();
  settingPage.scrollToRight();

  settingPage.clickVisibilityBtn();
  settingPage.selectVisibilityOption(VisibilityBtn);
});

When('The user clicks Cancel button', () => {
  settingPage.clickCancelButton();
});

Then(
  'A snack notification {string} confirming this has succeeded',
  (Notification: string) => {
    settingPage.checkSavedNotification(Notification);
  }
);

Then(
  'The user sees the new {string} status {string} for {string} in edit mode',
  (type: string, UpdatedVisibility: string, name: string) => {
    settingPage.checkSaveChangesButtonEnabled(type, name);
    settingPage.checkUpdatedVisibilityInEdit(UpdatedVisibility, name);
  }
);

Then(
  'The affected {string} for {string} should have updated visibility {string}',
  (type: string, name: string, UpdatedVisibility: string) => {
    settingPage.checkUpdatedVisibility(type, name, UpdatedVisibility);
  }
);

When('The user clicks Edit button', () => {
  settingPage.clickEditButton();
  settingPage.scrollToRight();
});

Then('A new button entitled Save Changes appears and is disabled', () => {
  settingPage.checkSaveChangesButtonDisabled();
});

Then('The user sees Add New {string} modal', (type: string) => {
  settingPage.checkAddNewModal(type);
});

When(
  'The user inputs {string} name {string}',
  (type: string, Input: string) => {
    settingPage.typeInNameField(type, Input);
  }
);

Then('The user confirms by clicking the Add button', () => {
  settingPage.clickConfirmButton();
});

Then(
  'The {string} row of the {string} list should be updated with name {string}',
  (rowIndex: string, type: SettingsType, name: string) => {
    settingPage.checkNewPosition(name, type, ordinalToNumber(rowIndex));
  }
);

When(
  'The user selects the visibility next to any status in the list and a dropdown appears',
  () => {
    settingPage.clickVisibilityBtn();
  }
);

When('The user selects {string} visibility', (VisibilityBtn: string) => {
  settingPage.selectVisibilityOption(VisibilityBtn);
});

Then(
  'The affected statuses for {string} have updated names {string}',
  (type: string, UpdatedName: string) => {
    settingPage.checkUpdatedStatusName(UpdatedName, type);
  }
);

When(
  'The user edits the new name {string} of any status in the list',
  (UpdatedName: string) => {
    settingPage.editStatusName(UpdatedName);
  }
);

When('The user selects the delete icon next to any status in the list', () => {
  settingPage.clickDeleteButton();
});

Then('A confirmation dialog appears', () => {
  settingPage.checkDialogIsOpen();
});

Then('The row is greyed out in the list', () => {
  settingPage.checkRowDeletedOpacity();
});

Then(
  'The user can click Cancel to discard the edits, restoring the row to its original state',
  () => {
    settingPage.clickCancelButton();
    settingPage.checkRowNotDeleted();
  }
);

Then(
  'The deleted row {string} are permanently removed from the list',
  (DeletedRowName: string) => {
    settingPage.checkRowIsDeleted(DeletedRowName);
  }
);

Then('The {string} row is selected', (rowOrder: string) => {
  cy.get('[data-testid^="reorder-table-row-"]')
    .eq(ordinalToNumber(rowOrder) - 1)
    .should('have.attr', 'data-selected', 'true');
  cy.get('[data-testid^="reorder-table-row-"]')
    .eq(ordinalToNumber(rowOrder) - 1)
    .within(() => {
      cy.getDataIdCy({ idAlias: 'row-check-box' }).should(
        'have.class',
        'Mui-checked'
      );
    });
});

When('The user confirms the deletion', () => {
  settingPage.clickConfirmButton();
});

When(
  'The user selects multiple {int} statuses using checkboxes',
  (numberOfRows: number) => {
    settingPage.selectMultipleStatuses(numberOfRows);
  }
);

When('The user clicks the Set Color button', () => {
  settingPage.clickSetColorButton();
});

When('The user clicks the Set Visibility button', () => {
  settingPage.clickSetVisibilityButton();
});

When('The user selects Inactive visibility', () => {
  settingPage.selectInactiveVisibility();
});

When('The user selects Active visibility', () => {
  settingPage.selectActiveVisibility();
});

When('The user confirms by clicking Save Changes button', () => {
  settingPage.clickConfirmButton();
});

When('The user clicks the Delete button', () => {
  settingPage.clickDeleteStatusButton();
});

When(
  'The user drags the {string} row to {string} row in the list',
  (currentPosition: string, targetPosition: string) => {
    cy.dragToChangePosition(
      ordinalToNumber(currentPosition),
      ordinalToNumber(targetPosition)
    );
  }
);

When('The user presses {string} key in settings table', (key: string) => {
  cy.get('[data-testid^="reorder-table-row-"] input[type="checkbox"]')
    .first()
    .focus();
  cy.focused().trigger('keydown', { key });
});
