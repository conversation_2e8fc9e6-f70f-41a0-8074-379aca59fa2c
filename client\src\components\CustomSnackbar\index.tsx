/**
 * CustomSnackbar
 *
 * This component is used to display custom notifications using the notistack library.
 *
 * To use this component, you can use 'enqueueSnackbar' as usual, but using variant 'customSnackbar'.
 *    example: enqueueSnackbar('message', { variant: 'customSnackbar' });
 *
 * You can pass all of the props that notistack supports out of the box (persist, autoHideDuration, etc.)
 *
 * NOTE: Passing 'persist === true' will automatically show the close icon.
 *
 * In addition there are custom props that you can pass.  See NotificationProps below for more details.
 */
import './index.scss';
import CloseIcon from '@mui/icons-material/Close';
import { Box, CircularProgress } from '@mui/material';
import cn from 'classnames';
import { CustomContentProps, SnackbarContent, useSnackbar } from 'notistack';
import { forwardRef } from 'react';

export interface CustomActionProp {
  action: () => void;
  label: string;
}

interface NotificationProps extends CustomContentProps {
  loading?: boolean; // Show loading spinner
  hasClose?: boolean; // Show close icon
  customAction?: CustomActionProp; // Custom action button with action and label
  longTextWrap?: boolean; // true to wrap long text and have the actions below the message
}

const CustomNotification = forwardRef<HTMLDivElement, NotificationProps>(
  ({ id, ...props }, ref) => {
    const { message, loading, hasClose, customAction, longTextWrap, persist } =
      props;
    const { closeSnackbar } = useSnackbar();

    return (
      <SnackbarContent ref={ref}>
        <Box
          className={cn('snackbar-noti', {
            'snackbar-noti__longText': longTextWrap,
          })}
        >
          <div>{message}</div>
          <div
            className={cn('snackbar-noti-actions', {
              'snackbar-noti-actions__longText': longTextWrap,
            })}
          >
            <div>
              {loading && (
                <CircularProgress className="snackbar-noti-loading" size={14} />
              )}
              {customAction && customAction.label.length > 0 && (
                <button
                  type="button"
                  className="snackbar-noti-action"
                  onClick={() => {
                    customAction.action();
                    closeSnackbar(id);
                  }}
                >
                  {customAction.label}
                </button>
              )}
            </div>
            <div>
              {(hasClose || persist) && (
                <CloseIcon
                  className="snackbar-noti-close"
                  onClick={() => {
                    // Close the snackbar
                    closeSnackbar(id);
                  }}
                />
              )}
            </div>
          </div>
        </Box>
      </SnackbarContent>
    );
  }
);

CustomNotification.displayName = 'CustomNotification';

export default CustomNotification;
