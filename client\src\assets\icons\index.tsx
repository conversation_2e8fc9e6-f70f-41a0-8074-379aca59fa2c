/* cspell: words Slomo */
export { Audio } from './Audio';
export { Activity } from './Activity';
export { Add } from './Add';
export { AddFilled } from './AddFilled';
export { AddGroup } from './AddGroup';
export { AddOne } from './AddOne';
export { AddToCart } from './AddToCart';
export { AddToCase } from './AddToCase';
export { AddUser } from './AddUser';
export { AddUserTwo } from './AddUserTwo';
export { AdminCenter } from './AdminCenter';
export { AdminTabIcon } from './AdminTabIcon';
export { AiPackage } from './AiPackage';
export { Alarm } from './Alarm';
export { AlarmAdd } from './AlarmAdd';
export { AlarmOn } from './AlarmOn';
export { AlarmTwo } from './AlarmTwo';
export { API } from './API';
export { AppBarArticleIcon } from './AppBarArticleIcon';
export { AppBarInAppGuidance } from './AppBarInAppGuidance';
export { Applications } from './Applications';
export { ApplicationsCircles } from './ApplicationsCircles';
export { ApplicationTabIcon } from './ApplicationTabIcon';
export { Archive } from './Archive';
export { ArrowBack } from './ArrowBack';
export { ArrowDown } from './ArrowDown';
export { ArrowForward } from './ArrowForward';
export { ArrowLeft } from './ArrowLeft';
export { ArrowRight } from './ArrowRight';
export { ArrowUp } from './ArrowUp';
export { Article } from './Article';
export { ArticleFilled } from './ArticleFilled';
export { Assignment } from './Assignment';
export { AssignmentFilled } from './AssignmentFilled';
export { Attachment } from './Attachment';
export { AutomateNode } from './AutomateNode';
export { Banner } from './Banner';
export { Benchmark } from './Benchmark';
export { Blur } from './Blur';
export { Bolt } from './Bolt';
export { BoltFilled } from './BoltFilled';
export { Bookmark } from './Bookmark';
export { BottomQueue } from './BottomQueue';
export { Bullet } from './Bullet';
export { Calendar } from './Calendar';
export { CalendarFilled } from './CalendarFilled';
export { Camera } from './Camera';
export { CameraFilled } from './CameraFilled';
export { Campaign } from './Campaign';
export { CampaignFilled } from './CampaignFilled';
export { Cancel } from './Cancel';
export { Cart } from './Cart';
export { CartFilled } from './CartFilled';
export { CaseDetailNullState } from './CaseDetailNullState';
export { Category } from './Category';
export { CategoryFilled } from './CategoryFilled';
export { Chart } from './Chart';
export { ChartFilled } from './ChartFilled';
export { Chat } from './Chat';
export { ChatFilled } from './ChatFilled';
export { Check } from './Check';
export { Checkmark } from './Checkmark';
export { CheckmarkFilled } from './CheckmarkFilled';
export { ChevronLeft } from './ChevronLeft';
export { ChevronRight } from './ChevronRight';
export { Class } from './Class';
export { ClassFilled } from './ClassFilled';
export { Close } from './Close';
export { Cloud } from './Cloud';
export { CloudFilled } from './CloudFilled';
export { CmsTabIcon } from './CmsTabIcon';
export { ColorIcon } from './ColorIcon.tsx';
export { Comfy } from './Comfy';
export { Compare } from './Compare';
export { Copy } from './Copy';
export { CoronaVirus } from './CoronaVirus';
export { CurlyBraces } from './CurlyBraces';
export { Cut } from './Cut';
export { DAG } from './DAG';
export { DagBuilder } from './DagBuilder';
export { DAGIcon } from './DAGIcon';
export { DAGTemplate } from './DAGTemplate';
export { Dashboard } from './Dashboard.tsx';
export { DataCenter } from './DataCenter';
export { DataRegistry } from './DataRegistry';
export { Delete } from './Delete';
export { DeleteFiles } from './DeleteFiles';
export { DeleteFilled } from './DeleteFilled';
export { DeleteWithLines } from './DeleteWithLines';
export { DeleteX } from './DeleteX';
export { Description } from './Description';
export { DeveloperBoard } from './DeveloperBoard';
export { DeveloperCenter } from './DeveloperCenter';
export { DeviceFilled } from './DeviceFilled';
export { Document } from './Document';
export { Download } from './Download';
export { DownloadFilled } from './DownloadFilled';
export { Edit } from './Edit';
export { EditAttributes } from './EditAttributes';
export { EditFilled } from './EditFilled';
export { EditList } from './EditList';
export { Email } from './Email';
export { EmailFilled } from './EmailFilled';
export { EngineBuild } from './EngineBuild';
export { EngineFilled } from './EngineFilled';
export { Engines } from './Engines';
export { Engines2 } from './Engines2';
export { EnginesMarketplace } from './EnginesMarketplace';
export { Expand } from './Expand';
export { ExpandLess } from './ExpandLess';
export { ExpandMore } from './ExpandMore';
export { Explore } from './Explore';
export { Export } from './Export';
export { Eye } from './Eye';
export { FaceRecognition } from './FaceRecogintion';
export { Favorite } from './Favorite';
export { FavoriteFilled } from './FavoriteFilled';
export { FileFilled } from './FileFilled';
export { Filename } from './Filename';
export { FileNullState } from './FileNullState';
export { Filter } from './Filter';
export { FilterCaseDetails } from './FilterCaseDetails.tsx';
export { FilterRemove } from './FilterRemove';
export { FireFilled } from './FireFilled';
export { FiveG } from './FiveG';
export { Flag } from './Flag';
export { FlowCenter } from './FlowCenter';
export { FlowCenter2 } from './FlowCenter2';
export { Flows } from './Flows';
export { FlowTemplate } from './FlowTemplate';
export { FlowTemplate2 } from './FlowTemplate2';
export { Folder } from './Folder';
export { FolderAddFilled } from './FolderAddFilled';
export { FolderClosed } from './FolderClosed';
export { FolderFilled } from './FolderFilled';
export { FolderMove } from './FolderMove';
export { FolderOpen } from './FolderOpen';
export { FolderSpecialFilled } from './FolderSpecialFilled';
export { Forum } from './Forum';
export { ForumFilled } from './ForumFilled';
export { FourK } from './FourK';
export { Fullscreen } from './Fullscreen';
export { FullscreenExit } from './FullscreenExit';
export { GridFilled } from './GridFilled';
export { GridIcon } from './GridIcon';
export { Group } from './Group';
export { Hamburger } from './Hamburger';
export { Headset } from './Headset';
export { HeadsetFilled } from './HeadsetFilled';
export { HelpCenter } from './HelpCenter';
export { HelpFilled } from './HelpFilled';
export { Hidden } from './Hidden';
export { Hide } from './Hide';
export { HideLeft } from './HideLeft';
export { History } from './History';
export { Home } from './Home';
export { HomeFilled } from './HomeFilled';
export { Human } from './Human';
export { HumanFilled } from './HumanFilled';
export { Iam } from './Iam';
export { Illuminate } from './Illuminate';
export { Image } from './Image';
export { ImageFilled } from './ImageFilled';
export { ImpersonateIcon } from './ImpersonateIcon';
export { Information } from './Information';
export { InformationFilled } from './InformationFilled';
export { Input } from './Input';
export { Key } from './Key';
export { KeyFilled } from './KeyFilled';
export { DeploymentModelIcons } from './lib/deployment-model';
export { EngineCategoryIcons } from './lib/engine-category';
export { EngineClassIcons } from './lib/engine-class';
export { EngineFlagIcons } from './lib/engine-flag';
export { EngineModeIcons } from './lib/engine-mode';
export { EvidenceTypes } from './lib/evidence-type';
export { FileTypes } from './lib/file-type';
export { LibraryCenter } from './LibraryCenter';
export { List } from './List';
export { ListView } from './ListView';
export { Load } from './Load';
export { Location } from './Location';
export { LocationFilled } from './LocationFilled';
export { LocationOne } from './LocationOne';
export { Lock } from './Lock';
export { Lock2 } from './Lock2';
export { LockFilled } from './LockFilled';
export { Marketplace } from './Marketplace';
export { MarketplaceFilled } from './MarketplaceFilled';
export { MarketplaceTwo } from './MarketplaceTwo';
export { Merge } from './Merge';
export { Mode } from './Mode';
export { Mode2 } from './Mode2';
export { MoreHor } from './MoreHor';
export { MoreVert } from './MoreVert';
export { Move } from './Move.tsx';
export { MoveFolder } from './MoveFolder';
export { NearMe } from './NearMe';
export { NearMeFilled } from './NearMeFilled';
export { NewFolder } from './NewFolder';
export { NoApps } from './NoApps';
export { Node } from './Node';
export { NoLink } from './NoLink';
export { Notifications } from './Notifications';
export { NotificationsActive } from './NotificationsActive';
export { NotificationsActiveFilled } from './NotificationsActiveFilled';
export { NotificationsFilled } from './NotificationsFilled';
export { NotificationsOff } from './NotificationsOff';
export { NotificationsOffFilled } from './NotificationsOffFilled';
export { NotInterested } from './NotInterested';
export { OpenWindow } from './OpenWindow';
export { Organization } from './Organization';
export { Organizations } from './Organizations';
export { OrganizationTabIcon } from './OrganizationTabIcon';
export { OrgFilled } from './OrgFilled';
export { OrgSettings } from './OrgSettings';
export { Packages } from './Packages';
export { PackagesAvatar } from './PackagesAvatar';
export { Palette } from './Palette';
export { Pause } from './Pause';
export { PersonalProfile } from './PersonalProfile';
export { PersonalProfileTabIcon } from './PersonalProfileTabIcon';
export { PersonRemove } from './PersonRemove';
export { Pin } from './Pin';
export { Play } from './Play';
export { PlayBack } from './PlayBack';
export { PlaySlomo } from './PlaySlomo';
export { PodcastSource } from './PodcastSource';
export { PostAdd } from './PostAdd';
export { PreferenceTabIcon } from './PreferenceTabIcon';
export { Preview } from './Preview';
export { Print } from './Print';
export { PrintFilled } from './PrintFilled';
export { Process } from './Process';
export { ProcessingDetail } from './ProcessingDetail';
export { Radio } from './Radio';
export { RadioSource } from './RadioSource';
export { Refresh } from './Refresh';
export { Registered } from './Registered';
export { Remove } from './Remove';
export { RemoveFilled } from './RemoveFilled';
export { RemoveOne } from './RemoveOne';
export { Replay } from './Replay';
export { ReplayFive } from './ReplayFive';
export { ReplayTen } from './ReplayTen';
export { ReplayThirty } from './ReplayThirty';
export { ReplyFilled } from './ReplyFilled';
export { Reprocess } from './Reprocess';
export { ReprocessOne } from './ReprocessOne';
export { Resources } from './Resources';
export { RetentionPolicy } from './RetentionPolicy';
export { Satisfied } from './Satisfied';
export { SatisfiedFilled } from './SatisfiedFilled';
export { Save } from './Save';
export { SaveDown } from './SaveDown';
export { ScheduleBulk } from './ScheduleBulk';
export { ScheduleJob } from './ScheduleJob';
export { Schema } from './Schema';
export { SchemaFilled } from './SchemaFilled';
export { Search } from './Search';
export { SearchedFor } from './SearchedFor';
export { SearchImage } from './SearchImage';
export { SearchOff } from './SearchOff';
export { SearchPerson } from './SearchPerson';
export { SearchStructuredData } from './SearchStructuredData';
export { SecurityGroup } from './SecurityGroup';
export { Send } from './Send';
export { SendFilled } from './SendFilled';
export { Settings } from './Settings';
export { SettingsFilled } from './SettingsFilled';
export { Share } from './Share';
export { ShowSelected } from './ShowSelected';
export { SixFeetApart } from './SixFeetApart';
export { Smart } from './Smart';
export { Sort } from './Sort';
export { SortArrow } from './SortArrow';
export { Sound } from './Sound';
export { SoundFilled } from './SoundFilled';
export { Source } from './Source';
export { SourceThree } from './SourceThree';
export { SourceTwo } from './SourceTwo';
export { SpecialFolder } from './SpecialFolder';
export { Speech } from './Speech';
export { SpeechFilled } from './SpeechFilled';
export { SpeechTwo } from './SpeechTwo';
export { Star } from './Star';
export { StarFilled } from './StarFilled';
export { StarHalf } from './StarHalf';
export { Subject } from './Subject';
export { Sunny } from './Sunny';
export { SunnyFilled } from './SunnyFilled';
export { Swap } from './Swap';
export { SwapVert } from './SwapVert';
export { SystemNotification } from './SystemNotification';
export { Tag } from './Tag';
export { TagFilled } from './TagFilled';
export { Task } from './Task';
export { TextIcon } from './Text';
export { Time } from './Time';
export { TimeBack } from './TimeBack';
export { TimeFilled } from './TimeFilled';
export { TimeUpdate } from './TimeUpdate';
export { ToolsDrawer } from './ToolsDrawer';
export { TopQueue } from './TopQueue';
export { Tune } from './Tune';
export { TVOff } from './TVOff';
export { TVOn } from './TVOn';
export { TVSource } from './TVSource';
export { Undefined } from './Undefined';
export { UnknownDocument } from './UnknownDocument';
export { UpDown } from './UpDown';
export { Upload } from './Upload';
export { UploadFilled } from './UploadFilled';
export { User } from './User';
export { UserAddFilled } from './UserAddFilled';
export { UserAddTwoFilled } from './UserAddTwoFilled';
export { UserFilled } from './UserFilled';
export { Users } from './Users';
export { UsersFilled } from './UsersFilled';
export { Verified } from './Verified';
export { VerifiedFilled } from './VerifiedFilled';
export { VeritoneLogo } from './VeriLogo';
export { VideoCamera } from './VideoCamera';
export { VideoCameraFilled } from './VideoCameraFilled';
export { View } from './View';
export { VisibilityFilled } from './VisibilityFilled';
export { Voice } from './Voice';
export { VoiceFilled } from './VoiceFilled';
export { Widgets } from './Widgets';
export { WidgetsFilled } from './WidgetsFilled';
export { YoutubeFilled } from './YoutubeFilled';
export { YoutubeSource } from './YoutubeSource';
export { ZoomOut } from './ZoomOut';

// interface IStandardIcons {
//   [x: string]: any;
// }

// export AlarmTwo;
//
// export default {
//   AutomateNode,
//   TopQueue,
//   BottomQueue,
//   Pause,
//   Cancel,
//   Activity,
//   AddFilled,
//   ApplicationTabIcon,
//   API,
//   Add,
//   AdminTabIcon,
//   AddOne,
//   AddGroup,
//   AddToCart,
//   AddUser,
//   AddUserTwo,
//   AiPackage,
//   Alarm,
//   AlarmAdd,
//   AlarmOn,
//   AlarmTwo,
//   Applications,
//   ApplicationsCircles,
//   Archive,
//   ArrowBack,
//   ArrowDown,
//   ArrowForward,
//   ArrowLeft,
//   ArrowRight,
//   ArrowUp,
//   Article,
//   ArticleFilled,
//   Assignment,
//   AssignmentFilled,
//   Attachment,
//   Banner,
//   Benchmark,
//   Bolt,
//   BoltFilled,
//   Bookmark,
//   Bullet,
//   Calendar,
//   CalendarFilled,
//   Camera,
//   CameraFilled,
//   Campaign,
//   CampaignFilled,
//   Cart,
//   CartFilled,
//   Category,
//   CategoryFilled,
//   Chart,
//   ChartFilled,
//   Chat,
//   ChatFilled,
//   Check,
//   CheckmarkFilled,
//   ChevronLeft,
//   ChevronRight,
//   Class,
//   ClassFilled,
//   Close,
//   Cloud,
//   CloudFilled,
//   CmsTabIcon,
//   Comfy,
//   Compare,
//   Copy,
//   CoronaVirus,
//   CurlyBraces,
//   Cut,
//   DataRegistry,
//   DAG,
//   DAGTemplate,
//   DagBuilder,
//   Delete,
//   DeleteFilled,
//   Description,
//   DeveloperBoard,
//   DeviceFilled,
//   Download,
//   DownloadFilled,
//   Edit,
//   EditFilled,
//   EditAttributes,
//   Email,
//   EmailFilled,
//   EngineFilled,
//   EngineBuild,
//   Engines,
//   Engines2,
//   EnginesMarketplace,
//   Expand,
//   ExpandLess,
//   ExpandMore,
//   Export,
//   Favorite,
//   FavoriteFilled,
//   FileFilled,
//   Filter,
//   FilterRemove,
//   FireFilled,
//   FiveG,
//   Flag,
//   FlowCenter,
//   Flows,
//   FlowTemplate2,
//   FlowTemplate,
//   Folder,
//   FolderAddFilled,
//   FolderFilled,
//   FolderOpen,
//   FolderClosed,
//   FolderSpecialFilled,
//   Forum,
//   ForumFilled,
//   FourK,
//   Fullscreen,
//   FullscreenExit,
//   GridIcon,
//   GridFilled,
//   Group,
//   Hamburger,
//   Headset,
//   HeadsetFilled,
//   HelpCenter,
//   HelpFilled,
//   Hide,
//   Hidden,
//   HideLeft,
//   History,
//   Home,
//   HomeFilled,
//   Human,
//   HumanFilled,
//   Illuminate,
//   Image,
//   ImageFilled,
//   Information,
//   InformationFilled,
//   Input,
//   Key,
//   KeyFilled,
//   List,
//   Load,
//   Location,
//   LocationFilled,
//   LocationOne,
//   Lock,
//   LockFilled,
//   Marketplace,
//   MarketplaceFilled,
//   MarketplaceTwo,
//   Merge,
//   Mode,
//   Mode2,
//   MoreHor,
//   MoreVert,
//   MoveFolder,
//   NearMe,
//   NearMeFilled,
//   NewFolder,
//   NoLink,
//   NotInterested,
//   Notifications,
//   NotificationsActive,
//   NotificationsActiveFilled,
//   NotificationsFilled,
//   NotificationsOff,
//   NotificationsOffFilled,
//   OpenWindow,
//   OrgFilled,
//   OrgSettings,
//   Organization,
//   OrganizationTabIcon,
//   Packages,
//   PackagesAvatar,
//   PersonRemove,
//   PreferenceTabIcon,
//   PersonalProfileTabIcon,
//   Pin,
//   Play,
//   PlayBack,
//   PlaySlomo,
//   PodcastSource,
//   PostAdd,
//   Preview,
//   Print,
//   PrintFilled,
//   Process,
//   ProcessingDetail,
//   Radio,
//   RadioSource,
//   Resources,
//   Refresh,
//   Registered,
//   Remove,
//   RemoveFilled,
//   RemoveOne,
//   Replay,
//   ReplayFive,
//   ReplayTen,
//   ReplayThirty,
//   ReplyFilled,
//   Reprocess,
//   ReprocessOne,
//   RetentionPolicy,
//   Satisfied,
//   SatisfiedFilled,
//   Save,
//   SaveDown,
//   ScheduleJob,
//   ScheduleBulk,
//   Schema,
//   SchemaFilled,
//   Search,
//   SearchImage,
//   SearchOff,
//   SearchPerson,
//   SearchedFor,
//   SecurityGroup,
//   Send,
//   SendFilled,
//   Settings,
//   SettingsFilled,
//   Share,
//   ShowSelected,
//   SixFeetApart,
//   Smart,
//   Sort,
//   Sound,
//   SoundFilled,
//   Source,
//   SourceThree,
//   SourceTwo,
//   SpecialFolder,
//   Speech,
//   SpeechFilled,
//   SpeechTwo,
//   Star,
//   StarFilled,
//   StarHalf,
//   SearchStructuredData,
//   Subject,
//   Sunny,
//   SunnyFilled,
//   Swap,
//   SwapVert,
//   SystemNotification,
//   TVOff,
//   TVOn,
//   TVSource,
//   Tag,
//   TagFilled,
//   Time,
//   TimeBack,
//   TimeFilled,
//   TimeUpdate,
//   Tune,
//   Undefined,
//   UnknownDocument,
//   UpDown,
//   Upload,
//   UploadFilled,
//   User,
//   UserAddFilled,
//   UserAddTwoFilled,
//   UserFilled,
//   Users,
//   UsersFilled,
//   Verified,
//   VeritoneLogo,
//   VerifiedFilled,
//   VideoCamera,
//   VideoCameraFilled,
//   View,
//   VisibilityFilled,
//   Voice,
//   VoiceFilled,
//   Widgets,
//   WidgetsFilled,
//   YoutubeFilled,
//   YoutubeSource,
//   ZoomOut,
//   AdminCenter,
//   DeveloperCenter,
//   DataCenter,
//   Organizations,
//   PersonalProfile,
//   FlowCenter2,
//   LibraryCenter,
//   AppBarArticleIcon,
//   AppBarInAppGuidance,
//   Explore,
//   ToolsDrawer,
//   Impersonate: ImpersonateIcon,
// };
