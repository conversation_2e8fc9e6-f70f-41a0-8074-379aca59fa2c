import { useEffect, useRef, useState } from 'react';

export const useObserveScrollBar = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasScrollBar, setHasScrollBar] = useState(false);

  useEffect(() => {
    const el = containerRef.current;
    if (!el) {
      return;
    }

    const observer = new ResizeObserver(() => {
      setHasScrollBar(el.scrollHeight > el.clientHeight);
    });

    observer.observe(el);

    return () => observer.disconnect();
  }, []);

  return { hasScrollBar, containerRef };
};
