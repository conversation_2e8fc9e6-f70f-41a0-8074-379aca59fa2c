import ConfirmationModal from '@components/ConfirmationModal';
import { I18nTranslate } from '@i18n';

interface CaseCreatedModalProps {
  showCaseCreatedModal: boolean;
  closeCaseCreatedModal: () => void;
  onCloseOKAndGoToNewCase: () => void;
}

const CaseCreatedModal = ({
  showCaseCreatedModal,
  closeCaseCreatedModal,
  onCloseOKAndGoToNewCase,
}: CaseCreatedModalProps) => {
  const intl = I18nTranslate.Intl();
  return (
    <div>
      <ConfirmationModal
        onConfirmOkBtnClick={onCloseOKAndGoToNewCase}
        closeConfirmationModal={closeCaseCreatedModal}
        showConfirmationModal={showCaseCreatedModal}
        titleText={intl.formatMessage({ id: 'caseCreatedSuccessfully' })}
        mainMessageText={intl.formatMessage({ id: 'youCanStartAddingFiles' })}
        backCancelButtonText={intl.formatMessage({ id: 'close' })}
        confirmOkButtonText={intl.formatMessage({ id: 'goToNewCase' })}
      />
    </div>
  );
};

export default CaseCreatedModal;
