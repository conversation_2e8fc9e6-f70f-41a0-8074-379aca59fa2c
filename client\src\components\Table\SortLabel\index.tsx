import './index.scss';
import { OrderBy as FileOrderBy } from '@components/CaseManager/FileTable';
import {
  ArrowDropDown as ArrowDropDownIcon,
  ArrowDropUp as ArrowDropUpIcon,
} from '@mui/icons-material';
import { Box, TableCell } from '@mui/material';
import cn from 'classnames';
import { ReactNode } from 'react';

// TODO: Persist individual direction
const SortLabel = ({
  direction,
  orderBy,
  header,
  field,
  handleSort,
  isSortable,
  id,
  width,
}: Props) => {
  let sortField = field;
  if (sortField === 'fileName') {
    sortField = FileOrderBy.FILE_NAME;
  }

  return (
    <TableCell
      key={id}
      sortDirection={orderBy === sortField ? direction : false}
      sx={{ width, whiteSpace: 'nowrap' }}
      data-testid={`sort-label-${field}`}
    >
      <div
        onClick={() => isSortable && handleSort(sortField)}
        className={cn('sort-label', { sortable: isSortable })}
        data-testid={`sort-${field}`}
      >
        {header}
        <Box
          className={cn('sort-icon', { 'visible-icon': orderBy === sortField })}
        >
          {direction === 'asc' ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
        </Box>
      </div>
    </TableCell>
  );
};

interface Props {
  direction: 'asc' | 'desc';
  orderBy: string;
  header: ReactNode;
  field: string;
  handleSort: (orderBy: string) => void;
  isSortable?: boolean;
  id: string;
  width?: string;
}

export default SortLabel;
