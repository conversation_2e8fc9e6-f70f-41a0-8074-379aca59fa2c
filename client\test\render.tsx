import { configureAppStore } from '@store/index';
import { RenderOptions, render as renderUI } from '@testing-library/react';
import { SnackbarProvider } from 'notistack';
import { ReactNode } from 'react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { vi } from 'vitest';
import translations from '../src/i18n/messages/en-US.json';

vi.mock(import('notistack'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useSnackbar: () => ({
      enqueueSnackbar: vi.fn(),
      closeSnackbar: vi.fn(),
    }),
    enqueueSnackbar: vi.fn(),
  };
});

export const render = (ui: ReactNode, options?: RenderOptions) => {
  const store = configureAppStore();
  return renderUI(
    <SnackbarProvider>
      <Provider store={store}>
        <IntlProvider locale="en" messages={translations}>
          {ui}
        </IntlProvider>
      </Provider>
    </SnackbarProvider>,
    options
  );
};
