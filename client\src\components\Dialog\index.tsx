import './index.scss';
import { Close } from '@assets/icons';
import { I18nTranslate } from '@i18n';
import { Button, Dialog as MuiDialog, TextField } from '@mui/material';
import cn from 'classnames';
import { ReactNode, useState } from 'react';

const Dialog = (props: Props) => {
  const {
    children,
    open,
    title,
    onClose,
    onConfirm,
    disableConfirm,
    confirmText = 'Add',
    cancelText = 'Cancel',
    isDelete,
    useTypeConfirmation,
  } = props;
  const intl = I18nTranslate.Intl();

  const [enableConfirm, setEnableConfirm] = useState(false);

  const handleCloseModal = () => {
    setEnableConfirm(false);
    onClose();
  };

  const handleOnConfirm = () => {
    setEnableConfirm(false);
    onConfirm();
  };

  return (
    <MuiDialog
      className={cn('dialog', { 'use-type-confirmation': useTypeConfirmation })}
      open={open}
      data-testid="dialog"
    >
      <div className="dialog-title" data-testid="status-dialog-title">
        <div>{title}</div>
        <Button onClick={handleCloseModal} className="icon-button">
          <Close />
        </Button>
      </div>
      <div className="dialog-content">{children}</div>
      {useTypeConfirmation && (
        <div className="dialog-content__type-confirmation">
          <div className="dialog-content__type-confirmation-text">
            {I18nTranslate.TranslateMessage('confirmByTyping')}&nbsp;
            <span>{I18nTranslate.TranslateMessage('deleteFile')}</span>&nbsp;
            {I18nTranslate.TranslateMessage('below')}
          </div>
          <TextField
            className="dialog-content__type-confirmation-input"
            placeholder={intl.formatMessage({ id: 'deleteFile' })}
            variant="outlined"
            size="small"
            slotProps={{
              htmlInput: {
                'data-testid': 'type-confirmation-input',
              },
            }}
            onChange={(e) => {
              if (e.target.value === intl.formatMessage({ id: 'deleteFile' })) {
                setEnableConfirm(true);
              } else {
                setEnableConfirm(false);
              }
            }}
            onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                if (
                  e.currentTarget.value ===
                  intl.formatMessage({ id: 'deleteFile' })
                ) {
                  setEnableConfirm(true);
                } else {
                  setEnableConfirm(false);
                }
              }
            }}
          />
        </div>
      )}
      <div className="dialog-buttons">
        <Button
          className="dialog-buttons__close-button"
          onClick={handleCloseModal}
          data-testid="close-button"
        >
          {cancelText}
        </Button>
        <Button
          data-testid="confirm-button"
          className={cn('dialog-buttons__confirm-button', {
            disabled: useTypeConfirmation ? !enableConfirm : disableConfirm,
            delete: isDelete,
          })}
          onClick={handleOnConfirm}
          disabled={useTypeConfirmation ? !enableConfirm : disableConfirm}
        >
          {confirmText}
        </Button>
      </div>
    </MuiDialog>
  );
};
interface Props {
  open: boolean;
  title: string;
  onClose: () => void;
  onConfirm: () => void;
  children: ReactNode;
  disableConfirm: boolean;
  confirmText?: string;
  cancelText?: string;
  isDelete?: boolean;
  useTypeConfirmation?: boolean;
  'data-testid'?: string;
}

export default Dialog;
