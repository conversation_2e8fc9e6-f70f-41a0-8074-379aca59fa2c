import { configureAppStore } from '@store/index';
import { ConfigSliceState } from '@store/modules/config/slice';
import * as metadataSlice from '@store/modules/metadata/slice';
import {
  MetadataSliceState,
  initialState as metadataInitialState,
} from '@store/modules/metadata/slice';
import { waitFor } from '@testing-library/dom';
import { GQLApi } from '@utils/helpers';
import { Provider } from 'react-redux';
import { MemoryRouter, useParams } from 'react-router';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CaseDetailsTable from '.';
import { render } from '../../../../test/render';

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useParams: vi.fn(),
  };
});

const initialState: {
  metadata: MetadataSliceState;
  config: Window['config'];
  appConfig: ConfigSliceState;
  auth: {
    sessionToken: string;
    user: {
      preferredLanguage: string;
    };
  };
} = {
  metadata: {
    ...metadataInitialState,
    metadata: {
      byFolderId: {
        folder123: {
          status: 'idle',
          error: undefined,
          data: {},
        },
      },
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  appConfig: {
    statusSchema: {
      status: 'complete',
      error: '',
      id: 'statusSchema123',
    },
    tagSchema: {
      status: 'complete',
      error: '',
      id: 'tagSchema123',
    },
    evidenceTypeSchema: {
      status: 'complete',
      error: '',
      id: 'evidenceTypeSchema123',
    },
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
  },
  auth: {
    sessionToken: 'sessionToken',
    user: {
      preferredLanguage: '',
    },
  },
};

describe('Case Details Table', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useParams).mockReturnValue({ selectedFolderId: 'folder123' });
  });

  it('calls fetchMetadata when cases and selectedFolderId exist', async () => {
    const store = configureAppStore(initialState);
    const fetchMetadataSpy = vi.spyOn(metadataSlice, 'fetchMetadata');
    vi.spyOn(GQLApi.prototype, 'getFolder').mockResolvedValue({
      id: 'folder123',
      name: 'Test Folder',
      treeObjectId: 'treeObj123',
      createdDateTime: '2025-03-20T09:00:00Z',
      modifiedDateTime: '2025-03-21T10:15:00Z',
      contentTemplates: [
        {
          id: 'template-1',
          sdo: {
            id: 'sdo-1',
            schemaId: 'schema-1',
            data: {
              createdDateTime: '2024-01-01T00:00:00Z',
              modifiedDateTime: '2024-01-01T00:00:00Z',
              caseName: 'Test Case',
              caseId: 'case-1',
              description: 'Test description',
              caseDate: '2024-01-01',
              statusId: 'Open',
              preconfiguredTagIds: ['1'],
              folderId: 'folder-456',
              createdBy: 'user-1',
              modifiedBy: 'user-1',
              createdByName: 'Test User',
            },
          },
        },
      ],
      description: 'A mocked folder for testing',
    });
    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockResolvedValue({
      id: '',
      email: '',
      lastName: '',
      firstName: '',
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <CaseDetailsTable />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(fetchMetadataSpy).toHaveBeenCalledWith({
        folderId: 'folder123',
        evidenceTypeSchemaId: 'evidenceTypeSchema123',
      });
    });
  });
});
