.edit-metadata-input {
  position: relative;
  width: 100%;

  .metadata-input {
    background: var(--background-secondary);
  }

  .normal-input {
    height: 36px;
  }

  .text-area-input {
    height: 99px;
    padding: 14px 14px;

    .Sdk-MuiInputBase-input {
      height: 100% !important; // there is inline style to fix height value
    }
  }

  .text-area-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    transform: rotate(135deg);
    color: #9e9e9e;
  }
}
