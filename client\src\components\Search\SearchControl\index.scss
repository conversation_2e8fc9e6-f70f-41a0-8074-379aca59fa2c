.search-page__search-control {
  flex: 1;
  padding-left: 21px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 40px;

  .search-control__total-results {
    font-size: 12px;
    color: var(--text-primary);
  }

  .search-control__group-buttons {
    display: flex;
    align-items: center;
    gap: 15px;

    .search-control__actions-button {
      display: flex;
      align-items: center;
      gap: 5px;
      color: var(--text-primary);
      padding: 5px 15px;
      font-size: 12px;
      border-radius: 4px;

      & > svg {
        font-size: 18px;
      }
    }

    .search-control__actions-select {
      display: flex;
      align-items: center;
      gap: 10px;
      color: var(--text-primary);
      font-size: 12px;
    }

    .search-control__icon-button {
      color: var(--text-primary);
    }
  }
}

.search-control__menu-list {
  .Sdk-MuiMenuItem-root {
    height: 40px;
  }
}

.search-view-list {
  width: 200px;
}
