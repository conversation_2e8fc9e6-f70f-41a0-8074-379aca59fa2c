.edit-metadata-form {
  height: 100%;

  .edit-metadata-container {
    width: 100%;
    height: 100%;
    padding: 10px 10px 10px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: var(--background-primary);
    color: var(--text-primary);

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 8px;

      & > p {
        margin: 0;
        font-size: 20px;
        font-weight: bold;
      }

      .close-btn {
        color: var(--text-primary);
      }
    }

    .warning {
      padding: 0 8px;
      margin: 0;
    }

    .main-content {
      height: 760px;
      background: var(--background-primary);
      border-radius: 4px;
      border: 1px solid var(--border-color);
      padding: 20px 25px 10px 25px;
      overflow: hidden;
      overflow-y: auto;
      scrollbar-color: #dadada transparent;
      display: flex;
      flex-direction: column;
      gap: 22px;
    }

    .footer {
      margin: auto 8px 10px 8px;
      display: flex;
      justify-content: flex-end;
      gap: 10px;

      .Sdk-MuiButtonBase-root {
        height: 40px;
      }

      .cancel {
        color: var(--text-primary);
      }

      .submit {
        background: #054fa3 !important;
        color: #ffff;
      }

      .Mui-disabled {
        background: #dadada !important;
        color: var(--button-text-disabled);
      }
    }
  }
}
