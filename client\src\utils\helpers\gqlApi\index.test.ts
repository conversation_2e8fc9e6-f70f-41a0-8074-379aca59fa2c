import { CaseSDO } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';
import { describe, expect, it } from 'vitest';
import config from '../../../../config.json';
// not unit test. just a driver to help troubleshoot gql api
describe('gql api client for test', () => {
  it.skip('getSDOSchemaId', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = 'c75ec8f2-e02d-4d92-91da-2b56d75802f2';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const dataRegistryId = '9028e1bc-d5c5-43ec-8482-45fad282f8e2';
    const got = await gql.getSDOSchemaId(dataRegistryId);
    console.log('got =====>', got);
    expect(got).not.toBeNull();
  }, 50000);

  it.skip('getSDO', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = '3b602b24-7c97-4536-bd10-05ae8bf323a9';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const schemaId = 'fca6103a-f1a9-4878-92a6-4741fc373f25';
    const sdoId = '44a1d29c-e411-4dbf-8ddb-e935c2cff896';
    const got = await gql.getSDO({ schemaId, id: sdoId });
    console.log('got =====>', got);
    expect(got).not.toBeNull();
  }, 50000);

  it.skip('getRootFolder', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = 'c75ec8f2-e02d-4d92-91da-2b56d75802f2';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const got = await gql.getRootFolder();
    console.log('got =====>', got);
    expect(got).not.toBeNull();
  }, 50000);

  it.skip('createStructuredData', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = '3b602b24-7c97-4536-bd10-05ae8bf323a9';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const nowStr = new Date().toISOString();
    const schemaId = 'fca6103a-f1a9-4878-92a6-4741fc373f25';
    const data = {
      caseName: 'JiaCase1',
      caseId: '123',
      description: `Jia's Case`,
      caseDate: nowStr,
      preconfiguredTagIds: ['South Los Angeles'],
      statusId: 'active',
      createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
      folderId: '123',
      createdDateTime: nowStr,
      modifiedDateTime: nowStr,
    } as CaseSDO;

    const got = await gql.createSDO({ schemaId, data });
    console.log('got =====>', got);
    expect(got).not.toBeNull();
  }, 50000);

  // this is a until to migrate data
  it.skip('migrate data', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = 'cfac46a8-f984-40a5-88ac-8f96a2022c0f';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const schemaId = '199afc28-bdc9-4439-b547-d8187e83ac4a';
    const sdos = await gql.getSDOs({ schemaId });
    expect(sdos).not.toBeNull();
    console.log(sdos.structuredDataObjects.count);
    for (const sdo of sdos.structuredDataObjects.records) {
      console.log(sdo);
      const id = sdo.id;
      const sdoData = sdo.data as {
        status?: { key?: string; label: string };
        preConfiguredTags?: string[];
        preconfiguredTags?: string[];
        statusId?: string;
        preconfiguredTagIds?: string[];
      };
      const data = {
        ...sdoData,
        statusId: sdoData.status?.key || sdoData.status || sdoData.statusId,
        preconfiguredTagIds:
          sdoData.preConfiguredTags ||
          sdoData.preconfiguredTags ||
          sdoData.preconfiguredTagIds,
      };
      // console.log('===>', data);
      try {
        const newSdo = await gql.updateSDO({ schemaId, id, data });
        console.log(newSdo);
      } catch (ex) {
        console.log(ex);
        console.log(data);
      }
    }
  }, 100000);
});
