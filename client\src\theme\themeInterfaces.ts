import {
  type Theme,
  type DeprecatedThemeOptions,
  type Palette,
  type PaletteOptions,
} from '@mui/material/styles';

export interface VeritonePalette extends Palette {
  button?: {
    containedPrimaryHoverBackground?: string;
    containedPrimaryBackgroundColor?: string;
    disabledColor?: string;
    disabledBackground?: string;
    outlineBackground?: string;
  };
}

export interface VeritonePaletteOptions extends PaletteOptions {
  button?: {
    containedPrimaryHoverBackground?: string;
    containedPrimaryBackgroundColor?: string;
    disabledColor?: string;
    disabledBackground?: string;
    outlineBackground?: string;
  };
}
export interface VeritoneTheme extends Theme {
  palette: VeritonePalette;
}

export interface VeritoneThemeOptions extends DeprecatedThemeOptions {
  palette: VeritonePaletteOptions;
}
