import './index.scss';
import { DeleteFiles, ListView, Move } from '@assets/icons';
import { I18nTranslate } from '@i18n';
import { FilterList as FilterListIcon, GridView } from '@mui/icons-material';
import { Box, IconButton, Paper } from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import {
  selectSelectedFiles,
  selectViewType,
  toggleOpenFilterDrawer,
  toggleViewType,
} from '@store/modules/caseDetail/slice';
import { selectCaseStatus } from '@store/modules/caseManager/slice';
import { dispatchCustomEvent } from '@utils/helpers';
import { ViewType } from '@utils/local-storage';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

interface CaseTableWrapperProps {
  caseSubtitle: string;
  children: React.ReactNode;
}

const CaseTableWrapper: React.FC<CaseTableWrapperProps> = ({
  caseSubtitle,
  children,
}) => {
  const dispatch = useAppDispatch();

  const handleFilterClick = () => {
    dispatch(toggleOpenFilterDrawer());
  };

  return (
    <Paper elevation={0} className="case-table-wrapper">
      <CaseHeader
        caseSubtitle={caseSubtitle}
        onFilterClick={handleFilterClick}
      />
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'auto',
        }}
      >
        <Box data-testid="file-table__wrapper" className="file-table-wrapper">
          {children}
        </Box>
      </Box>
    </Paper>
  );
};

interface CaseHeaderProps {
  caseSubtitle: string;
  onFilterClick: () => void;
}

const deleteAllSelectedFiles = () =>
  dispatchCustomEvent('delete-all-selected-files');

const moveAllSelectedFiles = () =>
  dispatchCustomEvent('move-all-selected-files');

const CaseHeader: React.FC<CaseHeaderProps> = ({
  caseSubtitle,
  onFilterClick,
}) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();

  const [enableBulkActions, setEnableBulkActions] = useState(false);
  const selectedFiles = useSelector(selectSelectedFiles);
  const selectedCaseStatus = useSelector(selectCaseStatus);
  const viewType = useSelector(selectViewType);

  const handleToggleViewType = () => {
    dispatch(toggleViewType());
  };

  const handleEnableBulkActions = (e: Event) => {
    const customEvent = e as CustomEvent<{ isEnabled: boolean }>;
    setEnableBulkActions(customEvent.detail.isEnabled);
  };

  useEffect(() => {
    addEventListener('enable-bulk-actions', handleEnableBulkActions);

    return () => {
      removeEventListener('enable-bulk-actions', handleEnableBulkActions);
    };
  }, []);

  return (
    <Box
      className="case-subtitle__container"
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        padding: '20px 30px',
      }}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '7px' }}>
        <div className="case-subtitle__content">
          {selectedCaseStatus === 'loading' ? (
            <div>{intl.formatMessage({ id: 'defaultEmpty' })}</div>
          ) : (
            <div className="case-subtitle__content-title">{caseSubtitle}</div>
          )}
          {!!selectedFiles.length && (
            <div className="case-subtitle__content-file-count">
              <strong>{selectedFiles.length}</strong>
              {intl.formatMessage({ id: 'space' })}
              {selectedFiles.length === 1
                ? intl.formatMessage({ id: 'file' })
                : intl.formatMessage({ id: 'files' })}
              {intl.formatMessage({ id: 'space' })}
              {intl.formatMessage({ id: 'selected' })}
            </div>
          )}
        </div>
        <span className="case-subtitle__header">
          {I18nTranslate.TranslateMessage('caseFiles')}
        </span>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
        {enableBulkActions && (
          <IconButton
            className="case-subtitle__button"
            data-testid="case-subtitle-move-button"
            size="small"
            onClick={moveAllSelectedFiles}
          >
            <Move />
          </IconButton>
        )}
        {enableBulkActions && (
          <IconButton
            className="case-subtitle__button"
            data-testid="case-subtitle-delete-button"
            size="small"
            onClick={deleteAllSelectedFiles}
          >
            <DeleteFiles />
          </IconButton>
        )}
        <IconButton
          onClick={handleToggleViewType}
          className="case-subtitle__button"
        >
          {viewType === ViewType.GRID ? (
            <ListView data-testid="list-view-icon" />
          ) : (
            <GridView data-testid="grid-view-icon" />
          )}
        </IconButton>
        <IconButton
          className="case-subtitle__button"
          data-testid="case-subtitle-filter-button"
          onClick={onFilterClick}
          size="small"
        >
          <FilterListIcon />
        </IconButton>
      </Box>
    </Box>
  );
};

export default CaseTableWrapper;
