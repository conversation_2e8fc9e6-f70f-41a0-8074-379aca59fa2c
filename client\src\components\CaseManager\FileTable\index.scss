.file-table__grid-view-header {
  height: 57px;
  display: flex;
  margin: 0 30px;
  padding: 0 16px;
  min-width: 700px;
  justify-content: space-between;
  border-bottom: 1.5px solid var(--border-color);
}

.file-table__blur-images-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-table__blur-label {
  font-size: 12px;
}

.file-table__card-view-content {
  gap: 15px;
  padding: 20px;
  display: flex;
  min-height: 315px;
  position: relative;
  height: calc(100% - 89px);
}

.file-table__table-cell {
  position: relative;
  width: 100%;

  &::before {
    content: '&nbsp;';
    visibility: hidden;
  }

  & > span {
    position: absolute;
    left: 0;
    right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.file-table__file-type {
  display: flex;
  align-items: center;
}

.status-chip {
  & > span {
    font-size: 11px;
    font-weight: bold;
    margin: 0 5px;
  }
}
