import { VFile } from '@shared-types/types';

const PENDING_FILE_KEY = 'investigate-pending-file';
const EXPIRATION_MS = 60 * 60 * 1000;

export interface PendingLSFile {
  value: VFile;
  expiry: number;
}

export type PendingFilesByFolderId = Record<string, PendingLSFile[]>;

const getStoredPendingFiles = (): PendingFilesByFolderId => {
  try {
    const localStorageStr = localStorage.getItem(PENDING_FILE_KEY);
    // TODO: We should be validating the structure of the data, not just parsing and assuming it's correct.
    return localStorageStr
      ? (JSON.parse(localStorageStr) as PendingFilesByFolderId)
      : {};
  } catch (error) {
    console.error('Failed to parse local storage data:', error);
    return {};
  }
};

const savePendingFiles = (data: PendingFilesByFolderId) => {
  localStorage.setItem(PENDING_FILE_KEY, JSON.stringify(data));
};

export const getPendingLSFiles = (folderId: string): PendingLSFile[] => {
  const now = Date.now();
  const parsedPendingFiles = getStoredPendingFiles();
  return (
    parsedPendingFiles[folderId]?.filter((item) => item.expiry > now) ?? []
  );
};

export const setPendingLSFile = ({
  folderId,
  id,
  fileName,
  fileType,
  createdTime,
}: {
  folderId: string;
  id: string;
  fileName: string;
  fileType: string;
  createdTime: string;
}) => {
  const now = Date.now();
  const parsedPendingFiles = getStoredPendingFiles();

  const validFiles = (parsedPendingFiles[folderId] ?? []).filter(
    (item) => item.expiry > now
  );

  const newPendingFile: PendingLSFile = {
    value: {
      id,
      fileName,
      fileType,
      parentTreeObjectIds: [],
      createdByName: '',
      createdTime,
      updatedTime: '',
      duration: -1,
    },
    expiry: now + EXPIRATION_MS,
  };

  savePendingFiles({
    ...parsedPendingFiles,
    [folderId]: [newPendingFile, ...validFiles],
  });
};

export const removePendingLSFiles = (ids: string[], folderId: string) => {
  const now = Date.now();
  const parsedPendingFiles = getStoredPendingFiles();

  if (!parsedPendingFiles[folderId]) {
    return;
  }

  const remainingFiles = parsedPendingFiles[folderId].filter(
    (item) => item.expiry > now && !ids.includes(item.value.id)
  );

  if (remainingFiles.length === 0) {
    // Remove empty folder entry
    delete parsedPendingFiles[folderId];
  } else {
    parsedPendingFiles[folderId] = remainingFiles;
  }

  savePendingFiles(parsedPendingFiles);
};
