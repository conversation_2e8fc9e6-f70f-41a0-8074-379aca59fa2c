.snackbar-noti {
  background-color: var(--button-inner);
  color: var(--background-tertiary);
  max-width: 400px;
  min-width: 300px;
  min-height: 50px;
  border-radius: 4px;
  padding: 14px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;

  &__longText {
    flex-direction: column;
  }

  .snackbar-noti-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    &__longText {
      width: 100%;
    }
  }

  .snackbar-noti-close {
    cursor: pointer;
    color: var(--background-tertiary);
    font-size: 25px;
    margin-left: 10px;
    padding-top: 7px;
  }

  .snackbar-noti-loading {
    color: var(--snackbar-action-text);
    margin-left: 40px;
    margin-right: 10px;
  }

  .snackbar-noti-action {
    background:none;
    border:none;
    margin-left: 40px;
    padding:0;
    cursor: pointer;
    color: var(--snackbar-action-text);
    text-wrap: nowrap;
  }
}
