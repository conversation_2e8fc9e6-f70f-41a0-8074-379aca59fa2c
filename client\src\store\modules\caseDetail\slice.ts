import { I18nTranslate } from '@i18n';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  CaseSearchResults,
  FILES_SORT_DIRECTION,
  FILES_SORT_FIELD,
  InvestigateCaseSDO,
  TemporalDataObject,
  VFile,
} from '@shared-types/types';
import { createAppSlice } from '@store/createAppSlice';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/index';
import { getCase } from '@store/modules/caseManager/getCase';
import { searchCases } from '@store/modules/caseManager/searchCases';
import { selectRootFolderId } from '@store/modules/caseManager/slice';
import { FileSearchParams, searchFiles } from '@store/modules/search/slice.ts';
import { ApiStatus } from '@store/types';
import { getApiAuthToken } from '@store/utils';
import { G<PERSON>Api, SortBy } from '@utils/helpers';
import {
  EDIT_FILE_KEY,
  getBlurValue,
  getLocalStorage,
  getPendingLSFiles,
  getViewTypeLocalStorage,
  PendingFilesByFolderId,
  removePendingLSFiles,
  setBlurValueLocalStorage,
  setLocalStorage,
  setPendingLSFile,
  setViewTypeLocalStorage,
  ViewType,
} from '@utils/local-storage';
import {
  getLocalStorageItemsForKey,
  PENDING_FILE_KEY,
  removeLocalStorageItems,
} from '@utils/saveToLocalStorage';
import { uniqBy } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { moveFile as moveFileFn } from './moveFile';
import { searchMedia } from './searchMedia';

export interface CaseDetailSliceState {
  folders: {
    status: ApiStatus;
    error?: string;
    data: CaseSearchResults['searchMedia']['jsondata'];
  };
  files: {
    status: ApiStatus;
    error?: string;
    data: {
      results: VFile[];
      totalResults: number;
      limit: number;
      from: number;
      to: number;
    };
  };
  selectedFileId: string;
  selectedFiles: string[];
  currentCaseId: string;
  showMoveFileDialog: boolean;
  moveFileDialogIsBulk: boolean;
  moveFileDialogBulkFileIds: string[];
  destinationCase: {
    id: string;
    status: ApiStatus;
    data?: InvestigateCaseSDO;
  };
  offset: number;
  limit: number;
  sortBy: FILES_SORT_FIELD;
  sortDirection: FILES_SORT_DIRECTION;
  openFilterDrawer: boolean;
  pendingFiles: PendingFilesByFolderId;
  blur: boolean;
  viewType: ViewType;
}

export const initialState: CaseDetailSliceState = {
  selectedFiles: [],
  folders: {
    status: 'idle',
    error: '',
    data: {
      results: [],
      totalResults: 0,
      limit: 0,
      from: 0,
      to: 0,
    },
  },
  files: {
    status: 'idle',
    error: '',
    data: {
      results: [],
      totalResults: 0,
      limit: 0,
      from: 0,
      to: 0,
    },
  },
  selectedFileId: '',
  currentCaseId: '',
  showMoveFileDialog: false,
  moveFileDialogIsBulk: false,
  moveFileDialogBulkFileIds: [],
  destinationCase: {
    id: '',
    status: 'loading',
    data: undefined,
  },
  offset: 0,
  limit: 50,
  sortBy: 'createdTime',
  sortDirection: 'desc',
  openFilterDrawer: false,
  pendingFiles: {},
  blur: getBlurValue(),
  viewType: getViewTypeLocalStorage(),
};

export const caseDetailSlice = createAppSlice({
  name: 'caseDetail',
  initialState,
  reducers: (create) => {
    const createThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();

    return {
      setSelectedFiles: create.reducer(
        (state, action: PayloadAction<string[]>) => {
          state.selectedFiles = action.payload;
        }
      ),
      toggleViewType: create.reducer((state) => {
        const currViewType = state.viewType;
        const viewType =
          currViewType === ViewType.LIST ? ViewType.GRID : ViewType.LIST;

        state.viewType = viewType;
        setViewTypeLocalStorage(viewType);
      }),
      toggleBlur: create.reducer((state) => {
        const newBlur = !state.blur;
        state.blur = newBlur;
        setBlurValueLocalStorage(newBlur);
      }),
      getFiles: createThunk(
        async (
          {
            fileName,
            folderId,
            dateFilter,
          }: {
            fileName?: string;
            folderId: string;
            dateFilter?: {
              startDate: string;
              endDate: string;
            };
            isPolling?: boolean;
          },
          thunkAPI
        ) => {
          const { getState, dispatch } = thunkAPI;

          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const rootFolderId = selectRootFolderId(state);

          const { sortBy, sortDirection, offset, limit } = state.caseDetail;

          const response = await searchMedia({
            fileName,
            folderId,
            dateFilter,
            sortBy,
            sortDirection,
            offset,
            limit,
            gql,
            rootFolderId,
          });

          const pendingMoveFiles = getLocalStorageItemsForKey<{
            fileId: string;
            oldFolderId: string;
            newFolderId: string;
          }>(PENDING_FILE_KEY).filter(
            (item) => item.value.newFolderId === folderId
          );
          const pendingMoveFileIds = pendingMoveFiles.map(
            (item) => item.value.fileId
          );

          // If pending files is returned in the search response, remove them in local storage
          // then re-fetch pending files from local storage
          const pendingFileIds = getPendingLSFiles(folderId).map(
            (item) => item.value.id
          );

          const readyFileIds = response.results
            .filter((file) => pendingFileIds.includes(file.id))
            .map((file) => file.id);

          const filesMoved = response.results
            .filter((file) => pendingMoveFileIds.includes(file.id))
            .map((file) => file.id);

          if (filesMoved.length > 0) {
            removeLocalStorageItems(PENDING_FILE_KEY, pendingMoveFiles);
          }

          if (readyFileIds.length > 0) {
            removePendingLSFiles(readyFileIds, folderId);
            dispatch(syncPendingFiles({}));
          }

          return response;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPolling) {
              state.files.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            // Not to update the edited file
            const editedTdoIds = getLocalStorage<string[]>(EDIT_FILE_KEY) || [];
            const currentEditedFiles = state.files.data.results.filter((file) =>
              editedTdoIds.includes(file.id)
            );
            const incomingFiles = action.payload.results.filter((file) =>
              editedTdoIds.includes(file.id)
            );
            state.files.data = action.payload;
            if (currentEditedFiles.length > 0 && incomingFiles.length > 0) {
              for (const file of currentEditedFiles) {
                for (let i = 0; i < state.files.data.results.length; i++) {
                  if (state.files.data.results[i].id === file.id) {
                    state.files.data.results[i] = file;
                  }
                }
              }
            }
            state.files.status = 'complete';
          },
          rejected: (state) => {
            state.files.status = 'failure';
          },
        }
      ),
      updateFile: create.reducer(
        (
          state,
          action: PayloadAction<{
            tdoId: string;
            fileName: string;
            description: string;
          }>
        ) => {
          const { tdoId, fileName, description } = action.payload;
          for (const file of state.files.data.results) {
            if (file.id === tdoId) {
              file.fileName = fileName;
              file.description = description;
            }
          }
          // Add the id of the updated file to local storage
          const editedTdoIds = getLocalStorage<string[]>(EDIT_FILE_KEY) || [];
          setLocalStorage(EDIT_FILE_KEY, [...editedTdoIds, tdoId]);
        }
      ),
      syncPendingFiles: createThunk(
        async ({ tdoId }: { tdoId?: string }, thunkAPI) => {
          let tdoDataDetail: TemporalDataObject;
          if (tdoId) {
            const state = thunkAPI.getState() as RootState;
            const token = getApiAuthToken(state);
            if (!token) {
              throw new Error('no auth token');
            }
            const config = state.config;
            const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
            const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
            tdoDataDetail = await gql.getTdoDetails({ tdoId });
            return tdoDataDetail;
          }
        },
        {
          fulfilled: (state, action) => {
            const tdoDataDetail = action.payload;
            if (tdoDataDetail) {
              setPendingLSFile({
                folderId: tdoDataDetail.folders[0].id,
                id: tdoDataDetail.id,
                createdTime: tdoDataDetail.createdDateTime ?? '',
                fileName: tdoDataDetail.details?.veritoneFile?.fileName ?? '',
                fileType: tdoDataDetail.details?.veritoneFile?.fileType ?? '',
              });

              const folderId = tdoDataDetail.folders[0].id;
              const pendingFiles = getPendingLSFiles(folderId);
              state.pendingFiles[folderId] = pendingFiles;
            }
          },
        }
      ),
      searchFolders: createThunk(
        async (
          { offset = 0, limit = 30 }: { offset: number; limit: number },
          thunkAPI
        ) => {
          const { getState } = thunkAPI;

          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const folderContentTemplateSchemaId =
            state.caseManager.folderContentTemplateSchema.id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }

          const response = await searchCases({
            limit,
            offset,
            folderContentTemplateSchemaId,
            sortBy: SortBy.CaseId,
            sortDirection: 'asc',
            gql,
          });

          return response;
        },
        {
          pending: (state) => {
            state.folders.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.folders.status = 'complete';
            // Dedupe by folderId (or your unique key)
            const allResults = state.folders.data.results.concat(
              action.payload.results
            );
            const dedupedResults = uniqBy(allResults, 'folderId');

            state.folders.data = {
              ...action.payload,
              to:
                state.folders.data.to > action.payload.to
                  ? state.folders.data.to
                  : action.payload.to,
              results: dedupedResults,
              from: 0,
            };
          },
          rejected: (state) => {
            state.folders.status = 'failure';
          },
        }
      ),
      getDestinationCase: createThunk(
        async (folderId: string, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const caseData = await getCase(folderId, gql);

          return caseData;
        },
        {
          pending: (state) => {
            state.destinationCase.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.destinationCase.data = action.payload;
            state.destinationCase.status = 'complete';
            if (action.payload.folderId) {
              // Update the currentCaseId with the new destination folderId
              state.currentCaseId = action.payload.folderId;
            }
          },
          rejected: (state) => {
            state.destinationCase.status = 'failure';
          },
        }
      ),
      moveFile: createThunk(
        async (
          {
            fileId,
            oldFolderId,
            newFolderId,
          }: {
            fileId: string;
            oldFolderId: string;
            newFolderId: string;
          },
          thunkAPI
        ) => {
          const { getState, dispatch } = thunkAPI;

          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const rootFolderId = selectRootFolderId(state);

          const response = await moveFileFn({
            rootFolderId,
            fileId,
            oldFolderId,
            newFolderId,
            gql,
          });

          dispatch(getDestinationCase(newFolderId));

          return response;
        },
        {
          pending: (state) => {
            state.files.status = 'loading';
          },
          fulfilled: (state) => {
            state.files.status = 'complete';
            enqueueSnackbar(I18nTranslate.TranslateMessage('fileMovedSuccess'));
          },
          rejected: (state) => {
            state.files.status = 'failure';
          },
        }
      ),
      pollFiles: createThunk((fileSearchParams: FileSearchParams, thunkAPI) => {
        const { signal, dispatch, getState } = thunkAPI;
        const state = getState() as RootState;
        const pollingMillis = state.config.filePollingInterval ?? 12000;

        let dispatchPromise: DispatchPromise;

        const pollInterval = setInterval(() => {
          dispatchPromise = dispatch(searchFilesPoll(fileSearchParams));
        }, pollingMillis);

        signal.addEventListener('abort', () => {
          clearInterval(pollInterval);
          dispatchPromise?.abort();
        });
      }),
      searchFilesPoll: createThunk(
        (fileSearchParams: FileSearchParams, thunkAPI) => {
          // This thunk is used to poll files with updated search params
          const { dispatch, getState } = thunkAPI;
          const state = getState() as RootState;

          let updatedSearchParams;

          if (!state.search.searchFiles.searchParams) {
            updatedSearchParams = fileSearchParams;
          } else {
            updatedSearchParams = {
              ...state.search.searchFiles.searchParams,
              folderIdsFilter: fileSearchParams.folderIdsFilter,
            };
          }

          dispatch(
            searchFiles({
              params: updatedSearchParams,
              isPolling: true,
              isCaseDetails: true,
            })
          );
        }
      ),
      setCurrentCaseId: create.reducer(
        (state, action: PayloadAction<string>) => {
          if (state.currentCaseId !== action.payload) {
            state.files.data = {
              results: [],
              totalResults: 0,
              limit: 0,
              from: 0,
              to: 0,
            };
            state.currentCaseId = action.payload;
          }
        }
      ),
      setSelectedFileId: create.reducer(
        (state, action: PayloadAction<string>) => {
          state.selectedFileId = action.payload;
        }
      ),
      toggleMoveFileDialog: create.reducer(
        (
          state,
          action: PayloadAction<
            | { isBulk?: boolean; moveFileDialogBulkFileIds: string[] }
            | undefined
          >
        ) => {
          state.showMoveFileDialog = !state.showMoveFileDialog;
          state.moveFileDialogIsBulk = !!action.payload?.isBulk;
          state.moveFileDialogBulkFileIds =
            action.payload?.moveFileDialogBulkFileIds || [];
        }
      ),
      setDestinationCase: create.reducer(
        (state, action: PayloadAction<InvestigateCaseSDO | undefined>) => {
          state.destinationCase.data = action.payload;
          state.destinationCase.status = 'idle';
        }
      ),
      setOffset: create.reducer((state, action: PayloadAction<number>) => {
        state.offset = action.payload;
      }),
      setLimit: create.reducer((state, action: PayloadAction<number>) => {
        state.limit = action.payload;
      }),
      setSortBy: create.reducer(
        (state, action: PayloadAction<FILES_SORT_FIELD>) => {
          state.sortBy = action.payload;
        }
      ),
      setSortDirection: create.reducer(
        (state, action: PayloadAction<FILES_SORT_DIRECTION>) => {
          state.sortDirection = action.payload;
        }
      ),
      toggleOpenFilterDrawer: create.reducer((state) => {
        state.openFilterDrawer = !state.openFilterDrawer;
      }),
    };
  },
  selectors: {
    selectSelectedFiles: (state) => state.selectedFiles,
    selectFiles: (state) => state.files,
    selectFolders: (state) => state.folders,
    selectCurrentCaseId: (state) => state.currentCaseId,
    selectSelectedFileId: (state) => state.selectedFileId,
    selectShowMoveFileDialog: (state) => state.showMoveFileDialog,
    selectDestinationCase: (state) => state.destinationCase,
    selectOffset: (state) => state.offset,
    selectLimit: (state) => state.limit,
    selectSortBy: (state) => state.sortBy,
    selectSortDirection: (state) => state.sortDirection,
    selectOpenFilterDrawer: (state) => state.openFilterDrawer,
    selectPendingFiles: (state) => state.pendingFiles,
    selectBlur: (state) => state.blur,
    selectViewType: (state) => state.viewType,
    selectMoveFileDialogIsBulk: (state) => state.moveFileDialogIsBulk,
    selectMoveFileDialogBulkFileIds: (state) => state.moveFileDialogBulkFileIds,
  },
});

export const {
  setSelectedFiles,
  getFiles,
  updateFile,
  syncPendingFiles,
  searchFolders,
  getDestinationCase,
  moveFile,
  setCurrentCaseId,
  setSelectedFileId,
  toggleMoveFileDialog,
  setDestinationCase,
  pollFiles,
  setOffset,
  setLimit,
  setSortBy,
  setSortDirection,
  toggleOpenFilterDrawer,
  toggleBlur,
  toggleViewType,
  searchFilesPoll,
} = caseDetailSlice.actions;

export const {
  selectSelectedFiles,
  selectFiles,
  selectFolders,
  selectCurrentCaseId,
  selectSelectedFileId,
  selectShowMoveFileDialog,
  selectDestinationCase,
  selectOffset,
  selectLimit,
  selectSortBy,
  selectSortDirection,
  selectOpenFilterDrawer,
  selectPendingFiles,
  selectBlur,
  selectViewType,
  selectMoveFileDialogIsBulk,
  selectMoveFileDialogBulkFileIds,
} = caseDetailSlice.selectors;
