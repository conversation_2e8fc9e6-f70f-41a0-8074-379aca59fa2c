import { GQLApi } from '@utils/helpers';

export async function moveFile({
  rootFolderId,
  fileId,
  oldFolderId,
  newFolderId,
  gql,
}: {
  rootFolderId?: string;
  fileId: string;
  oldFolderId: string;
  newFolderId: string;
  gql: GQLApi;
}) {
  if (!rootFolderId) {
    throw new Error('no root folder');
  }

  const response = await gql.moveFile({
    fileId,
    oldFolderId,
    newFolderId,
  });

  return response;
}
