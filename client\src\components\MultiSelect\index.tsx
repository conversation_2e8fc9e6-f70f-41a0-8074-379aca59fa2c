import './index.scss';
import { I18nTranslate } from '@i18n';
import { Check as CheckIcon, Search as SearchIcon } from '@mui/icons-material';
import {
  FormControl,
  InputAdornment,
  InputLabel,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { selectOpenFilterDrawer } from '@store/modules/caseDetail/slice';
import cn from 'classnames';
import { useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useSelector } from 'react-redux';

interface Option {
  id: string;
  label: string;
}

interface Props<T> {
  options: T[];
  currentIds: string[];
  name: string;
  isDisabled?: boolean;
  isFullWidth?: boolean;
}

const CASE_TAGS_LIMIT = 10;

const MultiSelect = <T extends Option>({
  options,
  currentIds,
  isDisabled,
  isFullWidth,
  name,
}: Props<T>) => {
  const [searchText, setSearchText] = useState('');
  const [selectedIds, setSelectedIds] = useState<string[]>(currentIds);

  const isCaseDetailFilter = useSelector(selectOpenFilterDrawer);

  const {
    setValue,
    formState: { dirtyFields },
  } = useFormContext();

  const isFilterCaseTags = name === 'filterCaseTagIds';
  const selectedIdSet = new Set(selectedIds);

  const filteredOptions = useMemo(() => {
    const optionList = !isCaseDetailFilter
      ? options
      : options.filter((option) => selectedIdSet.has(option.id));

    if (!searchText) {
      return optionList;
    }

    return optionList.filter((option) =>
      option.label.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [options, searchText, isCaseDetailFilter]);

  const handleAddOrRemoveTag = (caseTagIdsToAdd: string[]) => {
    if (!isFilterCaseTags && caseTagIdsToAdd.length > CASE_TAGS_LIMIT) {
      return;
    }
    const newCaseTagIds = caseTagIdsToAdd.splice(0);
    setSelectedIds(newCaseTagIds);
    setValue(name, newCaseTagIds, { shouldDirty: true });
  };

  const handleRenderValue = () => (
    <div
      className={cn('multi-select__rendered-label', {
        'multi-select__disabled': isCaseDetailFilter,
      })}
    >
      <div
        data-testid={'multi-select-number-selected'}
        className={cn('multi-select__selected-number', {
          'multi-select__filter-selected-number': isFilterCaseTags,
          haschanges: !!dirtyFields[name],
        })}
      >
        {selectedIdSet.size}
      </div>
      <div
        className={cn('multi-select__selected-text', {
          'multi-select__filter-selected-text': isFilterCaseTags,
        })}
      >
        {I18nTranslate.TranslateMessage('tagsSelected')}
      </div>
    </div>
  );

  return (
    <FormControl fullWidth data-testid="multi-select-list">
      <InputLabel shrink={false} id="search-select-label" />
      <Select
        fullWidth={isFullWidth}
        disabled={isDisabled}
        multiple
        // Disables autofocus on MenuItems and allows TextField to be in focus
        MenuProps={{
          autoFocus: false,
          sx: {
            '&& .Mui-selected': {
              backgroundColor: 'var(--background-green-light)',
            },
          },
          PaperProps: {
            style: {
              maxHeight: 400,
            },
          },
          className: cn({
            'multi-select__filter-menu': isFilterCaseTags,
          }),
        }}
        displayEmpty
        labelId="search-select-label"
        id="multi-select-search-select"
        data-testid={'multi-select-search-select'}
        value={selectedIds}
        onChange={(e) => {
          if (!isCaseDetailFilter && Array.isArray(e.target.value)) {
            handleAddOrRemoveTag(e.target.value);
          }
        }}
        onClose={() => setSearchText('')}
        className={cn({
          'multi-select__filter-select': isFilterCaseTags,
          'multi-select__normal-width': !isFilterCaseTags,
        })}
        renderValue={handleRenderValue}
      >
        {/* TextField is put into ListSubheader so that it doesn't
              act as a selectable item in the menu
              i.e. we can click the TextField without triggering any selection.*/}
        {!isCaseDetailFilter && (
          <ListSubheader
            className={cn('multi-select__search-header', {
              'multi-select__filter-search-header': isFilterCaseTags,
            })}
          >
            <TextField
              size="small"
              // Autofocus on textfield
              autoFocus
              fullWidth
              slotProps={{
                htmlInput: {
                  'data-testid': 'multi-select-search-input',
                },
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        fontSize={isFilterCaseTags ? 'small' : 'medium'}
                      />
                    </InputAdornment>
                  ),
                  className: cn('multi-select__search-input', {
                    'multi-select__filter-search-input': isFilterCaseTags,
                  }),
                },
              }}
              onChange={(e) => setSearchText(e.target.value)}
              onKeyDown={(e) => {
                if (e.key !== 'Escape') {
                  // Prevents auto-selecting item while typing (default Select behavior)
                  e.stopPropagation();
                }
              }}
            />
          </ListSubheader>
        )}
        {filteredOptions.map(({ id, label }) => {
          const isSelected = selectedIdSet.has(id);

          return (
            <MenuItem
              value={id}
              key={`option-${id}`}
              data-testid={`multi-select-search-option-${label}`}
              disabled={isCaseDetailFilter}
            >
              {isSelected && (
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <CheckIcon />
                </ListItemIcon>
              )}
              <ListItemText
                sx={{ pl: isSelected ? 0 : '47px' }}
                primary={label}
                slotProps={{
                  primary: { fontSize: isFilterCaseTags ? '12px' : '14px' },
                }}
              />
            </MenuItem>
          );
        })}
      </Select>
    </FormControl>
  );
};

export default MultiSelect;
