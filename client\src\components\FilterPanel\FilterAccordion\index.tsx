import './index.scss';
import { Add as AddIcon, Remove as RemoveIcon } from '@mui/icons-material';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from '@mui/material';
import { useState } from 'react';

interface Props {
  defaultExpanded?: boolean;
  children?: React.ReactNode;
  title: string;
}

const FilterAccordion = ({ defaultExpanded, children, title }: Props) => {
  const [expand, setExpand] = useState<boolean>(!!defaultExpanded);

  const titleName = `filter-accordion-${title.toLowerCase().replace(/\s+/g, '-')}`;

  return (
    <Accordion
      className="search-page__accordion"
      defaultExpanded={defaultExpanded}
      data-testid={titleName}
    >
      <AccordionSummary
        onClick={() => setExpand(!expand)}
        data-testid={`${titleName}-header`}
      >
        <div className="search-page__accordion-title">
          <Typography component="span">{title}</Typography>
          {expand ? <RemoveIcon /> : <AddIcon />}
        </div>
      </AccordionSummary>
      <AccordionDetails data-testid={`${titleName}-content`}>
        <div className="search-page__accordion-details">{children}</div>
      </AccordionDetails>
    </Accordion>
  );
};

export default FilterAccordion;
