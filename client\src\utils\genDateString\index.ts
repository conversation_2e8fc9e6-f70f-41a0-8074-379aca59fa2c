export const generateDateString = (
  date: Date = new Date(),
  isUTC?: boolean
) => {
  const day = (!isUTC ? date.getUTCDate() : date.getDate())
    .toString()
    .padStart(2, '0');
  const month = (!isUTC ? date.getUTCMonth() + 1 : date.getMonth() + 1)
    .toString()
    .padStart(2, '0');
  const year = !isUTC ? date.getUTCFullYear() : date.getFullYear();

  return new Date(`${month}/${day}/${year}`).toISOString();
};
