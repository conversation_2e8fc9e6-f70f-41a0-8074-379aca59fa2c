import { styled } from '@mui/material/styles';
import Switch, { SwitchProps } from '@mui/material/Switch';

export const BlurSwitch = styled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 42,
  height: 26,
  padding: 0,
  '& .Sdk-MuiSwitch-switchBase': {
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&.Mui-checked': {
      transform: 'translateX(16px)',
      color: '#fff',
      '& + .Sdk-MuiSwitch-track': {
        backgroundColor: '#054FA3',
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
  },
  '& .Sdk-MuiSwitch-thumb': {
    boxSizing: 'border-box',
    backgroundColor: '#EFEFEF',
    width: 22,
    height: 22,
  },
  '& .Sdk-MuiSvgIcon-root': {
    boxSizing: 'border-box',
    backgroundColor: '#EFEFEF',
    padding: 1,
    borderRadius: '50%',
    width: 22,
    height: 22,
  },
  '& .Sdk-MuiSwitch-track': {
    borderRadius: 26 / 2,
    backgroundColor: '#BBBBBB',
    opacity: 1,
    transition: theme.transitions.create(['background-color'], {
      duration: 500,
    }),
    ...theme.applyStyles('dark', {
      backgroundColor: '#39393D',
    }),
  },
}));

export const CategorySwitch = styled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 40,
  height: 22,
  padding: 0,
  '& .Sdk-MuiSwitch-switchBase': {
    width: 60,
    height: '100%',
    top: -2,
    left: -21,
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&:hover': {
      backgroundColor: 'transparent',
    },
    '&.Mui-checked': {
      transform: 'translateX(18px)',
      color: '#fff',
      '&:hover': {
        backgroundColor: 'transparent',
      },
      '& + .Sdk-MuiSwitch-track': {
        backgroundColor: '#054FA3',
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
  },
  '& .Sdk-MuiButton-root': {
    boxSizing: 'border-box',
    backgroundColor: 'var(--background-secondary)',
    borderRadius: '50%',
    minWidth: 18,
    width: 18,
    height: 18,

    '&>svg': {
      width: 14,
      height: 14,
    },
  },
  '& .Sdk-MuiSwitch-track': {
    borderRadius: 22 / 2,
    backgroundColor: '#BBBBBB',
    opacity: 1,
    transition: theme.transitions.create(['background-color'], {
      duration: 500,
    }),
    ...theme.applyStyles('dark', {
      backgroundColor: '#39393D',
    }),
  },
}));
