import './index.scss';
import { DAGIcon } from '@assets/icons';
import { CategorySwitch } from '@components/Search/Switch';
import { I18nTranslate } from '@i18n';
import CheckIcon from '@mui/icons-material/Check';
import { Button, Popover } from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import {
  selectCategories,
  selectHideIfNoResults,
  sortCategories,
  toggleHideIfNoResults,
} from '@store/modules/search/slice';
import { Category, SearchView } from '@utils/local-storage';
import classNames from 'classnames';
import {
  MaterialReactTable,
  MRT_Column,
  MRT_ColumnDef,
  MRT_Row,
} from 'material-react-table';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

interface Props {
  open: boolean;
  onClose: () => void;
  anchorEl: HTMLElement | null;
  searchView: SearchView;
}

const CategoryMenu = ({ open, onClose, anchorEl, searchView }: Props) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();

  const categories = useSelector(selectCategories);
  const hideIfNoResults = useSelector(selectHideIfNoResults);
  const categoryRows: Category[] = [
    {
      id: 'none',
      category: '',
      color: 'transparent',
    },
    ...categories,
    {
      id: 'none',
      category: '',
      color: 'transparent',
    },
  ];

  const [draggingRow, setDraggingRow] = useState<MRT_Row<Category> | null>(
    null
  );
  const [hoveredRow, setHoveredRow] = useState<Partial<
    MRT_Row<Category>
  > | null>(null);
  const [rowSelection, setRowSelection] = useState<{ [key: string]: boolean }>(
    categories.reduce(
      (acc, category) => ({
        ...acc,
        [category.id]: category.checked ?? false,
      }),
      {}
    )
  );

  useEffect(() => {
    const selection: { [key: string]: boolean } = {};
    let hasChecked = false;
    for (const row of categoryRows) {
      if (row.checked && !rowSelection[row.id]) {
        selection[row.id] = true;
        hasChecked = true;
      }
    }

    if (hasChecked) {
      setRowSelection(selection);
    }
  }, [categories]);

  useEffect(() => {
    const updatedRows = categoryRows.map((row) => {
      if (rowSelection[row.id]) {
        return { ...row, checked: true };
      } else {
        return { ...row, checked: false };
      }
    });
    dispatch(sortCategories(cleanCategories(updatedRows)));
  }, [rowSelection]);

  const columns: MRT_ColumnDef<Category>[] = [
    {
      accessorKey: 'category',
      header: intl.formatMessage({ id: 'showAllCategories' }),
      muiTableHeadCellProps: {
        className: 'category-menu__category-cell cell-header',
      },
      muiTableBodyCellProps: {
        className: 'category-menu__category-cell',
      },
      Cell: ({ renderedCellValue }) =>
        renderedCellValue &&
        intl.formatMessage({ id: renderedCellValue as string }),
    },
  ];

  const renderCellClassName = ({
    column,
    isHeaderCell,
  }: {
    column: MRT_Column<Category, unknown>;
    isHeaderCell?: boolean;
  }) => {
    let className: string | undefined;

    if (column.id === 'mrt-row-drag') {
      className = 'category-menu__drag-cell';
    } else if (column.id === 'mrt-row-select') {
      className = 'category-menu__checkbox-cell';
    }

    if (isHeaderCell) {
      className = `${className} cell-header`;
    }

    return className;
  };

  const cleanCategories = (categories: Category[]) =>
    categories.filter((category) => category.id !== 'none');

  const handleDragRow = () => {
    if (hoveredRow && draggingRow) {
      categoryRows.splice(
        hoveredRow?.index ?? 0,
        0,
        categoryRows.splice(draggingRow.index, 1)[0]
      );
    }
    dispatch(sortCategories(cleanCategories(categoryRows)));
  };

  return (
    <Popover
      open={open}
      onClose={onClose}
      anchorEl={anchorEl}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      className="category-menu"
    >
      <div className="category-menu__table" data-testid="category-menu-table">
        <MaterialReactTable
          data={categoryRows}
          columns={columns}
          state={{
            columnOrder: ['mrt-row-drag', 'mrt-row-select', 'category'],
            draggingRow,
            hoveredRow,
            rowSelection,
          }}
          getRowId={(row) => row.id}
          onRowSelectionChange={setRowSelection}
          muiRowDragHandleProps={{ onDragEnd: handleDragRow }}
          onDraggingRowChange={setDraggingRow}
          onHoveredRowChange={setHoveredRow}
          enableRowOrdering={searchView === SearchView.Grouped}
          enableRowSelection
          enablePagination={false}
          enableSorting={false}
          enableTopToolbar={false}
          enableBottomToolbar={false}
          enableColumnActions={false}
          displayColumnDefOptions={{
            'mrt-row-drag': {
              header: '',
              size: 20,
            },
            'mrt-row-select': {
              size: 20,
            },
          }}
          muiTablePaperProps={{
            className: 'category-menu__table-paper',
          }}
          muiTableHeadRowProps={{
            className: classNames('category-menu__table-header', {
              'no-row-ordering': searchView !== SearchView.Grouped,
            }),
          }}
          muiTableBodyRowProps={({ row }) => ({
            className: classNames('category-menu__table-row', {
              'no-row-ordering': searchView !== SearchView.Grouped,
            }),
            sx: {
              backgroundColor: row.original.color,
              '&.Mui-selected': { backgroundColor: row.original.color },
              '&:hover': {
                '&.Mui-selected': { backgroundColor: row.original.color },
              },
            },
          })}
          muiTableBodyCellProps={({ column, row }) => ({
            className: renderCellClassName({ column }),
            sx: {
              '&.Sdk-MuiTableCell-root': {
                '&::after': {
                  backgroundColor: row.original.color,
                },
              },
            },
          })}
          muiTableHeadCellProps={({ column }) => ({
            className: renderCellClassName({ column, isHeaderCell: true }),
          })}
          icons={{ DragHandleIcon: () => <DAGIcon /> }}
        />
      </div>
      {searchView === SearchView.Grouped && (
        <div
          className="category-menu__action"
          data-testid="category-menu-action"
        >
          <CategorySwitch
            icon={
              <Button>
                <CheckIcon />
              </Button>
            }
            checkedIcon={
              <Button>
                <CheckIcon />
              </Button>
            }
            checked={hideIfNoResults}
            onChange={() => dispatch(toggleHideIfNoResults())}
            inputProps={
              {
                'data-testid': 'hide-if-no-results',
              } as React.InputHTMLAttributes<HTMLInputElement>
            }
          />
          <span>{intl.formatMessage({ id: 'hideIfNoResults' })}</span>
        </div>
      )}
    </Popover>
  );
};

export default CategoryMenu;
