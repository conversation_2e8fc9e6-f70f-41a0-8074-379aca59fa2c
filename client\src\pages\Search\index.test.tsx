import { TdoDescriptions } from '@shared-types/types';
import { configureAppStore } from '@store/index';
import * as searchFilesExports from '@store/modules/search/searchFiles';
import { SearchMediaResponse } from '@store/modules/search/searchFiles';
import {
  changeSearchView,
  initialState,
  SearchSliceState,
  setFileSearchResult,
} from '@store/modules/search/slice';
import {
  act,
  fireEvent,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GQLApi } from '@utils/helpers';
import { SearchView } from '@utils/local-storage';
import { ViewType } from '@utils/local-storage/viewTypes';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router';
import { describe, beforeAll, expect, it, vi } from 'vitest';
import Search from '.';
import { render } from '../../../test/render';
import { GQLResponse } from '@utils/helpers/gqlApi/baseGraphQLApi.ts';

vi.mock(import('notistack'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useSnackbar: () => ({
      enqueueSnackbar: vi.fn(),
      closeSnackbar: vi.fn(),
    }),
    enqueueSnackbar: vi.fn(),
  };
});

const getCheckbox = (dataTestId: string) =>
  within(screen.getByTestId(dataTestId)).getByRole<HTMLInputElement>(
    'checkbox'
  );

const page1results: SearchMediaResponse = {
  searchMedia: {
    jsondata: {
      results: [
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '**********',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/**********',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '**********',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0, // Added missing property
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '22222222',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '22222222',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '33333333',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '33333333',
                mimetype: 'audio/mp4',
                segmented: true,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '444444444',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '444444444',
                mimetype: 'audio/mp4',
                segmented: true,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '555555555',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '555555555',
                mimetype: 'audio/mp4',
                segmented: true,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '66666666',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '66666666',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
      ],
      totalResults: {
        value: 100,
        relation: '',
      },
      limit: 50,
      from: 0,
      to: 50,
      searchToken: '',
      timestamp: 0,
    },
  },
};

const appStoreState: {
  search: SearchSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  search: {
    ...initialState,
    searchFiles: {
      ...initialState.searchFiles,
      searchParams: {
        searchResultType: 'ungrouped',
        checkedResultCategories: [],
        pagination: {
          ungrouped: {
            offset: 0,
            limit: 50,
          },
        },
      },
      ungroupedSearch: {
        status: 'success',
        data: page1results,
        pagination: {
          offset: 0,
          limit: 50,
        },
      },
    },
    viewType: ViewType.LIST,
    searchView: SearchView.UnGrouped,
    categories: [
      {
        id: 'filename',
        category: 'filename',
        color: 'var(--background-filename)',
        checked: false,
      },
      {
        id: 'transcription',
        category: 'transcription',
        color: 'var(--background-transcription)',
        checked: true,
      },
      {
        id: 'face-recognition',
        category: 'faceRecognition',
        color: 'var(--background-face-recognition)',
        checked: false,
      },
      {
        id: 'object-detection',
        category: 'objectDetection',
        color: 'var(--background-object-detection)',
        checked: true,
      },
    ],
    hideIfNoResults: false,
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

const page2Results: SearchMediaResponse = {
  searchMedia: {
    jsondata: {
      results: [
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '7777777777',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/7777777777',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            parentTreeObjectIds: [],
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '7777777777',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '888888888',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/888888888',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            parentTreeObjectIds: [],
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '888888888',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '9999999999',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/9999999999',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            parentTreeObjectIds: [],
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '444444444',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '100000000',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/100000000',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            parentTreeObjectIds: [],
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '100000000',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '1200000000',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/1200000000',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            parentTreeObjectIds: [],
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '1200000000',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '130000000',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/130000000',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            parentTreeObjectIds: [],
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '130000000',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
      ],
      totalResults: {
        value: 100,
        relation: '',
      },
      limit: 50,
      from: 50,
      to: 100,
      searchToken: '',
      timestamp: 0,
    },
  },
};

const mockTdoDescriptions: GQLResponse<TdoDescriptions> = {
  data: {
    temporalDataObjects: {
      count: 1,
      limit: 10,
      offset: 0,
      records: [
        {
          id: '**********',
          description: '',
        },
      ],
    },
  },
};

beforeAll(() => {
  window.HTMLElement.prototype.scrollIntoView = function () {};
});

describe('Search page', () => {
  vi.mock('@tanstack/react-virtual', () => ({
    useVirtualizer: vi.fn(() => ({
      getVirtualItems: () => [
        {
          index: 0,
          size: 69,
          start: 0,
          end: 69,
          key: 0,
          measureElement: vi.fn(),
        },
        {
          index: 1,
          size: 69,
          start: 69,
          end: 138,
          key: 1,
          measureElement: vi.fn(),
        },
        {
          index: 2,
          size: 69,
          start: 138,
          end: 207,
          key: 2,
          measureElement: vi.fn(),
        },
        {
          index: 3,
          size: 69,
          start: 207,
          end: 276,
          key: 3,
          measureElement: vi.fn(),
        },
        {
          index: 4,
          size: 69,
          start: 276,
          end: 345,
          key: 4,
          measureElement: vi.fn(),
        },
        {
          index: 5,
          size: 69,
          start: 345,
          end: 414,
          key: 5,
          measureElement: vi.fn(),
        },
      ],
      getTotalSize: () => 414,
      measure: vi.fn(),
      scrollToIndex: vi.fn(),
    })),
  }));

  it('should handle checkbox correctly', async () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    const firstRowCheckbox = () =>
      getCheckbox('check-box__check-row-**********');
    const secondRowCheckbox = () =>
      getCheckbox('check-box__check-row-22222222');
    const checkAll = () => getCheckbox('check-box__check-all');

    expect(checkAll().checked).toBe(false);
    expect(firstRowCheckbox().checked).toBe(false);
    expect(secondRowCheckbox().checked).toBe(false);

    fireEvent.click(checkAll());

    await waitFor(() => {
      expect(checkAll().checked).toBe(true);
      expect(firstRowCheckbox().checked).toBe(true);
      expect(secondRowCheckbox().checked).toBe(true);
    });
  });

  it('should toggle checkbox when ctrl click on row', () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    const firstRowCheckbox = () =>
      getCheckbox('check-box__check-row-**********');

    expect(firstRowCheckbox().checked).toBe(false);

    const firstRow = screen.getByTestId('table-row-**********');
    fireEvent.click(firstRow, { ctrlKey: true });
    expect(firstRowCheckbox().checked).toBe(true);
  });

  it('should keep show number of selected files', () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.queryByTestId('file-count')).not.toBeInTheDocument();

    const firstRowCheckbox = () =>
      getCheckbox('check-box__check-row-**********');
    const secondRowCheckbox = () =>
      getCheckbox('check-box__check-row-22222222');
    expect(firstRowCheckbox().checked).toBe(false);
    expect(secondRowCheckbox().checked).toBe(false);

    fireEvent.click(firstRowCheckbox());
    expect(firstRowCheckbox().checked).toBe(true);
    const singlefileCount = screen.getByText(/file selected/i);
    expect(within(singlefileCount).getByText(/1/i)).toBeInTheDocument();

    fireEvent.click(secondRowCheckbox());
    expect(firstRowCheckbox().checked).toBe(true);
    expect(secondRowCheckbox().checked).toBe(true);
    const multifileCount = screen.getByText(/files selected/i);
    expect(within(multifileCount).getByText(/2/i)).toBeInTheDocument();
  });

  it('should keep checked when back to the page', async () => {
    const store = configureAppStore(appStoreState);

    vi.spyOn(GQLApi.prototype, 'getTdoDescriptions').mockImplementation(() =>
      Promise.resolve(mockTdoDescriptions)
    );

    render(
      <div data-testid="search-test">
        <Provider store={store}>
          <MemoryRouter>
            <Search />
          </MemoryRouter>
        </Provider>
      </div>
    );

    await waitFor(() => {
      getCheckbox('check-box__check-row-**********');
    });

    const firstRowCheckbox = () =>
      getCheckbox('check-box__check-row-**********');
    expect(firstRowCheckbox().checked).toBe(false);

    fireEvent.click(firstRowCheckbox());
    expect(firstRowCheckbox().checked).toBe(true);

    const nextPageButton = () =>
      screen.getByRole('button', {
        name: /go to next page/i,
      });

    const prePageButton = () =>
      screen.getByRole('button', {
        name: /go to previous page/i,
      });

    store.dispatch(
      setFileSearchResult({
        pagination: {
          ungrouped: {
            offset: 50,
            limit: 50,
          },
        },
        data: page2Results,
        params: {
          keywordSearchQuery: 'test',
          searchResultType: 'ungrouped',
          checkedResultCategories: [],
          pagination: {
            ungrouped: {
              offset: 50,
              limit: 50,
            },
          },
        },
        status: 'success',
        isGrouped: false,
      })
    );

    fireEvent.click(nextPageButton());

    await waitFor(
      () => {
        expect(
          screen.queryByTestId(`check-box__check-row-**********`)
        ).not.toBeInTheDocument();
      },
      { timeout: 2500 }
    );

    store.dispatch(
      setFileSearchResult({
        pagination: {
          ungrouped: {
            offset: 50,
            limit: 50,
          },
        },
        data: page1results,
        params: {
          keywordSearchQuery: 'test',
          searchResultType: 'ungrouped',
          checkedResultCategories: [],
          pagination: {
            ungrouped: {
              offset: 50,
              limit: 50,
            },
          },
        },
        status: 'success',
        isGrouped: false,
      })
    );

    fireEvent.click(prePageButton());

    await waitFor(() => {
      expect(firstRowCheckbox().checked).toBe(true);
    });
  });

  it('should selected correctly when hold shift on checkbox', async () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    const firstRowCheckbox = () =>
      getCheckbox('check-box__check-row-**********');
    const secondRowCheckbox = () =>
      getCheckbox('check-box__check-row-22222222');
    const thirdRowCheckbox = () => getCheckbox('check-box__check-row-33333333');
    expect(firstRowCheckbox().checked).toBe(false);
    expect(secondRowCheckbox().checked).toBe(false);
    expect(thirdRowCheckbox().checked).toBe(false);

    fireEvent.click(firstRowCheckbox());

    await waitFor(() => {
      expect(firstRowCheckbox().checked).toBe(true);
    });

    fireEvent.click(thirdRowCheckbox(), { shiftKey: true });

    await waitFor(() => {
      expect(thirdRowCheckbox().checked).toBe(true);
      expect(secondRowCheckbox().checked).toBe(true);
    });
  });

  it('should selected correctly when hold shift on table row', async () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    const firstRowCheckbox = () =>
      getCheckbox('check-box__check-row-**********');
    const secondRowCheckbox = () =>
      getCheckbox('check-box__check-row-22222222');
    const thirdRowCheckbox = () => getCheckbox('check-box__check-row-33333333');

    expect(firstRowCheckbox().checked).toBe(false);
    expect(secondRowCheckbox().checked).toBe(false);
    expect(thirdRowCheckbox().checked).toBe(false);

    fireEvent.click(firstRowCheckbox(), { ctrlKey: true });
    await waitFor(() => {
      expect(firstRowCheckbox().checked).toBe(true);
    });

    fireEvent.click(screen.getByTestId('table-row-33333333'), {
      shiftKey: true,
    });

    await waitFor(() => {
      expect(thirdRowCheckbox().checked).toBe(true);
    });

    await waitFor(() => {
      expect(firstRowCheckbox().checked).toBe(true);
      expect(secondRowCheckbox().checked).toBe(true);
      expect(thirdRowCheckbox().checked).toBe(true);
    });
  });

  it('should show add-to-case and delete buttons when select files not in any cases', async () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    const firstRowCheckbox = getCheckbox('check-box__check-row-**********');
    const secondRowCheckbox = getCheckbox('check-box__check-row-22222222');
    const sixthRowCheckbox = getCheckbox('check-box__check-row-66666666');
    expect(firstRowCheckbox).not.toBeChecked();
    expect(secondRowCheckbox).not.toBeChecked();
    expect(sixthRowCheckbox).not.toBeChecked();

    fireEvent.click(firstRowCheckbox);
    fireEvent.click(secondRowCheckbox);

    await waitFor(() => {
      expect(
        screen.queryByText('Move to Another Case')
      ).not.toBeInTheDocument();
      expect(screen.queryByText('Add to Case')).toBeInTheDocument();
      expect(screen.queryByText('Delete')).toBeInTheDocument();
    });
  });

  // TODO: Unskip when cases are queried upon search results being returned
  it.skip('should show move-to-another-case and delete buttons when select a file already in a case', async () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    const firstRowCheckbox = getCheckbox('check-box__check-row-**********');
    const sixthRowCheckbox = getCheckbox('check-box__check-row-66666666');
    expect(firstRowCheckbox).not.toBeChecked();
    expect(sixthRowCheckbox).not.toBeChecked();

    fireEvent.click(firstRowCheckbox);
    fireEvent.click(sixthRowCheckbox);

    await waitFor(() => {
      expect(screen.queryByText('Add to Case')).not.toBeInTheDocument();
      expect(screen.queryByText('Move to Another Case')).toBeInTheDocument();
      expect(screen.queryByText('Delete')).toBeInTheDocument();
    });
  });

  it('should show group buttons exactly when there is no file selected', async () => {
    const store = configureAppStore(appStoreState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    // don't show sort category when show list view
    expect(screen.queryByText('Sort Category :')).not.toBeInTheDocument();
    expect(screen.getByText('View :')).toBeInTheDocument();
    expect(screen.getByTestId('grid-view-icon')).toBeInTheDocument();

    userEvent.click(screen.getByTestId('grid-view-icon'));
    await waitFor(() => {
      expect(screen.queryByTestId('grid-view-icon')).not.toBeInTheDocument();
    });
    expect(screen.getByTestId('list-view-icon')).toBeInTheDocument();
    // show sort category when show grid view
    expect(screen.getByText('Sort Category :')).toBeInTheDocument();

    act(() => {
      store.dispatch(changeSearchView('Grouped'));
    });

    await waitFor(() => {
      expect(screen.getByTestId('gear-icon')).toBeInTheDocument();
    });
  });

  it('should display files grouped when the grouped view is selected', () => {
    const store = configureAppStore({
      ...appStoreState,
      search: {
        ...appStoreState.search,
        searchView: 'Grouped',
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Transcription')).toBeInTheDocument();
    expect(screen.queryByText('Facial Recognition')).not.toBeInTheDocument();
    expect(screen.getByText('Object Detection')).toBeInTheDocument();
  });

  it('should perform a search when the search view is changed', async () => {
    const searchFilesFnMock = vi.spyOn(searchFilesExports, 'searchFiles');

    const store = configureAppStore({
      ...appStoreState,
      search: {
        ...appStoreState.search,
        searchView: SearchView.UnGrouped,
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    const keywordSearchField = screen.getByTestId<HTMLInputElement>(
      'keyword-search-input'
    );

    const searchText = 'keyword search';

    // Type some text into the keyword search field
    userEvent.type(keywordSearchField, searchText);
    await waitFor(() => {
      expect(keywordSearchField.value).toBe(searchText);
    });

    act(() => {
      store.dispatch(changeSearchView('Grouped'));
    });

    expect(screen.getByText('Transcription')).toBeInTheDocument();
    expect(screen.queryByText('Facial Recognition')).not.toBeInTheDocument();
    expect(screen.getByText('Object Detection')).toBeInTheDocument();

    await waitFor(() => {
      expect(searchFilesFnMock).toHaveBeenCalled();
    });
  });

  it('should perform a search when the file header is clicked', async () => {
    const searchFilesFnMock = vi.spyOn(searchFilesExports, 'searchFiles');

    const store = configureAppStore({
      ...appStoreState,
      search: {
        ...appStoreState.search,
        searchView: SearchView.UnGrouped,
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    // Click on the file column header to change sort.
    const fileHeader = screen.getByText('File Name');
    userEvent.click(fileHeader);

    await waitFor(() => {
      // No search is performed because there is nothing in the search panel
      expect(searchFilesFnMock).not.toHaveBeenCalled();
    });

    const keywordSearchField = screen.getByTestId<HTMLInputElement>(
      'keyword-search-input'
    );

    const searchText = 'keyword search';

    // Type some text into the keyword search field.
    userEvent.type(keywordSearchField, searchText);
    await waitFor(() => {
      expect(keywordSearchField.value).toBe(searchText);
    });

    // Click on the file header to trigger a search
    userEvent.click(fileHeader);

    // Search should be triggered as a results of clicking the file header to sort
    await waitFor(() => {
      expect(searchFilesFnMock).toHaveBeenCalled();
    });
  });

  it('uses the correct search view on first search', async () => {
    const searchFilesFnMock = vi.spyOn(searchFilesExports, 'searchFiles');

    const store = configureAppStore({
      ...appStoreState,
      search: {
        ...appStoreState.search,
        searchView: SearchView.UnGrouped,
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Search />
        </MemoryRouter>
      </Provider>
    );

    // Switch to the grouped view
    act(() => {
      store.dispatch(changeSearchView('Grouped'));
    });

    expect(screen.getByText('Transcription')).toBeInTheDocument();
    expect(screen.queryByText('Facial Recognition')).not.toBeInTheDocument();
    expect(screen.getByText('Object Detection')).toBeInTheDocument();

    const keywordSearchField = screen.getByTestId<HTMLInputElement>(
      'keyword-search-input'
    );

    const searchText = 'keyword search';

    // Type some text into the keyword search field
    userEvent.type(keywordSearchField, searchText);
    await waitFor(() => {
      expect(keywordSearchField.value).toBe(searchText);
    });

    const submitButton = screen.getByTestId('filter-panel-submit-button');

    await waitFor(() => {
      expect(submitButton).toBeEnabled();
    });

    act(() => {
      fireEvent.click(submitButton);
    });

    // The searchFiles function should be called with a searchResultType of 'grouped'
    // because we switched to the grouped view before typing the search term
    await waitFor(() => {
      expect(searchFilesFnMock).toHaveBeenCalledWith(
        expect.objectContaining({
          abortSignal: expect.anything(),
          config: expect.anything(),
          dispatch: expect.anything(),
          gql: expect.anything(),
          lastParams: expect.anything(),
          params: expect.objectContaining({ searchResultType: 'grouped' }),
        })
      );
    });
  });
});
