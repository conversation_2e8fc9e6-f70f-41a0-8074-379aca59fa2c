.filter-cognition {
  &__tag {
    &-container {
      margin-left: 10px;
      width: 95%;
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 transparent;
      max-height: 178px;
      overflow-y: auto;
      overflow-x: hidden;
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    &-wrapper {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 4px;
      flex-wrap: nowrap;

      &:first-child {
        margin-top: 4px;
      }

      &-deleteIcon {
        font-size: 14px;
        color: gray;
        margin-right: 4px;
        cursor: pointer;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.6;
        }
      }

      &-actionIcon {
        font-size: 16px;
        color: gray;
      }

      &-avatar {
        width: 18px;
        height: 18px;
        margin-left: 0;
      }
    }
  }

  &__listbox {
    &-options {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 8px;
      gap: 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &.Mui-focused {
        background-color: rgba(0, 0, 0, 0.08);
      }

      &:hover {
        background-color: rgba(187, 177, 177, 0.12);
      }

      &-avatar {
        width: 18px;
        height: 18px;
        margin-left: 0;
      }

      &-name {
        font-size: 14px;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .Sdk-MuiTypography-root.filter-cognition-action {
    font-size: 12px !important;
    font-weight: 500 !important;
    color: #387fc7 !important;
    white-space: nowrap;
    padding: 2px 9px;
    border-radius: 10px;
    background-color: #c8cbce;
    flex: 0 0 auto;
  }

  .Sdk-MuiFormControl-root {
    display: inline-block;
    margin: 5px 0px;
    width: 264px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .Sdk-MuiChip-root {
    max-width: 150px;
    padding-left: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 12px;
    font-weight: 700;
    height: 24px;
    background-color: #ffffff;
    border: 1px solid #ccc;
    flex: 0 0 auto;
    background-color: var(--background-primary);

    .Sdk-MuiChip-label {
      padding: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 110px;
      display: block;
    }
  }

  .Sdk-MuiInputBase-root {
    gap: 2px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .Sdk-MuiInputBase-input {
      max-width: 236px;
    }

    #tags-filled {
      max-width: 236px;
    }
  }

  .Sdk-MuiAutocomplete-root .Sdk-MuiAutocomplete-inputRoot {
    margin: 0;
    padding-left: 0px;
    padding-right: 0px;
  }

  .filter-cognition__menuItem--operator {
    font-size: 12px !important;
    font-weight: 500 !important;
    color: #387fc7 !important;
    white-space: nowrap;
    padding: 1px 5px;
    border-radius: 10px;
    background-color: #c8cbce;
    flex: 0 0 auto;
    cursor: pointer;
    position: relative;

    &>.close-icon {
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .filter-cognition__menuItem-alias {
    width: 20px;
    height: 23px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #387fc7;
    background: #c8cbce;
    user-select: none;

    &>span {
      font-size: 14px;
      line-height: 1;
    }
  }
}

.cognition-autocomplete__operators-menu {
  .menu-paper {
    width: 226px;
    margin-left: 0;
    margin-top: 0;

    &>ul {
      &>li {
        height: 40px;
        font-size: 16px;
      }

      .remove-option {
        background: #ff656f;
        color: #ffffff;
      }
    }
  }

}