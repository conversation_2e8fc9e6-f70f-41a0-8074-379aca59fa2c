import './index.scss';
import CheckIcon from '@mui/icons-material/Check';
import {
  MenuItem,
  PopoverOrigin,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import { ReactNode } from 'react';

export interface Option {
  value: string;
  label: ReactNode;
}

export enum Selectbackground {
  Primary = 'var(--background-primary)',
  Secondary = 'var(--background-secondary)',
}

interface Props {
  options: Option[];
  selectValue: string;
  onChange: (e: SelectChangeEvent<string>) => void;
  height?: number;
  minWidth?: number;
  fullWidth?: boolean;
  background?: Selectbackground;
  menuListClassName?: string;
  menuPosition?: {
    anchorOrigin?: PopoverOrigin;
    transformOrigin?: PopoverOrigin;
  };
  onExited?: () => void;
  showCheckIcon?: boolean;
}

const StringSelect = ({
  options,
  onChange,
  selectValue,
  height,
  minWidth,
  fullWidth,
  background,
  menuListClassName,
  menuPosition,
  onExited,
  showCheckIcon,
}: Props) => {
  const renderMenuItem = ({ value, label }: Option) => {
    if (!showCheckIcon) {
      return label;
    }

    const icon =
      selectValue === value ? (
        <div className="string-select__menu-item-icon">
          <CheckIcon />
        </div>
      ) : (
        <div className="string-select__menu-item-icon" />
      );

    return (
      <>
        {icon}
        <span>{label}</span>
      </>
    );
  };

  const renderValue = (value: string) => {
    const selectedItem = options.find((option) => option.value === value);
    return selectedItem?.label || value;
  };

  return (
    <Select
      value={selectValue}
      onChange={onChange}
      fullWidth={fullWidth}
      sx={{
        height: height ? `${height}px` : 'auto',
        minWidth: minWidth ? `${minWidth}px` : 'auto',
        background: background,
        '& .Sdk-MuiSelect-select': {
          height: 'auto',
        },
      }}
      MenuProps={{
        ...menuPosition,
        MenuListProps: {
          className: menuListClassName,
        },
        TransitionProps: { onExited },
      }}
      renderValue={renderValue}
      data-testid="view-select"
    >
      {options.map((option) => (
        <MenuItem
          data-testid={`view-select-item-${option.value}`}
          key={option.value}
          value={option.value}
          className="string-select__menu-item"
        >
          {renderMenuItem(option)}
        </MenuItem>
      ))}
    </Select>
  );
};

export default StringSelect;
