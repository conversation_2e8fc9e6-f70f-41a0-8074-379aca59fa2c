.case-status__container {
  button {
    color: var(--text-primary);
    font-size: 9px;
    cursor: pointer;
    font-weight: 600;
    padding: 3px 10px;
    width: fit-content;
    border-radius: 32px;
    border: 1px solid;
    text-transform: uppercase;
    background-color: var(--status-pill);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.case-status__container:hover .case-status-clear-icon-container {
  display: block;
}

.case-status-clear-icon {
  font-size: 14px;
  padding-top: 3px;
  margin-left: 10px;
}

.case-status-clear-icon-container {
  display: none;
}

.case-status-menu {
  .Sdk-MuiMenuItem-root {
    padding: 8px 12px;
    gap: 10px;
  }

  .check-icon {
    display: block;
    width: 24px;
    height: 24px;
  }

  .color-icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
  }

  .status-label {
    font-size: 14px;
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .status-label-delete-icon {
    font-size: 14px;
    color: var(--button-destructive-action);
  }
}
