export type Metadata = FileMetadata &
  GenericInvestigateMetadata &
  EvidenceTypeMetadata;

export interface FileMetadata {
  aiwareTdoId: string;
  description: string;
  uploadedDate: string;
  fileSize: number;
  fileFormat: string;
  duration: number;
  fileName: string;
  caseId: string;
  caseName: string;
  aiCognitionEngineOutput: Array<AICognitionEngineOutput>;
  sourceName: string;
  creator: string;
  veritoneFile?: object;
  summary:
    | { reason: 'noEngineRun' | 'noResults'; text?: string }
    | { reason?: 'noEngineRun' | 'noResults'; text: string };
}

export interface GenericInvestigateMetadata {
  sourceId: string;
  contentType: ContentType;
  assetStatus: AssetStatus;
}

export interface EvidenceTypeMetadata {
  evidenceType: EvidenceType | '';

  /* Optional Evidence Type Data */
  badgeId?: string;
  cadId?: string;
  callerPhoneNumber?: string;
  cameraFacingDirection?: string;
  cameraPhysicalAddress?: string;
  cameraPhysicalLocation?: string;
  cameraType?: 'Doorbell Camera' | 'Mobile Phone' | 'Security Camera';
  citizenName?: string;
  dateOfBirth?: string;
  deviceId?: string;
  deviceModel?: string;
  deviceName?: string;
  deviceRegisteredOwner?: string;
  deviceType?:
    | 'Desktop'
    | 'Laptop'
    | 'Phone'
    | 'Tablet'
    | 'Other (e.g. iPod Touch)';
  evidenceTechnician?: string;
  firstName?: string;
  interviewRoom?: string;
  interviewee?: string;
  interviewer?: string;
  lastName?: string;
  locationTimeline?: {
    latitude: string;
    longitude: string;
  }[];
  officerName?: string;
  reportNumber?: string;
  unitNumber?: string;
}

export type AICognitionEngineOutput =
  | 'Face Detection'
  | 'Facial Recognition'
  | 'Graphic Content Detection'
  | 'License Plate Recognition'
  | 'Media Summarization'
  | 'Object Detection'
  // | 'Scene Classification'
  | 'Text Recognition'
  | 'Transcription'
  | 'Translate Foreign Audio'
  | 'Vehicle Recognition';

export type ContentType = 'video' | 'audio' | 'image' | 'document';

export type AssetStatus =
  | 'pending'
  | 'processing'
  | 'error'
  | 'processed'
  | null;

export type EvidenceType =
  | '911 Call Recording'
  | 'Arrest Report'
  | 'Body Worn Camera'
  | 'Booking Photo'
  | 'Citizen Submitted Video'
  | 'Crime Scene Photo'
  | 'In Car Video'
  | 'Interview Audio Recording'
  | 'Interview Room Recording'
  | 'Mobile Device Extraction'
  | 'Security Camera Video';
