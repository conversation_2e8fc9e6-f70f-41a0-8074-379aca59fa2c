// This file serves as a central hub for re-exporting pre-typed Redux hooks.
// These imports are restricted elsewhere to ensure consistent
// usage of typed hooks throughout the application.
// We disable the ESLint rule here because this is the designated place
// for importing and re-exporting the typed versions of hooks.

import { Action, ThunkDispatch } from '@reduxjs/toolkit';
import HttpClient from '@store/dependencies/httpClient';
import type { RootState } from '@store/index';
import { useDispatch } from 'react-redux';

interface ExtraArg {
  http: HttpClient;
}

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch =
  useDispatch.withTypes<ThunkDispatch<RootState, ExtraArg, Action>>();
