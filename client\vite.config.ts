import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import gci from 'git-commit-info';
import csp from 'vite-plugin-csp';
import { defineConfig } from 'vite';
import svgr from 'vite-plugin-svgr';
import { createRequire } from 'module';
import mkcert from 'vite-plugin-mkcert';
import react from '@vitejs/plugin-react-swc';
import safeConfigKeys from './configWhitelist.json';

const require = createRequire(import.meta.url);
const processEnv = process.env.ENVIRONMENT || 'local';
const deployedConfigPath = `./config-stage.json`;
const localConfigPath = './config-stage.json';
const isDeployed = processEnv && fs.existsSync(deployedConfigPath);
// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
const appConfig = require(isDeployed ? deployedConfigPath : localConfigPath);
const safeConfig = _.pick(appConfig, safeConfigKeys);

export default defineConfig({
  plugins: [
    react(),
    svgr(),
    // eslint(),
    // stylelint(),
    mkcert({ hosts: ['local.veritone.com'] }),
    svgr(),
    csp({
      policy: {
        'base-uri': ['self'],
        'object-src': ['none'],
        'script-src': [
          'self',
          'unsafe-eval',
          'get.aiware.com',
          'veritone.my.site.com',
          'cdn.jsdelivr.net',
          'cdnjs.cloudflare.com',
          'nonce-NGINX_CSP_NONCE',
        ],
        'style-src': [
          'self',
          'fonts.googleapis.com',
          'unsafe-inline',
          'cdn.jsdelivr.net',
          'static.veritone.com',
          'veritone.my.site.com',
        ],
        'font-src': [
          'self',
          'data:',
          'fonts.googleapis.com',
          'cdn.jsdelivr.net',
          'fonts.gstatic.com',
          'static.veritone.com',
        ],
      },
      hashingMethod: 'sha256',
      hashEnabled: {
        'script-src': false,
        'style-src': true,
        'script-src-attr': false,
        'style-src-attr': false,
      },
      nonceEnabled: {
        'script-src': false,
        'style-src': false,
      },
    }),
  ],
  server: {
    // https: {
    //   cert: fs.readFileSync(path.resolve(__dirname, '../local.veritone.com.pem')),
    //   key: fs.readFileSync(path.resolve(__dirname, '../local.veritone.com-key.pem')),
    // },
    // Make sure the server is accessible over the local network
    host: 'local.veritone.com',
    port: 4200,
  },
  define: {
    'process.env': {}, // process.env.ENVIRONMENT
    buildDetails: {
      hash: gci().hash,
      date: gci().date,
      message: gci().message,
    },
    config: JSON.stringify(safeConfig),
  },
  html: {
    cspNonce: 'NGINX_CSP_NONCE',
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler',
      },
    },
  },
  build: {
    rollupOptions: {
      output: {
        entryFileNames: `main.js`,
      },
    },
  },
  resolve: {
    alias: {
      react: path.resolve('./node_modules/react'),
      'react-dom': path.resolve('./node_modules/react-dom'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@components': path.resolve(__dirname, './src/components'),
      '@assets/images': path.resolve(__dirname, 'src/assets/images'),
      '@assets/icons': path.resolve(__dirname, 'src/assets/icons'),
      '@i18n': path.resolve(__dirname, 'src/i18n'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@app-types': path.resolve(__dirname, 'src/types'),
      '@store': path.resolve(__dirname, 'src/store'),
      '@theme': path.resolve(__dirname, 'src/theme'),
    },
  },
});
