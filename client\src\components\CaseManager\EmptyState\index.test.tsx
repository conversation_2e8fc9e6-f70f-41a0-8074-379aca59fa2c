import { I18nTranslate } from '@i18n';
import { fireEvent, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import EmptyState from '.';
import { render } from '../../../../test/render';

describe('EmptyState Component', () => {
  it('calls onClick when Add Files button is clicked', () => {
    const handleUploadFile = vi.fn();

    render(
      <EmptyState
        imageSrc={I18nTranslate.TranslateMessage('image')}
        title={I18nTranslate.TranslateMessage('title')}
        description={I18nTranslate.TranslateMessage('description')}
        buttonText={I18nTranslate.TranslateMessage('addFiles')}
        onClick={handleUploadFile}
      />
    );

    const addFilesButton = screen.getByTestId('add-files-button');

    fireEvent.click(addFilesButton);

    expect(handleUploadFile).toHaveBeenCalledTimes(1);
  });
});
