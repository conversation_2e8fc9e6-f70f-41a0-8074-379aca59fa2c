import './index.scss';
import { I18nTranslate } from '@i18n';
import CloseIcon from '@mui/icons-material/Close';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import {
  Autocomplete,
  AutocompleteChangeReason,
  AutocompleteRenderGetTagProps,
  Avatar,
  Box,
  Chip,
  CircularProgress,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  Typography,
} from '@mui/material';
import Tooltip from '@mui/material/Tooltip';
import { FilterFormValue } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  MathOperators,
  getEntitySearch,
  getLibrarySearch,
  getObjectSearch,
  resetEntityAndLibrarySearch,
  resetObjectSearch,
  selectEntitySearch,
  selectLibrarySearch,
  selectObjectSearch,
} from '@store/modules/search/slice';
import { ApiStatus } from '@store/types';
import {
  faceDetectionsValidation,
  objectDetectionsValidation,
} from '@utils/validationTokens';
import { debounce } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ControllerRenderProps, UseFormSetError } from 'react-hook-form';
import { useSelector } from 'react-redux';
import PreviewCognition from './PreviewCognition';

export enum Mode {
  FACE = 'face',
  OBJECT = 'object',
}

export enum Parenthesis {
  OPEN_PARENTHESIS = MathOperators.OPEN_PARENTHESIS,
  CLOSE_PARENTHESIS = MathOperators.CLOSE_PARENTHESIS,
}

interface TagValue {
  value: CognitionItem;
  operator?: MathOperators;
}
type CurrentTagValue = Record<string, TagValue>;

export interface AliasParenthesis {
  parenthesis: Parenthesis;
  total: number;
}

export type AliasParenthesisMap = Record<string, AliasParenthesis>;

export interface CognitionItem {
  id: string;
  cognitionType:
    | 'string'
    | 'entity'
    | 'library'
    | 'foundString'
    | 'foundFullTextString'
    | 'operator'
    | 'default'; // for default type to prevent filter options return empty
  type: 'value' | 'operator' | 'parenthesis';
  value: string;
  profileImageUrl?: string;
  isValid?: boolean; // value has entity/library or foundString
  alias?: string; // Assign an alias to each cognition item with type 'value'
  valueId?: string; // parenthesis will have this field to link with the value
}

export type CognitionList = CognitionItem[];

interface CognitionAutocompleteProps {
  field: ControllerRenderProps<
    FilterFormValue,
    'faceDetections' | 'objectDescriptors'
  >;
  mode: Mode;
  setFormError?: UseFormSetError<FilterFormValue>;
  disableLibraryCognitionForms: boolean;
}

const CognitionAutocomplete: React.FC<CognitionAutocompleteProps> = ({
  field,
  mode,
  setFormError,
  disableLibraryCognitionForms,
}) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const textFieldRef = useRef<HTMLDivElement | null>(null);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [fieldValues, setFieldValues] = useState<CognitionList>(field.value);
  const [tagValueMap, setTagValueMap] = useState<CurrentTagValue>({});
  const [currentTagId, setCurrentTagId] = useState<string | null>(null);
  const [inputValue, setInputValue] = useState<string>('');
  const [aliasParenthesisMap, setAliasParenthesisMap] =
    useState<AliasParenthesisMap>({});
  const [error, setError] = useState<string | undefined>(undefined);

  const entitySearch = useSelector(selectEntitySearch);
  const librarySearch = useSelector(selectLibrarySearch);
  const objectSearch = useSelector(selectObjectSearch);

  useEffect(() => setFieldValues(field.value), [field.value]);

  const isLoadingSearch = (status: ApiStatus) =>
    status === 'idle' || status === 'loading';

  const isLoading =
    mode === Mode.FACE
      ? isLoadingSearch(entitySearch.status) ||
        isLoadingSearch(librarySearch.status)
      : isLoadingSearch(objectSearch.status);

  const unifiedCognitionData = useMemo((): CognitionList => {
    let cognitionData: CognitionList = [];

    if (mode === Mode.FACE) {
      if (entitySearch.data && entitySearch.data.length > 0) {
        cognitionData = cognitionData.concat(
          entitySearch.data.map(
            (item): CognitionItem => ({
              id: item.doc.entityId,
              value: item.doc.entityName,
              type: 'value',
              cognitionType: 'entity',
              profileImageUrl: item.doc.profileImageUrl,
            })
          )
        );
      }
      if (librarySearch.data && librarySearch.data.length > 0) {
        cognitionData = cognitionData.concat(
          librarySearch.data.map(
            (item): CognitionItem => ({
              id: item.doc.libraryId,
              value: item.doc.libraryName,
              cognitionType: 'library',
              type: 'value',
              profileImageUrl: item.doc.profileImageUrl,
            })
          )
        );
      }
    } else {
      if (objectSearch.data && objectSearch.data.length > 0) {
        cognitionData = cognitionData.concat(
          objectSearch.data.map(
            (item): CognitionItem => ({
              id: item.key,
              value: item.key,
              cognitionType: 'foundString',
              type: 'value',
            })
          )
        );
      }
    }

    if (!inputValue.trim()) {
      return cognitionData;
    }

    // wait for query entity/library or object to check if the query text has results
    if (!isLoading) {
      return [
        mode === Mode.FACE
          ? {
              id: `string-${inputValue}`,
              value: inputValue,
              cognitionType: 'string',
              type: 'value',
            }
          : {
              id: `fullText-${inputValue}`,
              value: inputValue,
              cognitionType: 'foundFullTextString',
              type: 'value',
            },
        ...cognitionData,
      ];
    }

    // just add default type to prevent filter options return empty leading to doesn't show option's popup
    return [
      {
        id: inputValue,
        value: inputValue,
        cognitionType: 'default',
        type: 'value',
      },
    ];
  }, [
    mode,
    entitySearch.data,
    librarySearch.data,
    objectSearch.data,
    inputValue,
  ]);

  useEffect(() => {
    if (!error) {
      setFormError?.('faceDetections', {});
    }

    if (mode === Mode.FACE) {
      setFormError?.('faceDetections', {
        type: 'manual',
        message: error,
      });
    } else if (mode === Mode.OBJECT) {
      setFormError?.('objectDescriptors', {
        type: 'manual',
        message: error,
      });
    }
  }, [error, setFormError]);

  const autocompleteDebounce = useMemo(
    () =>
      debounce((value: string) => {
        if (mode === Mode.FACE) {
          dispatch(getEntitySearch(value));
          dispatch(getLibrarySearch(value));
        } else {
          dispatch(
            getObjectSearch({
              keywordSearchQuery: value.trim(),
              searchResultType: 'ungrouped',
              checkedResultCategories: [],
              pagination: { ungrouped: { offset: 0, limit: 10 } },
            })
          );
        }
      }, 2000),
    [dispatch, mode]
  );

  const handleUpdateFieldValues = (newValues: CognitionList) => {
    setFieldValues(newValues);

    const { isValid, reason } =
      mode === Mode.FACE
        ? faceDetectionsValidation(newValues)
        : objectDetectionsValidation(newValues);

    if (!isValid) {
      setError(reason);
      return;
    }

    field.onChange(newValues);
    setError(undefined);
  };

  const filterAutocompleteOptions = (options: CognitionList) => {
    if (!inputValue.trim()) {
      return [];
    }
    const selectedIdSet = new Set(fieldValues.map((item) => item.id));
    return options.filter(
      (option) =>
        !selectedIdSet.has(option.id) &&
        option.value.toLowerCase().includes(inputValue.toLowerCase())
    );
  };

  const handleOpenOperatorMenu = (tagId: string) => {
    if (textFieldRef.current) {
      setAnchorEl(textFieldRef.current);
      setCurrentTagId(tagId);
    }
  };

  // clear entity/library or object search store to prevent showing old data
  const handleResetState = () => {
    if (mode === Mode.FACE) {
      dispatch(resetEntityAndLibrarySearch());
    } else {
      dispatch(resetObjectSearch());
    }
  };

  const handleCloseOperatorMenu = () => {
    setAnchorEl(null);
    setCurrentTagId(null);
    handleResetState();
  };

  const handleRemoveOperator = (tagId: string) => {
    const tagValue = tagValueMap[tagId];
    const tagIndex = fieldValues.findIndex((tag) => tag.id === tagId);
    if (tagIndex === -1 || !tagValue || !tagValue.operator) {
      handleCloseOperatorMenu();
      return;
    }

    setTagValueMap((prev) => ({
      ...prev,
      [tagId]: {
        ...tagValue,
        operator: undefined,
      },
    }));

    const updateTags = [...fieldValues];
    const tagParenthesis = aliasParenthesisMap[tagId];
    let startIndex = tagIndex + 1;

    if (tagParenthesis) {
      if (tagParenthesis.parenthesis === Parenthesis.CLOSE_PARENTHESIS) {
        startIndex = tagIndex + tagParenthesis.total + 1;
      }
    }

    updateTags.splice(startIndex, 1);
    handleUpdateFieldValues(updateTags);
    handleCloseOperatorMenu();
  };

  const renderOperatorsMenu = () => {
    if (!currentTagId || !tagValueMap[currentTagId]) {
      return null;
    }

    return (
      <Menu
        id="long-menu"
        className="cognition-autocomplete__operators-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseOperatorMenu}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        slotProps={{
          paper: {
            className: 'menu-paper',
          },
        }}
        disableAutoFocusItem
      >
        {[MathOperators.AND, MathOperators.OR].map((label) => (
          <MenuItem key={label} onClick={() => handleSelectOperator(label)}>
            {label.toUpperCase()}
          </MenuItem>
        ))}
        {tagValueMap[currentTagId]?.operator && (
          <>
            <MenuItem
              onClick={() => handleRemoveOperator(currentTagId)}
              className="remove-option"
            >
              {intl.formatMessage({
                id: 'removeOperator',
                defaultMessage: 'Remove Operator',
              })}
            </MenuItem>
          </>
        )}
      </Menu>
    );
  };

  const handleSelectOperator = (option: MathOperators) => {
    if (currentTagId === null || !tagValueMap[currentTagId]) {
      handleCloseOperatorMenu();
      return;
    }

    const tagIndex = fieldValues.findIndex((tag) => tag.id === currentTagId);
    if (tagIndex === -1) {
      return;
    }

    setTagValueMap((prev) => ({
      ...prev,
      [currentTagId]: {
        ...prev[currentTagId],
        operator: option,
      },
    }));

    const { operator: currOperator } = tagValueMap[currentTagId];
    const updateTags = [...fieldValues];
    const OperatorTag: CognitionItem = {
      id: `operator-${currentTagId}`,
      value: option,
      cognitionType: 'operator',
      type: 'operator',
    };

    const tagParenthesis = aliasParenthesisMap[currentTagId];
    let startIndex = tagIndex + 1;
    let deleteCount = 0;

    if (!tagParenthesis) {
      if (currOperator) {
        deleteCount = 1;
      }
    } else {
      const isOpen =
        tagParenthesis.parenthesis === Parenthesis.OPEN_PARENTHESIS;
      const totalParenthesis = tagParenthesis.total;

      if (isOpen) {
        deleteCount = currOperator ? 1 : 0;
      } else {
        startIndex = tagIndex + totalParenthesis + 1;
        deleteCount = currOperator ? 1 : 0;
      }
    }

    updateTags.splice(startIndex, deleteCount, OperatorTag);
    handleUpdateFieldValues(updateTags);
    handleCloseOperatorMenu();
  };

  const noneOperatorTagId = useMemo(() => {
    const noneOperatorTag = Object.values(tagValueMap).find(
      (tag) => !tag.operator
    );
    if (!noneOperatorTag) {
      return undefined;
    }
    return noneOperatorTag.value.id;
  }, [tagValueMap]);

  const handleOnInputChange = (value: string) => {
    // clear entity/library or object search store when typing to prevent showing old data
    handleResetState();

    if (!noneOperatorTagId) {
      setInputValue(value);
      autocompleteDebounce(value);
      return;
    }

    // show the warning when user try to typing new selection if the previous tag doesn't have an operator
    handleOpenOperatorMenu(noneOperatorTagId);
    enqueueSnackbar({
      message: intl.formatMessage({
        id: 'noneOperatorWarning',
        defaultMessage: 'Please select an operator before select a new one',
      }),
      variant: 'warning',
    });
  };

  const parseCounter = (alias: string) => {
    const match = alias.match(/^([A-Z])(\d+)?$/);
    if (!match) {
      return null;
    }

    const baseChart = match[1];
    const suffix = match[2] ? parseInt(match[2], 10) : 0;
    const baseIndex = baseChart.charCodeAt(0) - 65;

    return suffix * 26 + baseIndex;
  };

  const renderAlias = () => {
    const reverseValues = fieldValues.reverse();
    const lastAlias = reverseValues.find((item) => !!item.alias);

    const aliasNumber = parseCounter(lastAlias?.alias || 'invalid-alias');
    const nextAliasNumber = aliasNumber !== null ? aliasNumber + 1 : 0;

    const base = String.fromCharCode(65 + (nextAliasNumber % 26));
    const suffix = Math.floor(nextAliasNumber / 26);

    return suffix > 0 ? `${base}${suffix}` : base; // A, B, C,... A1, B1, C1,...
  };

  const processCognitionItems = (items: (string | CognitionItem)[]) => {
    const cognitionValues: CognitionList = [];
    const newTagValueMap: CurrentTagValue = {};

    for (const item of items) {
      if (typeof item === 'string') {
        continue;
      }

      if (item.type !== 'value') {
        cognitionValues.push(item);
        continue;
      }

      if (!item.alias) {
        item.alias = renderAlias();
      }

      const cognition: CognitionItem =
        item.isValid !== false
          ? {
              ...item,
              isValid: true,
            }
          : item;
      cognitionValues.push(cognition);

      newTagValueMap[item.id] = {
        value: cognition,
        operator: tagValueMap[item.id]?.operator,
      };
    }

    handleUpdateFieldValues(cognitionValues);
    setTagValueMap(newTagValueMap);
    setInputValue('');
  };

  const isCognitionItemArray = (
    items: (string | CognitionItem)[]
  ): items is CognitionItem[] =>
    items.every((item) => typeof item !== 'string');

  const handleOnChange = (
    e: React.SyntheticEvent,
    newValues: (string | CognitionItem)[],
    reason: AutocompleteChangeReason
  ) => {
    if (!inputValue.trim()) {
      return;
    }

    if (!isCognitionItemArray(newValues)) {
      return;
    }

    // prevent immediate pressing enter after type text
    // need to wait query entity/library or object to check if the query text has results
    if (
      (e as React.KeyboardEvent).key === 'Enter' &&
      reason === 'createOption'
    ) {
      e.preventDefault();
      return;
    }

    const isFaceMode = mode === Mode.FACE;
    // check has library or entity if in face mode
    // check has foundString if in object mode
    const hasType = unifiedCognitionData.some(
      (item) =>
        item.cognitionType === (isFaceMode ? 'library' : 'foundString') ||
        (isFaceMode && item.cognitionType === 'entity')
    );

    // just add plain text to show as a selection but it will be cleared in the params
    if (!hasType) {
      const type = isFaceMode ? 'string' : 'fullText';
      const plainTextItem: CognitionItem = {
        id: `${type}-${inputValue}`,
        value: inputValue,
        cognitionType: isFaceMode ? 'string' : 'foundFullTextString',
        type: 'value',
        isValid: true,
        alias: renderAlias(),
      };

      newValues[newValues.length - 1] = plainTextItem;
      handleUpdateFieldValues(newValues);
      setTagValueMap((prev) => ({
        ...prev,
        [plainTextItem.id]: { value: plainTextItem },
      }));
      setInputValue('');
      return;
    }

    processCognitionItems(newValues);
  };

  const handleDeleteChip = (tagId: string) => {
    setTagValueMap((prev) => {
      const { [tagId]: _, ...rest } = prev;
      return rest;
    });

    const tagIndex = fieldValues.findIndex((tag) => tag.id === tagId);

    if (tagIndex === -1) {
      return;
    }

    const updatedTags = [...fieldValues];
    const tagParenthesis = aliasParenthesisMap[tagId];
    const nextTag = updatedTags[tagIndex + 1];

    let startIndex = tagIndex;
    let deleteCount = 1;

    if (!tagParenthesis) {
      if (nextTag?.cognitionType === 'operator') {
        deleteCount = 2;
      }
    } else {
      const isOpen =
        tagParenthesis.parenthesis === Parenthesis.OPEN_PARENTHESIS;
      const totalParenthesis = tagParenthesis.total;

      if (isOpen) {
        startIndex = tagIndex - totalParenthesis;
        deleteCount =
          totalParenthesis + (nextTag?.cognitionType === 'operator' ? 2 : 1);
      } else {
        deleteCount =
          totalParenthesis + (nextTag?.cognitionType === 'operator' ? 2 : 1);
      }

      setAliasParenthesisMap((prev) => {
        const newMap = { ...prev };
        delete newMap[tagId];
        return newMap;
      });
    }

    updatedTags.splice(startIndex, deleteCount);
    handleUpdateFieldValues(updatedTags);
  };

  const handleRenderTags = (
    tagValue: CognitionList,
    getTagProps: AutocompleteRenderGetTagProps
  ) => (
    <div className="filter-cognition__tag-container">
      {tagValue.map((option, index) => {
        const { id: tagId, value: label, cognitionType, alias } = option;
        const operator = tagValueMap[tagId]?.operator;

        if (cognitionType === 'operator') {
          return <></>;
        }

        const { key, ...restTagProps } = getTagProps({ index });

        return (
          <div key={tagId} className="filter-cognition__tag-wrapper">
            {alias && (
              <>
                <div className="filter-cognition__menuItem-alias">
                  <span>{alias}</span>
                </div>
                {/* eslint-disable-next-line formatjs/no-literal-string-in-jsx */}
                <span>-</span>
              </>
            )}
            <Chip
              label={label}
              {...restTagProps}
              deleteIcon={
                <CloseIcon className="filter-cognition__tag-wrapper-deleteIcon" />
              }
              avatar={
                mode === Mode.FACE &&
                option.cognitionType === 'string' ? undefined : (
                  <Avatar
                    src={option.profileImageUrl || '/default-avatar.png'}
                    className="filter-cognition__tag-wrapper-avatar"
                  />
                )
              }
              onDelete={() => handleDeleteChip(tagId)}
            />
            {operator ? (
              <Typography
                variant="body2"
                className="filter-cognition__menuItem--operator"
                onClick={() => handleOpenOperatorMenu(tagId)}
              >
                {operator.toLowerCase()}
              </Typography>
            ) : (
              <IconButton
                size="small"
                onClick={() => handleOpenOperatorMenu(tagId)}
              >
                <MoreHorizIcon className="filter-cognition__tag-wrapper-actionIcon" />
              </IconButton>
            )}
          </div>
        );
      })}
    </div>
  );

  const renderAutocomplete = () => (
    <Autocomplete
      {...field}
      id="tags-filled"
      filterSelectedOptions
      multiple
      freeSolo
      disabled={disableLibraryCognitionForms}
      inputValue={inputValue}
      onInputChange={(_, value) => handleOnInputChange(value)}
      value={fieldValues}
      onBlur={() => setInputValue('')}
      onChange={handleOnChange}
      filterOptions={filterAutocompleteOptions}
      renderTags={handleRenderTags}
      options={unifiedCognitionData}
      getOptionLabel={(option) =>
        typeof option === 'string' ? option : option.value
      }
      groupBy={(item) => {
        if (mode === Mode.FACE) {
          if (item.cognitionType === 'library') {
            return intl.formatMessage({ id: 'libraries' });
          } else if (item.cognitionType === 'entity') {
            return intl.formatMessage({ id: 'entities' });
          }
          return 'Plain text';
        } else {
          if (item.cognitionType === 'foundString') {
            return intl.formatMessage({ id: 'foundString' });
          } else if (item.cognitionType === 'foundFullTextString') {
            return intl.formatMessage({ id: 'foundFullTextString' });
          }
          return 'Plain text';
        }
      }}
      renderGroup={(params) => (
        <div>
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 'bold',
              padding: '8px 16px',
              backgroundColor: 'transparent',
              border: 'none',
            }}
          >
            {params.group}
          </Typography>
          <Divider
            sx={{
              padding: '4px 0px',
              marginBottom: '8px',
              borderColor: '#E0E0E0',
            }}
          />
          {isLoading ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <CircularProgress size={16} />
            </Box>
          ) : (
            params.children
          )}
        </div>
      )}
      renderOption={(props, option) => {
        if (typeof option === 'string') {
          return (
            <li {...props} key={option}>
              <span className="filter-cognition__listbox-options-name">
                {option}
              </span>
            </li>
          );
        }

        return (
          <li
            {...props}
            className="filter-cognition__listbox-options"
            key={option.id}
          >
            {mode === Mode.FACE && option.cognitionType === 'string' ? null : (
              <Avatar
                src={option.profileImageUrl || '/default-avatar.png'}
                className="filter-cognition__listbox-options-avatar"
              />
            )}
            <span className="filter-cognition__listbox-options-name">
              {option.value}
            </span>
          </li>
        );
      }}
      slotProps={{
        popper: {
          modifiers: [
            { name: 'preventOverflow', options: { padding: 8 } },
            {
              name: 'flip',
              options: { fallbackPlacements: ['bottom-start'] },
            },
          ],
        },
      }}
      renderInput={(params) => (
        <TextField
          ref={textFieldRef}
          {...params}
          type="text"
          inputProps={{
            ...params.inputProps,
            'data-testid': 'cognition-autocomplete-input',
          }}
          placeholder={
            fieldValues.length > 0
              ? ''
              : intl.formatMessage({
                  id:
                    mode === Mode.FACE ? 'faceDetections' : 'objectDescriptors',
                })
          }
        />
      )}
    />
  );

  return (
    <>
      {disableLibraryCognitionForms ? (
        <Tooltip
          title={intl.formatMessage({ id: 'cognitionFiltersNotAvailable' })}
        >
          {renderAutocomplete()}
        </Tooltip>
      ) : (
        renderAutocomplete()
      )}
      {!!fieldValues.length && (
        <PreviewCognition
          error={error}
          fieldValues={fieldValues}
          updateFieldValues={handleUpdateFieldValues}
          aliasParenthesisMap={aliasParenthesisMap}
          setAliasParenthesisMap={setAliasParenthesisMap}
        />
      )}
      {renderOperatorsMenu()}
    </>
  );
};

export default CognitionAutocomplete;
