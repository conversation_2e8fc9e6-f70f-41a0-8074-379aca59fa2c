import { CaseSearchResults } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function reassignStatus({
  cases,
  statusId,
  dataRegistryId,
  gql,
}: {
  cases: CaseSearchResults['searchMedia']['jsondata']['results'];
  statusId: string;
  dataRegistryId: string;
  gql: GQLApi;
}) {
  const folderContentTemplateSchemaId =
    await gql.getSDOSchemaId(dataRegistryId);

  const data = cases.map((caseItem) => {
    const { id, ...rest } = caseItem;
    return {
      id,
      data: {
        ...rest,
        statusId: statusId,
        sdoId: id,
      },
    };
  });

  const response = await gql.createSDOs(folderContentTemplateSchemaId, data);
  return response.data;
}
