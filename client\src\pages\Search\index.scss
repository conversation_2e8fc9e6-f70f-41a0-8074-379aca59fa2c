.search-page {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 8px;
  margin-right: 15px;
  margin-bottom: 15px;
  flex-direction: column;

  &__header {
    gap: 15px;
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    justify-content: space-between;

    &-title {
      display: flex;
      font-size: 16px;
      font-weight: 600;
      align-items: center;
      gap: 20px;
      color: var(--text-primary);
      height: 24px;
      padding-left: 21px;
      min-width: 334px;

      .search-page__header__file-count {
        font-weight: normal;
        font-size: 14px;
      }
    }
  }

  &__content {
    flex: 1;
    gap: 15px;
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    flex-direction: row;
    color: var(--text-primary);
  }
}
