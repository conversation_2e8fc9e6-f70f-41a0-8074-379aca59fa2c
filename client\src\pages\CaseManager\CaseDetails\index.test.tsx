import { sagaIntl } from '@i18n';
import { configureAppStore } from '@store/index';
import {
  initialState as caseManagerInitialState,
  CaseManagerSliceState,
  toggleCaseDrawer,
} from '@store/modules/caseManager/slice';
import {
  initialState as settingInitialState,
  SettingsSliceState,
} from '@store/modules/settings/slice';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GQLA<PERSON> } from '@utils/helpers';
import { Provider } from 'react-redux';
import { describe, expect, it, vi } from 'vitest';
import CaseDetails from '.';
import { render } from '../../../../test/render';

vi.mock('../../../utils/helpers/gqlApi/baseGraphQLApi', () => ({
  baseGraphQLApiThrowError: vi.fn(),
  baseGraphQLApi: vi.fn(),
}));

const mockGetFolderResponse = {
  id: 'folderId123',
  name: 'folder test',
  description: '',
  treeObjectId: 'treeObjectId123',
  createdDateTime: '2025-02-11T02:32:41.716Z',
  modifiedDateTime: '2025-02-11T02:32:41.716Z',
  contentTemplates: [
    {
      id: 'contentTemplateId123',
      sdo: {
        id: 'sdoId123',
        schemaId: 'schemaId123',
        data: {
          caseId: '11',
          statusId: 'key1',
          caseDate: '2025-02-10T02:32:30.726Z',
          caseName: 'folder test',
          folderId: 'folderId123',
          createdBy: '5a638922-bc0a-46ca-b43d-b12363888221',
          createdByName: 'mockOwnerName',
          modifiedBy: '5a638922-bc0a-46ca-b43d-b12363888221',
          description: '',
          createdDateTime: '2025-02-11T02:32:42.614Z',
          modifiedDateTime: '2025-02-11T02:32:42.614Z',
          preconfiguredTagIds: ['tagKey3', 'tagKey2'],
        },
      },
    },
  ],
};

const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: {
    ...settingInitialState,
    fetchedStatuses: {
      status: 'idle',
      error: '',
      statuses: [
        {
          id: 'key1',
          label: 'status1',
          active: true,
          color: '#00FF00',
        },
        {
          id: 'key2',
          label: 'status2',
          active: true,
          color: '#FF0000',
        },
      ],
      sdoId: '',
    },
  },
  caseManager: {
    ...caseManagerInitialState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'rootFolder123',
    },
    folderContentTemplateSchema: {
      status: 'idle',
      error: '',
      id: 'folderContentTemplateSchemaId123',
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

const mockGetUserResponse = {
  id: 'userId123',
  email: '<EMAIL>',
  firstName: 'firstNameMock',
  lastName: 'lastNameMock',
};

describe('Case Details', () => {
  it('renders case details with Empty state', () => {
    const mockOnShowCaseFiles = vi.fn();
    render(<CaseDetails showCaseFiles onShowCaseFiles={mockOnShowCaseFiles} />);

    expect(screen.queryByTestId('case-details')).not.toBeInTheDocument();
  });

  it('renders case details with Error state', () => {
    const mockOnShowCaseFiles = vi.fn();
    render(
      <CaseDetails
        folderId="" // folderId and caseName is empty means there is an error
        showCaseFiles
        onShowCaseFiles={mockOnShowCaseFiles}
      />
    );

    expect(screen.getByText('Something went wrong...')).toBeInTheDocument();
    expect(
      screen.queryByTestId('case-details-content')
    ).not.toBeInTheDocument();
  });

  it('clicking the View Case Details button -> change button to tabs in header', async () => {
    const mockOnShowCaseFiles = vi.fn();
    vi.spyOn(GQLApi.prototype, 'getFolder').mockImplementationOnce(() =>
      Promise.resolve(mockGetFolderResponse)
    );
    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockImplementationOnce(() =>
      Promise.resolve(mockGetUserResponse)
    );

    const store = configureAppStore(initialStateForMock);

    render(
      <Provider store={store}>
        <CaseDetails
          folderId={mockGetFolderResponse.id}
          showCaseFiles={false}
          onShowCaseFiles={mockOnShowCaseFiles}
        />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('case-details-content')).toBeInTheDocument();
    });

    userEvent.click(screen.getByTestId('view-case-details-button'));

    await waitFor(() => {
      expect(mockOnShowCaseFiles).toHaveBeenCalled();
    });
  });

  it('clicking the Edit case button -> opens edit case drawer', async () => {
    const mockOnShowCaseFiles = vi.fn();
    vi.spyOn(GQLApi.prototype, 'getFolder').mockImplementationOnce(() =>
      Promise.resolve(mockGetFolderResponse)
    );
    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockImplementationOnce(() =>
      Promise.resolve(mockGetUserResponse)
    );
    const store = configureAppStore(initialStateForMock);
    const dispatchSpy = vi.spyOn(store, 'dispatch');
    render(
      <Provider store={store}>
        <CaseDetails
          folderId={mockGetFolderResponse.id}
          showCaseFiles={false}
          onShowCaseFiles={mockOnShowCaseFiles}
        />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('case-details-content')).toBeInTheDocument();
    });

    userEvent.click(screen.getByTestId('edit-case-button'));

    await waitFor(() => {
      expect(dispatchSpy).toHaveBeenCalledWith(
        toggleCaseDrawer(mockGetFolderResponse.id)
      );
    });
  });

  it('opens edit case details and shows user name', async () => {
    const mockOnShowCaseFiles = vi.fn();
    vi.spyOn(GQLApi.prototype, 'getFolder').mockImplementationOnce(() =>
      Promise.resolve(mockGetFolderResponse)
    );
    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockImplementationOnce(() =>
      Promise.resolve(mockGetUserResponse)
    );
    const store = configureAppStore(initialStateForMock);

    render(
      <Provider store={store}>
        <CaseDetails
          folderId={mockGetFolderResponse.id}
          showCaseFiles={false}
          onShowCaseFiles={mockOnShowCaseFiles}
        />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('case-details-content')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('case-detail-user-name')).toHaveTextContent(
        `${mockGetUserResponse.firstName} ${mockGetUserResponse.lastName}`
      );
    });
  });

  it('should render correct case tags', async () => {
    const data = mockGetFolderResponse.contentTemplates[0].sdo.data;
    vi.spyOn(GQLApi.prototype, 'getFolder').mockImplementationOnce(() =>
      Promise.resolve(mockGetFolderResponse)
    );
    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockImplementationOnce(() =>
      Promise.resolve(mockGetUserResponse)
    );
    const store = configureAppStore({
      ...initialStateForMock,
      settings: {
        ...initialStateForMock.settings,
        fetchedTags: {
          status: 'idle',
          error: '',
          tags: [
            {
              id: 'tagKey3',
              label: 'tag1',
              active: true,
            },
            {
              id: '2',
              label: 'tag2',
              active: true,
            },
          ],
        },
      },
    });

    render(
      <Provider store={store}>
        <CaseDetails
          folderId={mockGetFolderResponse.id}
          showCaseFiles={false}
          onShowCaseFiles={vi.fn()}
        />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('case-details-content')).toBeInTheDocument();
    });
    expect(
      screen.getByTestId(`case-tag-${data.preconfiguredTagIds[0]}`)
    ).toBeTruthy();
    expect(
      screen.queryByTestId(`case-tag-${data.preconfiguredTagIds[1]}`)
    ).toBeFalsy();
  });

  it('clicking the status button opens the statuses menu', async () => {
    const mockOnShowCaseFiles = vi.fn();
    vi.spyOn(GQLApi.prototype, 'getFolder').mockImplementationOnce(() =>
      Promise.resolve(mockGetFolderResponse)
    );
    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockImplementationOnce(() =>
      Promise.resolve(mockGetUserResponse)
    );

    const store = configureAppStore(initialStateForMock);

    render(
      <Provider store={store}>
        <CaseDetails
          folderId={mockGetFolderResponse.id}
          showCaseFiles={false}
          onShowCaseFiles={mockOnShowCaseFiles}
        />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('case-details-content')).toBeInTheDocument();
    });

    userEvent.click(screen.getByTestId('case-status-button'));

    await waitFor(() => {
      expect(screen.getByTestId('case-status-menu')).toBeInTheDocument();
    });
  });

  it('show empty status when the case does not have any status', async () => {
    const mockOnShowCaseFiles = vi.fn();

    const mockFolderWithEmptyStatusId = {
      ...mockGetFolderResponse,
      contentTemplates: [
        {
          ...mockGetFolderResponse.contentTemplates[0],
          sdo: {
            ...mockGetFolderResponse.contentTemplates[0].sdo,
            data: {
              ...mockGetFolderResponse.contentTemplates[0].sdo.data,
              statusId: '',
            },
          },
        },
      ],
    };

    vi.spyOn(GQLApi.prototype, 'getFolder').mockImplementationOnce(() =>
      Promise.resolve(mockFolderWithEmptyStatusId)
    );
    vi.spyOn(GQLApi.prototype, 'getBasicUserInfo').mockImplementationOnce(() =>
      Promise.resolve(mockGetUserResponse)
    );

    const store = configureAppStore(initialStateForMock);

    render(
      <Provider store={store}>
        <CaseDetails
          folderId={mockFolderWithEmptyStatusId.id}
          showCaseFiles={false}
          onShowCaseFiles={mockOnShowCaseFiles}
        />
      </Provider>
    );

    await waitFor(() => {
      expect(
        screen.queryByText(sagaIntl().formatMessage({ id: 'selectAStatus' }))
      ).toBeInTheDocument();
    });
  });
});
