import { Given, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { settingPage } from '../../../pages/settingPage';
import '../common-setting/common-setting';

Given('The user logins successfully', () => {
  cy.LoginLandingPage();
});

Given('The user logins as {string}', (userName: string) => {
  cy.loginAsUser(userName);
});

Given('The user is on Tag Settings screen', () => {
  settingPage.goToTagSetting();
});

Given('The user deletes tag if exists', (tagName: DataTable) => {
  cy.deleteTagByName(tagName);
});

Given('The user creates a default tag name {string}', (tagName: string) => {
  settingPage.createDefaultTag(tagName);
});
