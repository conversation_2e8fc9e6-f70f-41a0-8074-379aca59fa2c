.empty-state__container {
  top: 50%;
  left: 50%;
  width: 100%;
  display: flex;
  position: absolute;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  transform: translate(-50%, -50%);

  p {
    text-align: center;
  }
}

.empty-state-icon {
  width: 160px;
  height: 120px;
}

.create-case-empty-button {
  color: var(--text-primary);
  background: transparent !important;

  &.Sdk-MuiButtonBase-root:not(:disabled):not(:hover) {
    background: transparent !important;
  }
}

