.cognition-preview {
  width: 264px;
  display: block;
  overflow-wrap: break-word;
  padding: 6.5px 0;
  margin-top: -13px;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--border-color);

  &__parenthesis-option {
    font-size: 12px;
    padding: 2px 3px;
    border-radius: 3px;
    background-color: #dbdbdba1;
    color: var(--button-text-disabled);
    cursor: pointer;
  }

  .open {
    margin-right: 1px;
  }

  .close {
    margin-left: 1px;
  }

  &__item {
    font-size: 12px;
    padding: 2px;
    user-select: none;
  }

  &__alias {
    padding: 2px 3px;
    margin: 2px;
    font-weight: 500;
    border-radius: 10px;
    background-color: #c8cbce;
    color: #387fc7;
    cursor: pointer;
  }

  &__parenthesis {
    cursor: pointer;
  }

}

.cognition-warning-message {
  color: #ed6c02;
  font-size: 12px;
  margin-top: -13px;
  margin-left: 5px;
}