import js from '@eslint/js';
import globals from 'globals';
import react from 'eslint-plugin-react';
import promise from 'eslint-plugin-promise';
import eslintConfigPrettier from 'eslint-config-prettier';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import formatjs from 'eslint-plugin-formatjs';
import tseslint from 'typescript-eslint';
import importPlugin from 'eslint-plugin-import';
import cspellESLintPluginRecommended from '@cspell/eslint-plugin/recommended';
import pluginCypress from 'eslint-plugin-cypress/flat';
// import jambitTypedReduxSaga from '@jambit/eslint-plugin-typed-redux-saga'

export default [
  {
    ignores: [
      '**/codecept.conf.js',
      '**/test_e2e/',
      '**/translate-coverage.js',
      '**/webpack.config.js',
      '**/webpack.dev.js',
      '**/webpack.prod.js',
      '**/webpack.common.js',
      'coverage/**',
      '**/cypress.config.ts',
    ],
  },
  js.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  importPlugin.flatConfigs.recommended,
  react.configs.flat.recommended,
  eslintPluginPrettierRecommended,
  // @ts-expect-error tODO: why doesn't this have types?
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  formatjs.configs.recommended,
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  promise.configs['flat/recommended'],
  eslintConfigPrettier,
  eslintPluginPrettierRecommended,
  cspellESLintPluginRecommended,
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  pluginCypress.configs.recommended,
  {
    // plugins: {
    //   '@jambit/typed-redux-saga': jambitTypedReduxSaga,
    // },
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.amd,
        ...globals.node,
        React: 'readonly',
        Promise: 'readonly',
        WeakMap: 'readonly',
        JSX: true,
      },
      parser: tseslint.parser,
      ecmaVersion: 8,
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          impliedStrict: true,
          jsx: true,
          experimentalObjectRestSpread: true,
          legacyDecorators: true,
        },
        // TODO: Needed when @typescript-eslint/no-deprecated is enabled.
        // projectService: true,
        projectService: {
          // TODO: Figure out how to correctly get these files included
          maximumDefaultProjectFileMatchCount_THIS_WILL_SLOW_DOWN_LINTING: 13,
          allowDefaultProject: [
            '.prettierrc.cjs',
            '.stylelintrc.js',
            '.yarn/releases/*.cjs',
            'eslint.config.mts',
            'test/*.js',
            'vitest.config.ts',
          ],
        },
        project: ['./tsconfig.lint.json'],
      },
    },
    settings: {
      react: {
        version: 'detect',
      },
      'import/resolver': {
        typescript: {
          project: './tsconfig.json',
        },
        'eslint-import-resolver-custom-alias': {
          alias: {
            '@pages/*': ['src/pages/*'],
            '@components/*': ['src/components/*'],
            '@assets/images': ['src/assets/images'],
            '@assets/icons': ['src/assets/icons'],
            '@i18n': ['src/i18n'],
            '@hooks': ['src/hooks'],
            '@shared-types/*': ['src/types/*'],
            '@utils/*': ['src/utils/*'],
            '@store/*': ['src/store/*'],
            '@theme': ['src/theme'],
          },
          extensions: ['.ts', '.tsx'],
        },
      },
    },
    rules: {
      'no-console': 0,
      'promise/always-return': 'off',
      'promise/catch-or-return': 'off',
      'promise/no-return-wrap': 'warn',
      'promise/param-names': 'warn',
      'promise/no-native': 'off',
      'promise/no-nesting': 'warn',
      'promise/no-promise-in-callback': 'warn',
      'promise/no-callback-in-promise': 'off',
      // 'lodash/prefer-lodash-method': 'off',
      // 'lodash/import-scope': 'off',
      // 'lodash/path-style': ['warn', 'as-needed'],
      // 'lodash/prefer-noop': 'off',
      'no-debugger': 'warn',
      'no-var': 'warn',
      'no-unused-vars': [
        'warn',
        {
          vars: 'all',
          args: 'none',
        },
      ],
      'block-scoped-var': 'warn',
      'no-param-reassign': 'warn',
      curly: 'warn',
      'no-tabs': 'warn',
      'require-await': 'warn',
      'react/jsx-no-bind': 'off',
      'react/jsx-boolean-value': 'warn',
      'react/jsx-handler-names': [
        'off',
        {
          eventHandlerPropPrefix: '',
        },
      ],
      'react/jsx-key': 'warn',
      'react/jsx-no-comment-textnodes': 'warn',
      'react/jsx-pascal-case': [
        'warn',
        {
          allowAllCaps: true,
        },
      ],
      'react/forbid-prop-types': 'off',
      'react/no-did-mount-set-state': 'warn',
      'react/no-did-update-set-state': 'warn',
      'react/no-array-index-key': 'warn',
      'react/self-closing-comp': [
        'warn',
        {
          component: true,
          html: true,
        },
      ],
      'react/sort-comp': 'warn',
      'react/prefer-es6-class': 'warn',
      'react/style-prop-object': 'warn',
      'react/no-unescaped-entities': 'warn',
      'react/default-props-match-prop-types': 'warn',
      'react/forbid-foreign-prop-types': 'warn',
      'react/no-access-state-in-setstate': 'warn',
      'react/no-children-prop': 'warn',
      'react/no-typos': 'warn',
      'react/no-this-in-sfc': 'warn',
      'react/no-unused-prop-types': 'warn',
      // 'import/named': 'warn',
      // 'import/no-cycle': 'warn',
      eqeqeq: ['error', 'always'],
      'prettier/prettier': ['error'],
      'arrow-body-style': 'error',
      'eol-last': 'error',
      'guard-for-in': 'error',
      'no-restricted-modules': ['error', 'rxjs/Rx'],
      'no-caller': 'error',
      'prefer-const': 'error',
      'spaced-comment': 'warn',
      '@typescript-eslint/prefer-function-type': ['error'],
      '@typescript-eslint/unified-signatures': 'error',
      '@typescript-eslint/consistent-type-definitions': 'error',
      '@typescript-eslint/explicit-member-accessibility': [
        'off',
        {
          accessibility: 'explicit',
        },
      ],
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          args: 'all',
          argsIgnorePattern: '^_',
          caughtErrors: 'all',
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
      // TODO: Enable this, and fix all deprecation usages.
      '@typescript-eslint/no-deprecated': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-empty-interface': 'error',
      '@typescript-eslint/no-inferrable-types': 'error',
      '@typescript-eslint/no-misused-new': 'error',
      'react/jsx-uses-react': 'off',
      'react/react-in-jsx-scope': 'off',
      'testing-library/no-manual-cleanup': 'off',
      'testing-library/no-node-access': 'off',
      'testing-library/no-wait-for-multiple-assertions': 'off',
      '@typescript-eslint/no-namespace': 'off',
      'formatjs/no-offset': 'error',
      'formatjs/enforce-default-message': 'off',
      'formatjs/enforce-description': 'off',
      '@cspell/spellchecker': [
        'warn',
        {
          checkIdentifiers: false,
        },
      ],
      // TODO: Type checked rules to enable and fix
      '@typescript-eslint/unbound-method': 'off',
      '@typescript-eslint/no-floating-promises': 'off',
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx'],
    rules: {
      'no-unused-vars': 'off',
      // 'import/named': 'off',
      'no-useless-escape': 'off',
      'no-undef': 'off',
      'no-redeclare': 'off',
      'react/prop-types': 'off',
      'react/no-unused-prop-types': 'off',
      '@typescript-eslint/no-redeclare': ['error'],
      // 'lodash/prefer-lodash-typecheck': 'off',
      // 'lodash/prop-shorthand': 'off',
    },
  },
  {
    files: ['./test/**/*.ts', './test/**/*.js'],
    rules: {
      'no-import-assign': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
    },
  },
  // {
  //   files: ['./**/*.ts'],
  //   ignores: ['./**/*.spec.ts', './**/*.test.ts'],
  //   rules: {
  //     '@jambit/typed-redux-saga/use-typed-effects': ['error', 'macro'],
  //     '@jambit/typed-redux-saga/delegate-effects': 'error',
  //   },
  // },
  {
    files: ['./**/*.test.ts', './**/*.test.tsx', 'cypress/**/*.ts'],
    rules: {
      '@typescript-eslint/no-floating-promises': 'off',
      '@typescript-eslint/no-misused-promises': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
    },
  },
];
