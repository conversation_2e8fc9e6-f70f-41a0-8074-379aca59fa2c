.multi-select {
  &__filter-select {
    height: 30px;
    width: 214px;
    background: var(--background-primary);
    line-height: normal;

    & > .Sdk-MuiSelect-select {
      height: auto;
    }
  }

  &__normal-width {
    width: 60%;
  }

  &__rendered-label {
    display: flex;
    flex: 1 3 auto;
    align-items: center;
    flex-wrap: wrap;
  }

  &__disabled {
    color: var(--text-disabled);
  }

  &__filter-selected-number {
    font-size: 12px !important;

    &.haschanges {
      background: #d5e8f9 !important;
      border-radius: 3px;
    }
  }

  &__selected-number {
    padding: 0 10px;
    background: var(--button-background);
    font-size: 14px;

    &.haschanges {
      background: var(--background-green-light);
    }
  }

  &__filter-selected-text {
    font-size: 12px !important;
  }

  &__selected-text {
    padding-left: 10px;
    font-size: 14px;
  }

  &__filter-menu {
    .Sdk-MuiPaper-root {
      max-height: 300px !important;

      & > ul {
        padding: 0;

        & > li {
          padding: 5px 12px;

          &:hover(:not(.Mui-selected)) {
            background: var(--background-primary);
          }
        }
      }
    }
  }

  &__filter-search-header {
    height: 50px !important;
    margin-top: 0 !important;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
  }

  &__search-header {
    background: var(--background-secondary);
    margin-top: -10px;
    height: 76px;
    border-bottom: solid 1px var(--border-color);
  }

  &__filter-search-input {
    margin-top: 0 !important;
    height: 25px !important;
    width: 200px;
  }

  &__search-input {
    border-radius: 32px;
    height: 32px;
    margin-top: 20px;
    background: var(--background-input);
  }
}
