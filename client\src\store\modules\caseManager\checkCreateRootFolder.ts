import { sagaIntl } from '@i18n';
import { G<PERSON><PERSON>pi } from '@utils/helpers';

export async function checkCreateRootFolder(gql: GQLApi) {
  let rootFolder = await gql.getRootFolder();
  if (!rootFolder?.id) {
    const newRootFolder = await gql.createRootFolders();
    rootFolder = newRootFolder.rootFolders.find(
      (folder) => folder.ownerId === null
    );
  }
  if (!rootFolder?.id) {
    alert(sagaIntl().formatMessage({ id: 'noRootFolder' }));
    throw new Error('no root folder is created');
  }
  return rootFolder.id;
}
