.case-details-table {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 10px;
  margin-right: 15px;
  margin-bottom: 15px;
  flex-direction: column;

  &__header {
    gap: 15px;
    height: 52px;
    display: flex;
    margin-left: 21px;
    align-items: center;
    padding-bottom: 26px;

    &__title {
      gap: 10px;
      display: flex;
      font-size: 14px;
      align-items: center;
      color: var(--text-primary);
    }

    &__case-detail {
      font-weight: bold;
      padding-left: 15px;
      color: var(--text-secondary);
      border-left: 1px solid var(--app-status-tab-border);
    }
  }

  .case-details-table__content {
    flex: 1;
    gap: 15px;
    display: flex;
    overflow: auto;
    flex-direction: row;
  }

  .file__table {
    flex: 2;
    max-height: 100%;
  }
}
