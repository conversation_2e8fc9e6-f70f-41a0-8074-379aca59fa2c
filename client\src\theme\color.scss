body {
  --button-dark-blue: #054fa3;
  --button-inner-dark: #000000cc;
  --button-light-blue: #00b7ff;
  --button-destructive-action: #a61111;
  --button-text-disabled: #afafaf;
  --button-background-disabled: #e4e4e4;
  --button-text-submit: #ffffff;

  --checkbox-checked: #054fa3;
  --checkbox-unchecked: #616161;

  --focus-blue: #8188ff;

  --text-disabled: #afafaf;
  --text-link: #1565c0;
  --surface-disabled: #e4e4e4;

  --status-green: #d8d8d8;
  --status-blue: #0067cf;
  --status-red-dark: #950001;
  --status-red: #b30002;
  --status-brown: #804d00;
  --status-grey-blue: #4f5863;
  --status-purple: #8257ef;
  --status-pill: #ffffff;

  --app-status-tab-border: #d8d8d8;

  --color-picker-hue-pointer: #ffffff;

  --border-color: #e4e4e4;

  --button-inner: #212121;
  --button-hover: #0000000d;
  --button-background: #0000000d;
  --button-nav-bar: #000000;

  --chip-background-pink: #efdeff;
  --chip-background-grey: #dadada;

  --menu-background-hover: #f5f5f5;

  --text-primary: #212121;
  --text-secondary: #424242;
  --text-tertiary: #757575;

  --input-border: #9e9e9e;

  --background-primary: #f5f5f5;
  --background-secondary: #fafafa;
  --background-tertiary: #ffffff;
  --background-lavender: #e7dff4;
  --background-orange: #f8e9cb;
  --background-green: #d3f115;
  --background-green-light: #edfbe6;

  --surface-grey: #efefef;
  --hr: #e5e5ea;

  --background-selected: #eceff2;
  --background-table: #f6f6f6;
  --row-border-table: #ffffff;
  --background-input: #ffffff;

  --scrollbar-thumb: #0000007f;
  --table-border-color: #d8d8d8;
  --snackbar-action-text: #64B5F6;

  // categories
  --background-filename: #eaeaea;
  --background-transcription: #f1f8e9;
  --background-face-recognition: #e1f5fe;
  --background-object-detection: #fbe9e7;
  --background-vehicle-recognition: #ede7f6;
  --background-license-plate-recognition: #e0f7fa;
  --background-scene-classification: #fff8e1;
  --background-text-recognition: #ffebee;
  --background-metadata: #e8eaf6;

  &.dark {
    --caret: #0057ff;
    --border-color: #282828;

    --button-inner: #ffffff;
    --button-hover: #ffffff0d;
    --button-background: #ffffff0d;
    --button-nav-bar: #ffffff;

    --chip-background-grey: #333333;
    --chip-text: #ececec;

    --text-primary: #ffffff;

    --menu-background-hover: #ffffff0d;

    --text-secondary: #d8d8d8;
    --text-tertiary: #757575;
    --text-link: #ABCEED;

    --input-border: #4d4d4d;

    --status-pill: #333333;

    --background-primary: #101010;
    --background-secondary: #171717;
    --background-tertiary: #000000;
    --background-lavender: #212534;
    --background-orange: #2e2c18;
    --background-green: #1f3423;
    --background-green-light: #1f3423;

    --surface-grey: #212121;
    --hr: #373737;

    --background-selected: #202020;
    --background-table: #1c1c1c;
    --row-border-table: #f6f6f6;
    --background-input: #272727;

    --scrollbar-thumb: #ffffff7f;
    --table-border-color: #282828;
    --snackbar-action-text: #1565C0;

    // categories
    --background-filename: #151515;
    --background-transcription: #112311;
    --background-face-recognition: #112B36;
    --background-object-detection: #370C0C;
    --background-vehicle-recognition: #1E1128;
    --background-license-plate-recognition: #00272C;
    --background-scene-classification: #382B00;
    --background-text-recognition: #310822;
    --background-metadata: #141B46;
  }
}
