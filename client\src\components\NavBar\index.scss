.tab-panel {
  gap: 5px;
  height: 100%;
  padding: 0 12px 16px;
}

.add-button {
  background-color: var(--button-dark-blue);
  color: white;
  border-radius: 50px;
  min-width: 40px;
  width: 40px;
  height: 40px;
}

.tab-button {
  width: 40px;
  height: 40px;
  min-width: 40px;
  border-radius: 20px;
  color: var(--button-background);

  .Sdk-MuiSvgIcon-root > * {
    fill: var(--button-inner);
  }

  &.selected {
    box-shadow: none;
    -webkit-box-shadow: none;
    background: var(--button-background);

    .Sdk-MuiSvgIcon-root > * {
      fill: var(--button-dark-blue);
    }
  }

  &.Mui-disabled {
    transition: none;
    background: var(--background-primary);
  }

  &:hover {
    background: var(--button-background);
  }

  &:last-of-type {
    justify-self: flex-end;
  }
}

.snackbar-subtitle {
  font-size: 12px;
}
