import {
  CaseButtonText,
  CognitionInput,
  CognitionPlaceholder,
  DropdownBoxCaseType,
  EvidenceTypeInput,
  FileTypeInput,
  GroupView,
  SearchDataTestSelector as Search,
  titleInSearchPage,
  ViewType,
} from '../support/helperFunction/searchResultsHelper';
import { caseManagerPage } from '../pages/caseManagerPage';

export const searchPage = {
  visit: (): void => {
    cy.visit('/');
  },
  goToSearchPage: () => {
    cy.get(`[data-test=${Search.NavTabSearch}]`).should('be.visible').click();
    cy.url().should('include', 'search');
    cy.get('.search-page__header-title').should(
      'contain.text',
      'Search and Manage Files'
    );
  },
  verifySearchPage: () => {
    cy.getDataIdCy({ idAlias: Search.SearchFilter })
      .should('be.visible')
      .within(() => {
        titleInSearchPage.forEach((title) => {
          cy.contains(title).should('be.visible');
        });
        cy.getDataIdCy({ idAlias: Search.ResetAll }).should('be.visible');
        cy.getDataIdCy({ idAlias: Search.SearchInput }).should('be.visible');
      });
  },
  enterSearchKeyword: (keyword: string) => {
    cy.getDataIdCy({ idAlias: Search.SearchInput }).type(keyword);
  },
  verifySearchResults: (caseId: string) => {
    cy.get('[data-testid^="table-row-"]', { timeout: 60000 })
      .contains(caseId)
      .then(($el) => {
        expect($el).to.have.length(1);
        cy.wrap($el).scrollIntoView();
        cy.wrap($el).should('be.visible');
      });
  },
  pickCaseInformation: (
    caseInformation: string,
    dropdownBox: DropdownBoxCaseType
  ) => {
    cy.get(`#mui-component-select-${dropdownBox}`).click();
    cy.get('[role="listbox"]').contains(caseInformation).click();
  },
  chooseFirstCaseInList: () => {
    cy.get('[data-testid^="table-row-"]').first().click();
  },
  clickKebabIconFirstRow: () => {
    cy.get('[data-testid^="table-row-"]')
      .first()
      .within(() => {
        cy.getDataIdCy({ idAlias: Search.SearchTableMenu }).click();
      });
  },
  selectKebabMenuItem: (menuItemName: string) =>
    cy.get('[role="menu"]').contains(menuItemName).should('be.visible').click(),
  InputCaseId: (caseId: string) =>
    cy.getDataIdCy({ idAlias: Search.FilterCaseTextField }).type(caseId),
  PickDateRange: (startDay: number, endDay: number, month: string) => {
    cy.getDataIdCy({ idAlias: Search.FilterDateRange }).click();
    cy.get('span span select').first().select(month);
    cy.get('.rdrMonth')
      .should('be.visible')
      .within(() => {
        cy.get('.rdrDay')
          .contains(new RegExp(`^${startDay}$`))
          .click();
        cy.get('.rdrDay')
          .contains(new RegExp(`^${endDay}$`))
          .click();
      });
    cy.getDataIdCy({ idAlias: Search.ConfirmButtonDate }).click();
  },
  ClickToOpenSearchInCaseType: (caseTypeName: string) => {
    cy.contains(caseTypeName).should('be.visible').click();
  },
  InputCognitionFields: (inputs: CognitionInput[]) => {
    cy.getDataIdCy({ idAlias: Search.FilterCognition }).within(() => {
      inputs.forEach((inputItem) => {
        cy.get(`input[placeholder="${inputItem.placeholder}"]`)
          .should('be.visible')
          .type(inputItem.value);
      });
    });
  },
  CheckEvidenceTypes: (evidenceTypes: EvidenceTypeInput[]) => {
    cy.checkItemsByLabelText(/^Evidence Type$/, evidenceTypes, 'evidenceType');
  },
  CheckFileTypes: (fileTypes: FileTypeInput[]) => {
    cy.checkItemsByLabelText(/^File Type$/, fileTypes, 'fileType');
  },
  ClickResetAllButton: () => {
    cy.getDataIdCy({ idAlias: Search.ResetAll }).click();
  },
  VerifyResetAll: () => {
    cy.getDataIdCy({ idAlias: Search.SearchInput }).should('have.value', '');
    cy.getDataIdCy({ idAlias: Search.FilterCaseTextField }).should(
      'have.value',
      ''
    );
    cy.getDataIdCy({ idAlias: Search.FilterDateRange }).should(
      'have.value',
      ''
    );
    cy.getDataIdCy({ idAlias: Search.FilterCognition }).within(() => {
      Object.values(CognitionPlaceholder).forEach((placeholder) => {
        cy.get(`input[placeholder="${placeholder}"]`).should('have.value', '');
      });
    });
    cy.contains('Evidence Type').should('be.visible').click();
    cy.checkItemsByLabelText(/^Evidence Type$/, [], 'evidenceType', true);
    cy.contains('Evidence Type').should('be.visible').click();
    cy.contains('File Type').should('be.visible').click();
    cy.checkItemsByLabelText(/^File Type$/, [], 'fileType', true);
  },
  ChangeGroupView: (view: GroupView) => {
    cy.getDataIdCy({ idAlias: Search.ViewSelect }).click();
    cy.get(`[data-testid="view-select-item-${view}"]`).click();
  },
  verifyTextInFileDetailView: (expectedText: string) => {
    cy.get('iframe')
      .its('0.contentDocument.body')
      .should('not.be.empty')
      .then(cy.wrap)
      .contains(expectedText)
      .should('be.visible');
  },
  selectRowsPerPage: (rowsPerPage: string) => {
    cy.get('[data-testid^="table-row-"]').should('have.length.gt', 0);

    cy.getDataIdCy({ idAlias: Search.TablePaginationContainer })
      .should('exist')
      .scrollIntoView();

    cy.getDataIdCy({ idAlias: Search.TablePaginationContainer }).should(
      'be.visible'
    );

    cy.getDataIdCy({
      idAlias: Search.TablePaginationRowsPerPageSelect,
    }).scrollIntoView();

    cy.getDataIdCy({ idAlias: Search.TablePaginationRowsPerPageSelect })
      .parent()
      .should('be.visible')
      .should('not.be.disabled')
      .click();

    cy.get('[role="listbox"]')
      .should('be.visible')
      .contains(new RegExp(`^${rowsPerPage}$`))
      .click();

    cy.wait('@searchMedia', { timeout: 10000 })
      .its('response.statusCode')
      .should('eq', 200);
  },
  verifyMaxRowsDisplayed: (maxRows: number) => {
    cy.get('[data-testid^="table-row-"]', { timeout: 60000 })
      .should('be.visible')
      .should('have.length.at.least', 1);

    cy.get('[data-testid^="table-row-"]').then(($rows) => {
      const actualRowCount = $rows.length;
      expect(actualRowCount).to.be.at.most(maxRows);
      expect(actualRowCount).to.be.greaterThan(0);
    });
  },
  clickNextPageButton: () => {
    cy.getDataIdCy({ idAlias: Search.TablePaginationContainer })
      .should('exist')
      .scrollIntoView();

    cy.getDataIdCy({
      idAlias: Search.TablePaginationNextButton,
    }).scrollIntoView();

    cy.getDataIdCy({ idAlias: Search.TablePaginationNextButton })
      .should('be.visible')
      .should('not.be.disabled')
      .click();

    cy.wait('@searchMedia', { timeout: 10000 })
      .its('response.statusCode')
      .should('eq', 200);
  },
  clickPreviousPageButton: () => {
    cy.getDataIdCy({ idAlias: Search.TablePaginationContainer })
      .should('exist')
      .scrollIntoView();

    cy.getDataIdCy({
      idAlias: Search.TablePaginationPreviousButton,
    }).scrollIntoView();

    cy.getDataIdCy({ idAlias: Search.TablePaginationPreviousButton })
      .should('be.visible')
      .should('not.be.disabled')
      .click();

    cy.wait('@searchMedia', { timeout: 10000 })
      .its('response.statusCode')
      .should('eq', 200);
  },
  verifyPageRange: (expectedRange: string) => {
    cy.getDataIdCy({ idAlias: Search.TablePaginationContainer })
      .should('exist')
      .scrollIntoView();

    cy.getDataIdCy({
      idAlias: Search.TablePaginationDisplayedRows,
    }).scrollIntoView();

    cy.getDataIdCy({ idAlias: Search.TablePaginationDisplayedRows })
      .should('be.visible')
      .contains(new RegExp(expectedRange.replace(/[–\-\s]/g, '[–\\-\\s]*')))
      .should('be.visible');

    cy.log(`Verified page range: ${expectedRange}`);
  },

  verifyViewToggleButtonsPresent: () => {
    cy.getDataIdCy({ idAlias: Search.ViewTypeToggleButton })
      .should('be.visible')
      .should('not.be.disabled');
  },
  verifyCurrentViewType: (viewType: ViewType) => {
    if (viewType === ViewType.list) {
      cy.getDataIdCy({ idAlias: Search.GridViewIcon }).should('be.visible');
      cy.getDataIdCy({ idAlias: Search.ListViewIcon }).should('not.exist');
    } else {
      cy.getDataIdCy({ idAlias: Search.ListViewIcon }).should('be.visible');
      cy.getDataIdCy({ idAlias: Search.GridViewIcon }).should('not.exist');
    }
  },
  clickViewToggleButton: () => {
    cy.getDataIdCy({ idAlias: Search.ViewTypeToggleButton })
      .should('be.visible')
      .should('not.be.disabled')
      .click();
  },
  verifyListViewActive: () => {
    cy.getDataIdCy({ idAlias: Search.SortLabelFileName }).should('be.visible');
    cy.get('[data-testid^="table-row-"]').first().should('be.visible');

    cy.getDataIdCy({ idAlias: Search.SearchResultCardView }).should(
      'not.exist'
    );
  },
  verifyGridViewActive: () => {
    cy.getDataIdCy({ idAlias: Search.SearchResultCardView }).should(
      'be.visible'
    );
    cy.getDataIdCy({ idAlias: Search.FileCard }).first().should('be.visible');
  },
  clickSearchButton: () => {
    cy.get('[role="progressbar"]').should('not.exist', { timeout: 60000 });
    cy.getDataIdCy({ idAlias: Search.SearchButton }).click();
  },

  verifyInitEmptyState: () => {
    cy.contains('Start Exploring').should('be.visible');
    cy.contains('Search for anything').should('be.visible');
    cy.get('[data-testid^="table-row-"]').should('not.exist');
    cy.getDataIdCy({ idAlias: Search.SearchInput }).should('have.value', '');
  },

  validateSearchResultsState: () => {
    cy.wait('@searchMedia', { timeout: 60000 })
      .its('response.statusCode')
      .should('eq', 200);
    cy.get('[data-testid^="table-row-"]').should('have.length.greaterThan', 0);
    cy.get('[data-testid^="table-row-"]').first().should('be.visible');
  },

  verifyErrorMessage: (errorMessage: string) => {
    cy.get('#notistack-snackbar')
      .should('be.visible')
      .and('contain.text', errorMessage);
  },

  selectsCase: (targetCase: string) => {
    cy.getDataIdCy({ idAlias: 'add-to-case-dialog' })
      .should('be.visible')
      .as('addToCaseDialog');
    cy.getByRoles('progressbar').should('not.exist');
    cy.get('@addToCaseDialog')
      .parent()
      .within(() => {
        cy.get('input[type="text"]').type(targetCase);
      });
    cy.getByRoles('menuitem').contains(targetCase).click({ force: true });
    caseManagerPage.clickConfirmButton();
  },

  opensMenuItem: (fileName: string) => {
    cy.get('[data-testid^="table-row-"]', { timeout: 60000 })
      .contains(fileName)
      .should('be.visible')
      .parents('[data-testid^="table-row-"]')
      .within(() => {
        cy.getDataIdCy({
          idAlias: Search.SearchTableMenu,
        }).click({ force: true });
      });
  },

  clicksAddFilesToCase: () => {
    cy.contains(
      'button',
      new RegExp(`^(${CaseButtonText.ADD}|${CaseButtonText.MOVE})$`)
    ).click();
  },

  checksTheBoxes: (fileList: string) => {
    const filesSelected = fileList.split(',').map((name) => name.trim());

    filesSelected.forEach((fileName) => {
      cy.get('[data-testid^="table-row-"]')
        .filter(':has(input[type="checkbox"]:not(:checked))')
        .contains(fileName)
        .parents('[data-testid^="table-row-"]')
        .first()
        .within(() => {
          cy.get('input[type="checkbox"]').click();
        });
    });
  },
};
