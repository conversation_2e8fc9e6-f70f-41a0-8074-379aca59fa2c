import './index.scss';
import AddIcon from '@mui/icons-material/Add';
import { Button } from '@mui/material';
import React from 'react';

interface EmptyStateProps {
  imageSrc: React.ReactElement;
  title: React.ReactElement;
  description: React.ReactElement;
  buttonText: React.ReactElement;
  onClick: () => void;
}

const EmptyState = ({
  imageSrc,
  title,
  description,
  buttonText,
  onClick,
}: EmptyStateProps) => (
  <div className="empty-state__container">
    <div className="empty-state__icon">{imageSrc}</div>
    <h2>{title}</h2>
    <p>{description}</p>
    <Button
      color="primary"
      variant="contained"
      className="create-case-empty-button"
      onClick={onClick}
      data-testid="add-files-button"
    >
      <AddIcon />
      {buttonText}
    </Button>
  </div>
);
export default EmptyState;
