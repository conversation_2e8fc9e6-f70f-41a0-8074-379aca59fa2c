const SEARCH_VIEW_KEY = 'investigate-search-view';

export enum SearchView {
  Grouped = 'Grouped',
  UnGrouped = 'Un-Grouped',
}

export const isSearchView = (view: string): view is SearchView =>
  Object.values(SearchView).includes(view as SearchView);

export const getSearchViewLocalStorage = () => {
  const viewType = localStorage.getItem(SEARCH_VIEW_KEY);

  if (viewType && isSearchView(viewType)) {
    return viewType;
  }

  return SearchView.UnGrouped;
};

export const setSearchViewLocalStorage = (view: SearchView) => {
  localStorage.setItem(SEARCH_VIEW_KEY, view);
};
