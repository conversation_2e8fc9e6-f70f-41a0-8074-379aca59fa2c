import messages from './messages';
import LOCALES from './locales.tsx';
import { createIntl } from 'react-intl';

const defaultLocale = LOCALES.ENGLISH;
export const LOCALSTORAGE_LANGUAGE_KEY = 'vtn_lang_preference';

export const sagaIntl = () => {
  const storeLanguage =
    window.localStorage?.getItem(LOCALSTORAGE_LANGUAGE_KEY) ??
    window.navigator.language;

  const [locale, localeMessages] = Object.entries(messages).find(([key]) =>
    storeLanguage.includes(key)
  ) || [defaultLocale, messages[defaultLocale]];

  return createIntl({
    locale,
    messages: localeMessages,
  });
};
