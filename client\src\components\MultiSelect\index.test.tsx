import { configureAppStore } from '@store/index';
import { render } from '../../../test/render';
import { describe, expect, it } from 'vitest';
import { Provider } from 'react-redux';
import MultiSelect from '.';
import { screen } from '@testing-library/dom';
import userEvent from '@testing-library/user-event';
import {
  CaseDetailSliceState,
  initialState as CaseDetailInitialState,
} from '@store/modules/caseDetail/slice';
import { CaseTag } from '@shared-types/types';
import { FormProvider, useForm } from 'react-hook-form';
import { ReactElement } from 'react';

const initialStateForMock: {
  caseDetail: CaseDetailSliceState;
} = {
  caseDetail: CaseDetailInitialState,
};

const mockCaseTags: CaseTag[] = [
  { id: 'tag1', label: 'tag1', active: true },
  { id: 'tag2', label: 'tag2', active: false },
  { id: 'tag3', label: 'tag3', active: true },
];

const handleRender = (children: ReactElement) => {
  const Wrapper = () => {
    const methods = useForm();

    return <FormProvider {...methods}>{children}</FormProvider>;
  };

  return render(<Wrapper />);
};

describe('MultiSelect', () => {
  it('should render correctly selected options', () => {
    const store = configureAppStore(initialStateForMock);

    handleRender(
      <Provider store={store}>
        <MultiSelect
          options={mockCaseTags}
          currentIds={['tag1']}
          name="filterCaseTagIds"
        />
      </Provider>
    );

    //  Open the menu
    userEvent.click(screen.getByRole('combobox'));

    //  Test case tags options are there
    expect(screen.getByText('tag1')).toBeInTheDocument();
    expect(screen.getByText('tag3')).toBeInTheDocument();

    //  The tag1 should be selected in the list
    const selected = screen.getByTestId('multi-select-search-option-tag1');
    expect(selected).toHaveAttribute('aria-selected', 'true');
  });

  it('should show the correct number of tags selected', () => {
    const store = configureAppStore(initialStateForMock);
    handleRender(
      <Provider store={store}>
        <MultiSelect
          options={mockCaseTags}
          currentIds={[]}
          name="filterCaseTagIds"
        />
      </Provider>
    );

    //  Open the menu
    userEvent.click(screen.getByRole('combobox'));

    // select 2 options
    userEvent.click(screen.getByText('tag1'));
    userEvent.click(screen.getByText('tag3'));

    //  Check the number of selected tags
    const selectedNumber = screen.getByTestId('multi-select-number-selected');
    expect(selectedNumber).toHaveTextContent('2');
  });
});
