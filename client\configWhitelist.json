["nodeEnv", "apiRoot", "switchAppRoute", "loginRoute", "graphQLEndpoint", "useOAuthGrant", "sentryDSN", "sentry", "veritoneAppId", "registryIds", "registryIds.caseRegistryId", "registryIds.evidenceTypeRegistryId", "registryIds.statusRegistryId", "registryIds.tagRegistryId", "aiwareJSPath", "aiwareJSVersion", "settingsPollInterval", "caseStatusTagsPollInterval", "casePollingInterval", "filePollingInterval"]