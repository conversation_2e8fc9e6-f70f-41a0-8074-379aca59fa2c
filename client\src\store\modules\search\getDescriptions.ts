import { GQLApi } from '@utils/helpers';
import { filter } from 'lodash';

export interface TdoDescription {
  id: string;
  description: string | null;
}

function isTdoDescription(obj: unknown): obj is TdoDescription {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    'id' in obj &&
    'description' in obj
  );
}

export async function getDescriptions(
  tdoIds: string[],
  gql: GQLApi
): Promise<Record<string, string | null>> {
  if (tdoIds.length > 0) {
    const descriptionMapper: Record<string, string | null> = {};
    const result = await gql.getTdoDescriptions(tdoIds);

    filter(result.data.temporalDataObjects.records, isTdoDescription).forEach(
      (folder) => {
        descriptionMapper[folder.id] = folder.description;
      }
    );
    return descriptionMapper;
  }
  return {};
}
