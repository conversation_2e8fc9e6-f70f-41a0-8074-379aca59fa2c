import { Provider } from 'react-redux';
import { render } from '../../../test/render';
import AddToCaseDialog from './index.tsx';
import { expect, vi, it, describe, beforeEach } from 'vitest';
import { act, fireEvent, screen, waitFor } from '@testing-library/react';
import { configureAppStore } from '../../store';
import { GQLApi } from '../../utils/helpers/gqlApi/index.ts';
import {
  CaseDetailSliceState,
  initialState as caseDetailInitialState,
} from '../../store/modules/caseDetail/slice.ts';
import {
  CaseManagerSliceState,
  initialState as CaseManagerState,
} from '../../store/modules/caseManager/slice.ts';
import { SearchSliceState } from '../../store/modules/search/slice.ts';
import { getViewTypeLocalStorage } from '../../utils/local-storage/viewTypes.ts';
import { getSortCategoryLocalStorage } from '../../utils/local-storage/sortCategory.ts';
import { getSearchViewLocalStorage } from '../../utils/local-storage/searchView.ts';
import { getCategoriesLocalStorage } from '../../utils/local-storage/categories.ts';
import { getHideIfNoResultsLocalStorage } from '../../utils/local-storage/hideIfNoResults.ts';
import { getBlurValue } from '../../utils/local-storage/setBlur.ts';

const initialState: {
  caseManager: CaseManagerSliceState;
  caseDetail: CaseDetailSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
  search: SearchSliceState;
} = {
  search: {
    showAddToCaseDialog: true,
    showMoveToCaseDialog: false,
    selectedResults: [{ id: 'file1' }, { id: 'file2' }],
    searchFiles: {
      groupedSearch: {},
      searchParams: undefined,
      ungroupedSearch: {
        status: 'idle',
        pagination: {
          offset: 0,
          limit: 0,
        },
        data: undefined,
      },
    },
    entitySearch: {
      status: 'idle',
      data: undefined,
    },
    librarySearch: {
      status: 'idle',
      data: undefined,
    },
    objectSearch: {
      status: 'idle',
      data: [],
    },
    queryUserLibraries: {
      status: 'idle',
      orgHasLibraries: undefined,
    },
    viewType: getViewTypeLocalStorage(),
    sortCategory: getSortCategoryLocalStorage(),
    searchView: getSearchViewLocalStorage(),
    categories: getCategoriesLocalStorage(),
    hideIfNoResults: getHideIfNoResultsLocalStorage(),
    blur: getBlurValue(),
    sort: {
      type: 'veritone-file.filename',
      order: 'asc',
    },
    moveFileStatus: 'idle',
    unfileFromCaseStatus: 'idle',
    showDeleteFileDialog: false,
    showDeleteSelectedFilesDialog: false,
  },
  caseDetail: {
    ...caseDetailInitialState,
    folders: {
      status: 'complete',
      error: undefined,
      data: {
        results: [
          {
            caseId: 'Current case',
            statusId: '456',
            caseDate: '2025-02-18T09:11:39.133Z',
            caseName: 'Current case',
            folderId: 'folder-000',
            createdBy: '',
            description: '',
            createdDateTime: '2025-02-18T09:11:50.196Z',
            modifiedDateTime: '2025-02-19T09:05:26.333Z',
            preconfiguredTagIds: [],
            id: 'uuid-000',
          },
          {
            caseId: 'Test case 1',
            statusId: '456',
            caseDate: '2025-02-18T09:11:39.133Z',
            caseName: 'Test case 1',
            folderId: 'folder-001',
            createdBy: '',
            description: '',
            createdDateTime: '2025-02-18T09:11:50.196Z',
            modifiedDateTime: '2025-02-19T09:05:26.333Z',
            preconfiguredTagIds: [],
            id: 'uuid-001',
          },
          {
            caseId: 'Test case 2',
            statusId: '456',
            caseDate: '2025-02-18T09:11:39.133Z',
            caseName: 'Test case 2',
            folderId: 'folder-002',
            createdBy: '',
            description: '',
            createdDateTime: '2025-02-18T09:11:50.196Z',
            modifiedDateTime: '2025-02-19T09:05:26.333Z',
            preconfiguredTagIds: [],
            id: 'uuid-002',
          },
        ],
        totalResults: 2,
        limit: 10,
        from: 0,
        to: 2,
      },
    },
    selectedFileId: 'selectedFileId123',
    currentCaseId: 'folder-000',
    showMoveFileDialog: true,
    destinationCase: {
      id: '',
      status: 'loading',
      data: undefined,
    },
  },
  caseManager: {
    ...CaseManagerState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'root-folder',
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: '',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

vi.mock('@i18n', () => ({
  I18nTranslate: {
    TranslateMessage: (msg: string) => msg,
    Intl: () => ({
      formatMessage: ({ defaultMessage }: { defaultMessage: string }) =>
        defaultMessage,
    }),
  },
  sagaIntl: () => ({
    formatMessage: ({ defaultMessage }: { defaultMessage: string }) =>
      defaultMessage,
  }),
}));

describe('AddToCaseDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the AddToCaseDialog', async () => {
    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <AddToCaseDialog />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('add-to-case-dialog')).toBeInTheDocument();
    });
  });

  it('renders and allows selecting a case and confirming', async () => {
    const moveFile = vi.spyOn(GQLApi.prototype, 'moveFile');
    const fileTemporalDataObject = vi.spyOn(
      GQLApi.prototype,
      'fileTemporalDataObject'
    );

    vi.spyOn(GQLApi.prototype, 'getTDOFolder').mockImplementation(
      ({ tdoId }) => {
        if (tdoId === 'file1') {
          return Promise.resolve([
            {
              id: 'folder-000',
            },
          ]);
        } else {
          return Promise.resolve([]);
        }
      }
    );

    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <AddToCaseDialog />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('add-to-case-dialog')).toBeInTheDocument();
      expect(screen.getByText('addToCase')).toBeInTheDocument();
    });

    act(() => {
      fireEvent.mouseDown(screen.getByTestId('ArrowDropDownIcon'));
    });

    await waitFor(() => {
      expect(screen.getByText('Test case 1')).toBeInTheDocument();
    });

    act(() => {
      fireEvent.click(screen.getByText('Test case 1'));
    });

    await waitFor(() => {
      expect(screen.getByTestId('confirm-button')).toBeEnabled();
    });

    act(() => {
      fireEvent.click(screen.getByTestId('confirm-button'));
    });

    await waitFor(() => {
      expect(moveFile).toHaveBeenCalledWith({
        fileId: 'file1',
        oldFolderId: 'folder-000',
        newFolderId: 'folder-001',
      });
      expect(fileTemporalDataObject).toHaveBeenCalledWith({
        folderId: 'folder-001',
        tdoId: 'file2',
      });
    });
  });

  it('renders the dialog and type in the textfield to select menuitem', async () => {
    const moveFile = vi.spyOn(GQLApi.prototype, 'moveFile');
    const fileTemporalDataObject = vi.spyOn(
      GQLApi.prototype,
      'fileTemporalDataObject'
    );
    vi.spyOn(GQLApi.prototype, 'getTDOFolder').mockImplementation(
      ({ tdoId }) => {
        if (tdoId === 'file1') {
          return Promise.resolve([
            {
              id: 'folder-000',
            },
          ]);
        } else {
          return Promise.resolve([]);
        }
      }
    );

    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <AddToCaseDialog />
      </Provider>
    );

    // Wait for the dialog to be rendered
    const inputField = screen.getByRole('textbox');
    await waitFor(() => {
      expect(screen.getByTestId('add-to-case-dialog')).toBeInTheDocument();
      expect(screen.getByText('addToCase')).toBeInTheDocument();
      expect(inputField).toBeInTheDocument();
    });

    // Type in the input field to filter the cases
    act(() => {
      fireEvent.change(inputField, { target: { value: 'Test case 1' } });
    });

    // Wait for the input field to have the value and the menu item to be displayed
    const menuItem = screen.getByText('Test case 1');
    await waitFor(() => {
      expect(inputField).toHaveValue('Test case 1');
      expect(menuItem).toBeInTheDocument();
    });

    // Click on the menu item
    act(() => {
      fireEvent.click(menuItem);
    });

    // Check if the confirm button is enabled
    await waitFor(() => {
      expect(screen.getByTestId('confirm-button')).toBeEnabled();
    });

    // Click the confirm button
    act(() => {
      fireEvent.click(screen.getByTestId('confirm-button'));
    });

    // Verify that the moveFile and fileTemporalDataObject functions were called with the correct parameters
    await waitFor(() => {
      expect(moveFile).toHaveBeenCalledWith({
        fileId: 'file1',
        oldFolderId: 'folder-000',
        newFolderId: 'folder-001',
      });
      expect(fileTemporalDataObject).toHaveBeenCalledWith({
        folderId: 'folder-001',
        tdoId: 'file2',
      });
    });
  });

  it('closes the menu item popper first and then dialog on escape key press', async () => {
    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <AddToCaseDialog />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('add-to-case-dialog')).toBeInTheDocument();
      expect(screen.getByText('addToCase')).toBeInTheDocument();
    });

    // Click the dropdown icon to open the menu
    act(() => {
      fireEvent.mouseDown(screen.getByTestId('ArrowDropDownIcon'));
    });

    // Check if the menu items are displayed
    await waitFor(() => {
      expect(screen.getByText('Test case 1')).toBeInTheDocument();
    });

    // Press the Escape key to close the menu
    act(() => {
      fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' });
    });

    // Check if the dialog is still open
    expect(screen.queryByText('Test case 1')).not.toBeInTheDocument();

    // Press the Escape key again to close the dialog
    act(() => {
      const dialog = screen.getByRole('dialog');
      fireEvent.keyDown(dialog, { key: 'Escape', code: 'Escape' });
    });

    // Check if the dialog is closed
    await waitFor(() => {
      expect(screen.queryByText('add-to-case-dialog')).not.toBeInTheDocument();
      expect(screen.queryByText('addToCase')).not.toBeInTheDocument();
    });
  });

  it('disables confirm button if no case is selected', async () => {
    const store = configureAppStore({
      ...initialState,
      search: { ...initialState.search, selectedResults: [] },
    });

    render(
      <Provider store={store}>
        <AddToCaseDialog />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('add-to-case-dialog')).toBeInTheDocument();
    });

    const confirmBtn = screen.getByTestId('confirm-button');
    expect(confirmBtn).toBeDisabled();
  });
});
