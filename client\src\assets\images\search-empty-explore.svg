<svg width="190" height="190" viewBox="0 0 190 190" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#paint0_angular_8164_109322_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0 0.125 -0.0722749 0 95 65)"><foreignObject x="-1328.26" y="-1328.26" width="2656.52" height="2656.52"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(252, 253, 255, 0.55) 0deg,rgba(242, 246, 254, 1) 180deg,rgba(252, 253, 255, 0.55) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="95" cy="95" r="95" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99166667461395264,&#34;g&#34;:0.99416667222976685,&#34;b&#34;:1.0,&#34;a&#34;:0.55000001192092896},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.95287126302719116,&#34;g&#34;:0.96675604581832886,&#34;b&#34;:0.99915367364883423,&#34;a&#34;:1.0},&#34;position&#34;:0.50}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:1.1379956222148935e-12,&#34;m01&#34;:-144.54975891113281,&#34;m02&#34;:167.27487182617188,&#34;m10&#34;:250.0,&#34;m11&#34;:-9.5284002910034360e-09,&#34;m12&#34;:-60.000003814697266},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<path d="M94.0072 142.041C122.129 142.041 144.927 119.243 144.927 91.0207C144.927 62.7986 122.028 40 94.0072 40C65.8855 40 43.0869 62.7986 43.0869 91.0207C43.0869 119.243 65.8855 142.041 94.0072 142.041Z" fill="#EAEEF9"/>
<path d="M151.392 74.8555C153.666 74.8555 155.51 73.0119 155.51 70.7377C155.51 68.4635 153.666 66.6199 151.392 66.6199C149.118 66.6199 147.274 68.4635 147.274 70.7377C147.274 73.0119 149.118 74.8555 151.392 74.8555Z" fill="#EAEEF9"/>
<path d="M157.418 58.786C158.971 58.786 160.23 57.5269 160.23 55.9738C160.23 54.4207 158.971 53.1616 157.418 53.1616C155.865 53.1616 154.605 54.4207 154.605 55.9738C154.605 57.5269 155.865 58.786 157.418 58.786Z" fill="#EAEEF9"/>
<path d="M45.3966 57.576C46.9498 57.576 48.2088 56.3169 48.2088 54.7638C48.2088 53.2107 46.9498 51.9517 45.3966 51.9517C43.8435 51.9517 42.5845 53.2107 42.5845 54.7638C42.5845 56.3169 43.8435 57.576 45.3966 57.576Z" fill="#F1F3F9"/>
<path d="M28.2226 111.811C31.107 111.811 33.4452 109.473 33.4452 106.588C33.4452 103.704 31.107 101.366 28.2226 101.366C25.3382 101.366 23 103.704 23 106.588C23 109.473 25.3382 111.811 28.2226 111.811Z" fill="#EAEEF9"/>
<g filter="url(#filter0_d_8164_109322)">
<path d="M148.654 56.8693V125.221C148.654 128.213 146.23 130.667 143.237 130.667H44.9895C41.9969 130.667 39.543 128.243 39.543 125.221V56.8693C39.543 53.8767 41.967 51.4527 44.9895 51.4527H143.237C146.23 51.4527 148.654 53.8767 148.654 56.8693Z" fill="url(#paint1_linear_8164_109322)"/>
</g>
<path d="M62.3853 80.3261H54.4256C53.594 80.3261 53 79.6091 53 78.8922C53 78.0558 53.594 77.4583 54.4256 77.4583H62.3853C63.2169 77.4583 63.8109 78.1753 63.8109 78.8922C63.8109 79.7286 63.2169 80.3261 62.3853 80.3261Z" fill="#AAB2C5"/>
<path d="M73.9091 89.7658H54.4256C53.594 89.7658 53 89.0488 53 88.3319C53 87.4955 53.7128 86.898 54.4256 86.898H73.9091C74.7407 86.898 75.3347 87.615 75.3347 88.3319C75.3347 89.0488 74.7407 89.7658 73.9091 89.7658Z" fill="#EAECF3"/>
<path d="M73.9091 99.0861H54.4256C53.594 99.0861 53 98.3691 53 97.6522C53 96.8158 53.7128 96.2183 54.4256 96.2183H73.9091C74.7407 96.2183 75.3347 96.9353 75.3347 97.6522C75.3347 98.3691 74.7407 99.0861 73.9091 99.0861Z" fill="#D6DCE8"/>
<path d="M66.4246 108.406H54.4256C53.594 108.406 53 107.689 53 106.972C53 106.136 53.7128 105.538 54.4256 105.538H66.4246C67.2562 105.538 67.8502 106.255 67.8502 106.972C67.969 107.809 67.2562 108.406 66.4246 108.406Z" fill="#AAB2C5"/>
<path d="M148.654 56.8693V62.5254H39.543V56.8693C39.543 53.8767 41.967 51.4527 44.9895 51.4527H143.237C146.23 51.4527 148.654 53.8767 148.654 56.8693Z" fill="#D5DDEA"/>
<path d="M45.4387 58.4553C46.2651 58.4553 46.935 57.7854 46.935 56.959C46.935 56.1326 46.2651 55.4627 45.4387 55.4627C44.6123 55.4627 43.9424 56.1326 43.9424 56.959C43.9424 57.7854 44.6123 58.4553 45.4387 58.4553Z" fill="#989FB0"/>
<path d="M49.9275 58.4553C50.7538 58.4553 51.4238 57.7854 51.4238 56.959C51.4238 56.1326 50.7538 55.4627 49.9275 55.4627C49.1011 55.4627 48.4312 56.1326 48.4312 56.959C48.4312 57.7854 49.1011 58.4553 49.9275 58.4553Z" fill="#989FB0"/>
<path d="M54.3864 58.4553C55.2128 58.4553 55.8827 57.7854 55.8827 56.959C55.8827 56.1326 55.2128 55.4627 54.3864 55.4627C53.5601 55.4627 52.8901 56.1326 52.8901 56.959C52.8901 57.7854 53.5601 58.4553 54.3864 58.4553Z" fill="#989FB0"/>
<g filter="url(#filter1_d_8164_109322)">
<path d="M123.058 106L115.168 98.1103C114.247 98.8712 113.187 99.4666 111.989 99.8967C110.791 100.327 109.552 100.542 108.271 100.542C105.121 100.542 102.455 99.451 100.273 97.2697C98.091 95.0883 97 92.423 97 89.2738C97 86.1245 98.0907 83.4582 100.272 81.2749C102.453 79.0916 105.119 78 108.268 78C111.417 78 114.084 79.091 116.267 81.2729C118.45 83.4548 119.542 86.1208 119.542 89.2709C119.542 90.587 119.321 91.844 118.879 93.042C118.437 94.24 117.848 95.282 117.11 96.1681L125 104.058L123.058 106ZM108.271 97.7773C110.646 97.7773 112.657 96.9532 114.305 95.3051C115.953 93.657 116.777 91.6456 116.777 89.2709C116.777 86.8962 115.953 84.8848 114.305 83.2366C112.657 81.5885 110.646 80.7645 108.271 80.7645C105.896 80.7645 103.885 81.5885 102.237 83.2366C100.589 84.8848 99.7645 86.8962 99.7645 89.2709C99.7645 91.6456 100.589 93.657 102.237 95.3051C103.885 96.9532 105.896 97.7773 108.271 97.7773Z" fill="url(#paint2_linear_8164_109322)"/>
</g>
<defs>
<clipPath id="paint0_angular_8164_109322_clip_path"><circle cx="95" cy="95" r="95"/></clipPath><filter id="filter0_d_8164_109322" x="17.543" y="40.4527" width="153.11" height="123.214" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="11"/>
<feGaussianBlur stdDeviation="11"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8164_109322"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8164_109322" result="shape"/>
</filter>
<filter id="filter1_d_8164_109322" x="83" y="68" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.665578 0 0 0 0 0.704895 0 0 0 0 0.783529 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8164_109322"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8164_109322" result="shape"/>
</filter>
<linearGradient id="paint1_linear_8164_109322" x1="94.0627" y1="49.6204" x2="94.0627" y2="131.521" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFEFF"/>
<stop offset="0.9964" stop-color="#ECF0F5"/>
</linearGradient>
<linearGradient id="paint2_linear_8164_109322" x1="97.0002" y1="83.25" x2="123.308" y2="93.4725" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0BACC"/>
<stop offset="1" stop-color="#969EAE"/>
</linearGradient>
</defs>
</svg>
