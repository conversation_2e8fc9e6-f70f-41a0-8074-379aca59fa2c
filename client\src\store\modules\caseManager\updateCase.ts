import { InvestigateCase, InvestigateCaseSDO } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function updateCase(
  sdoId: InvestigateCaseSDO['sdoId'],
  schemaId: string,
  investigateCase: InvestigateCase,
  gql: GQLApi
) {
  const result = await gql.updateSDO({
    schemaId,
    id: sdoId,
    data: investigateCase,
  });
  if (!result) {
    throw new Error('case not updated');
  }

  return result;
}
