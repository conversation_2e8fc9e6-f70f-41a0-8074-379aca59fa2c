import createSvgIcon from './lib/createSvgIcon';
export const FileNullState = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="198"
    height="164"
    viewBox="0 0 198 164"
    fill="none"
  >
    <g filter="url(#filter0_d_810_6256)">
      <path
        d="M147.407 123.549L100.577 97.7293L132.295 40.0303C133.327 38.152 135.68 37.4519 137.564 38.4906L171.236 57.056L175.606 72.2515L147.407 123.549Z"
        fill="url(#paint0_linear_810_6256)"
      />
    </g>
    <path
      d="M121.114 96.3315L114.031 92.4258C113.766 92.2801 113.816 91.6888 114.129 91.1191C114.442 90.5493 114.915 90.1912 115.179 90.3369L122.263 94.2426C122.527 94.3883 122.477 94.9796 122.164 95.5493C121.799 96.214 121.379 96.4772 121.114 96.3315Z"
      fill="#D5DDEA"
    />
    <path
      d="M153.888 62.4026L136.252 52.6788C135.842 52.4527 135.755 51.7768 136.065 51.2144C136.374 50.652 136.99 50.3638 137.4 50.5899L155.037 60.3137C155.447 60.5398 155.533 61.2157 155.224 61.7781C154.915 62.3405 154.298 62.6288 153.888 62.4026Z"
      fill="#D6DCE8"
    />
    <path
      d="M139.526 60.8355L133.487 57.5061C133.123 57.3052 133.076 56.6509 133.385 56.0885C133.694 55.5261 134.271 55.2163 134.636 55.4172L140.622 58.7179C140.987 58.9188 141.034 59.5731 140.725 60.1355C140.416 60.6979 139.838 61.0077 139.526 60.8355Z"
      fill="#D6DCE8"
    />
    <path
      d="M151.15 97.4664L130.008 85.8098L127.678 84.5251L120.631 80.6395C120.347 80.4828 119.823 80.7614 119.488 81.3706C119.201 81.8929 119.142 82.5404 119.483 82.7284L126.53 86.614L128.86 87.8987L150.001 99.5553C150.286 99.712 150.809 99.4335 151.144 98.8242C151.422 98.1836 151.434 97.623 151.15 97.4664Z"
      fill="#D6DCE8"
    />
    <path
      d="M154.211 91.8954L146.354 87.5634L143.678 86.088L123.694 75.0697C123.41 74.9127 122.886 75.1909 122.551 75.8002C122.264 76.3224 122.204 76.9702 122.546 77.1586L142.53 88.1769L145.206 89.6523L153.063 93.9843C153.348 94.1413 153.872 93.863 154.206 93.2538C154.484 92.6131 154.496 92.0523 154.211 91.8954Z"
      fill="#D6DCE8"
    />
    <path
      d="M156.943 87.0497L154.901 85.9239L152.859 84.7981L126.372 70.1943C126.088 70.0379 125.565 70.3168 125.231 70.9261C124.943 71.4483 124.883 72.0956 125.224 72.2832L151.711 86.887L153.979 88.1379L155.738 89.1073C156.021 89.2637 156.544 88.9848 156.879 88.3756C157.223 87.8846 157.226 87.206 156.943 87.0497Z"
      fill="#D6DCE8"
    />
    <path
      d="M159.959 81.4546L153.38 77.8275L151.749 76.9284L129.428 64.621C129.146 64.466 128.626 64.7462 128.291 65.3555C128.004 65.8777 127.942 66.524 128.279 66.71L150.601 79.0174L152.232 79.9164L158.754 83.5125C159.148 83.7295 159.612 83.4183 159.947 82.8091C160.234 82.2868 160.296 81.6406 159.959 81.4546Z"
      fill="#D6DCE8"
    />
    <path
      d="M171.012 57.465L168.508 62.0193C166.912 64.9232 167.97 68.5743 170.872 70.1742L175.381 72.6605"
      fill="#D5DDEA"
    />
    <g filter="url(#filter1_d_810_6256)">
      <path
        d="M118.922 88.2905L66.1253 79.896L76.4373 14.8456C76.773 12.728 78.7494 11.2707 80.8734 11.6084L118.836 17.6443L128.09 30.4569L118.922 88.2905Z"
        fill="url(#paint1_linear_810_6256)"
      />
    </g>
    <path
      d="M118.762 18.1053L117.947 23.2418C117.429 26.5146 119.661 29.5896 122.934 30.11L128.016 30.918"
      fill="#D5DDEA"
    />
    <path
      d="M89.6798 48.4894C88.9904 48.3798 88.4447 48.0478 88.0428 47.4934C87.641 46.939 87.4948 46.3166 87.6042 45.6261L90.0712 30.064C90.1806 29.3736 90.5121 28.8271 91.0657 28.4246C91.6192 28.0221 92.2407 27.8757 92.9302 27.9853L108.469 30.4559C109.158 30.5656 109.704 30.8976 110.106 31.4519C110.508 32.0063 110.654 32.6288 110.545 33.3193L109.553 39.5757L115.119 35.5288L113.11 48.1992L109.069 42.6249L108.078 48.8813C107.968 49.5718 107.637 50.1183 107.083 50.5208C106.53 50.9232 105.908 51.0697 105.219 50.9601L89.6798 48.4894Z"
      fill="url(#paint2_linear_810_6256)"
    />
    <g filter="url(#filter2_d_810_6256)">
      <path
        d="M95.3643 102.654L45.2513 121.294L22.3536 59.5494C21.6082 57.5394 22.616 55.2982 24.6321 54.5483L60.6648 41.1457L75.0069 47.7596L95.3643 102.654Z"
        fill="url(#paint3_linear_810_6256)"
      />
    </g>
    <path
      d="M60.8262 41.5834L62.6339 46.4579C63.7861 49.5649 67.2379 51.1471 70.3437 49.9918L75.1683 48.1973"
      fill="#D5DDEA"
    />
    <path
      d="M76.8916 82.0276L65.8242 76.1095C63.7519 75.0013 61.1712 75.9856 60.2964 78.218L56.411 88.1322L54.2758 86.9986C52.191 85.8918 49.6022 86.8978 48.7444 89.1481L47.0592 93.5686C45.8266 96.8017 48.9025 99.9571 52.0905 98.7299L66.1664 93.3114L76.3958 89.3273C79.5783 88.0879 79.8813 83.6263 76.8916 82.0276Z"
      fill="#D6DCE8"
    />
    <path
      d="M48.2003 80.3835C50.7914 79.0182 51.868 76.0185 50.6471 73.5661C49.4262 71.1136 46.431 70.2594 43.8399 71.6247C41.2488 72.99 40.1722 75.9897 41.3931 78.4421C42.614 80.8946 45.6092 81.7488 48.2003 80.3835Z"
      fill="#D6DCE8"
    />
    <g filter="url(#filter3_d_810_6256)">
      <path
        d="M135.266 131H74.7188V56.4002C74.7188 53.9718 76.6713 51.9901 79.107 51.9901H122.642L135.266 64.6765V131Z"
        fill="url(#paint4_linear_810_6256)"
      />
    </g>
    <path
      d="M122.643 52.5189V59.2053C122.643 62.5191 125.329 65.2053 128.643 65.2053H135.266"
      fill="#D5DDEA"
    />
    <path
      d="M96.5882 76.9678C99.1462 75.3026 102.088 74.406 105.413 74.406C114.238 74.406 121.401 81.5791 121.401 90.4174C121.401 99.2557 114.238 106.429 105.413 106.429C96.5882 106.429 89.4258 99.2557 89.4258 90.4174C89.4258 84.7814 92.2396 79.9139 96.5882 76.9678Z"
      fill="url(#paint5_linear_810_6256)"
    />
    <path
      d="M106.532 82.3457L111.324 87.0488C111.971 87.6844 111.971 88.5742 111.324 89.2098C110.676 89.8453 109.77 89.8453 109.122 89.2098L106.92 87.0488V97.2179C106.92 97.9806 106.273 98.7433 105.366 98.7433C104.589 98.7433 103.812 98.1077 103.812 97.2179V87.0488L101.61 89.2098C100.962 89.8453 100.056 89.8453 99.4081 89.2098C99.149 88.9555 99.0195 88.5742 99.0195 88.1929C99.0195 87.8115 99.149 87.4302 99.4081 87.176L104.2 82.4728C104.459 82.2185 104.848 82.0914 105.236 82.0914C105.625 82.0914 106.273 82.0914 106.532 82.3457Z"
      fill="white"
    />
    <defs>
      <filter
        id="filter0_d_810_6256"
        x="78.5762"
        y="27.0092"
        width="119.029"
        height="129.54"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="11" />
        <feGaussianBlur stdDeviation="11" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_810_6256"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_810_6256"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_d_810_6256"
        x="44.125"
        y="0.559814"
        width="105.965"
        height="120.731"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="11" />
        <feGaussianBlur stdDeviation="11" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_810_6256"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_810_6256"
          result="shape"
        />
      </filter>
      <filter
        id="filter2_d_810_6256"
        x="0.107422"
        y="30.1458"
        width="117.256"
        height="124.148"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="11" />
        <feGaussianBlur stdDeviation="11" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_810_6256"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_810_6256"
          result="shape"
        />
      </filter>
      <filter
        id="filter3_d_810_6256"
        x="52.7188"
        y="40.9901"
        width="104.547"
        height="123.01"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="11" />
        <feGaussianBlur stdDeviation="11" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_810_6256"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_810_6256"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_810_6256"
        x1="158.347"
        y1="48.1073"
        x2="123.535"
        y2="111.246"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FDFEFF" />
        <stop offset="0.9964" stopColor="#ECF0F5" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_810_6256"
        x1="103.68"
        y1="13.6009"
        x2="92.3556"
        y2="84.828"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FDFEFF" />
        <stop offset="0.9964" stopColor="#ECF0F5" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_810_6256"
        x1="88.8258"
        y1="37.8455"
        x2="114.121"
        y2="41.8553"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#B0BACC" />
        <stop offset="1" stopColor="#969EAE" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_810_6256"
        x1="45.4792"
        y1="45.0728"
        x2="70.6188"
        y2="112.66"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FDFEFF" />
        <stop offset="0.9964" stopColor="#ECF0F5" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_810_6256"
        x1="104.972"
        y1="50.1625"
        x2="104.972"
        y2="131.852"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FDFEFF" />
        <stop offset="0.9964" stopColor="#ECF0F5" />
      </linearGradient>
      <linearGradient
        id="paint5_linear_810_6256"
        x1="89.4111"
        y1="90.4209"
        x2="121.407"
        y2="90.4209"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#B0BACC" />
        <stop offset="1" stopColor="#969EAE" />
      </linearGradient>
    </defs>
  </svg>,
  'FileNullState'
);
