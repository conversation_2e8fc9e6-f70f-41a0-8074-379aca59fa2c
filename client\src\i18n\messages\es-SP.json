{">": ">", "active": "Activo", "add": "Agregar", "addFiles": "Agregar archivos", "addNewCase": "<PERSON><PERSON>r nuevo caso", "addNewStatus": "Agregar nuevo estado", "addNewTag": "Agregar nueva etiqueta", "addStatus": "Agregar estado", "addTag": "Agregar etiqueta", "addTags": "Agregar etiquetas", "addToShare": "Agregar a compartir", "advancedSearch": "Búsqueda avanzada", "areYouSure": "¿Estás seguro?", "areYouSureCloseAndLoseData": "¿Estás seguro de que deseas cerrar el panel y perder tus datos?", "back": "Atrás", "backToCaseManager": "Volver al gestor de casos", "belowYouCanFindKeySettings": "A continuación puedes encontrar los ajustes clave que se pueden gestionar para tu aplicación.", "cancel": "<PERSON><PERSON><PERSON>", "case": "Caso", "caseFiles": "Archivos del caso", "caseCreatedSuccessfully": "¡Caso creado con éxito!", "caseCreationFailed": "¡Error al crear el caso!", "caseDate": "Fecha del caso", "caseDateWithColons": "Fecha del caso:", "caseID": "ID del caso", "caseIDWithAsterisk": "ID del caso *", "caseIdWithColons": "ID del caso: ", "caseInformation": "Información del caso", "caseManagement": "Gestión de casos", "caseManager": "<PERSON><PERSON><PERSON> de casos", "caseName": "Nombre del caso", "caseNameWithAsterisk": "Nombre del caso *", "caseOwner": "Propietario del caso", "caseOwnerWithColons": "Propietario del caso:", "caseRetentionPolicy": "Política de retención del caso", "caseStatus": "Estado del caso", "chooseACase": "Elegir un caso", "clearAll": "<PERSON><PERSON><PERSON> todo", "clearStatus": "Limpiar solo estado", "close": "<PERSON><PERSON><PERSON>", "closePanel": "Cerrar panel", "cognition": "Cognición", "confirmClosePanel": "Confirmar cierre del panel", "createCase": "<PERSON><PERSON><PERSON> caso", "createNewCase": "<PERSON><PERSON>r nuevo caso", "createNewShare": "Crear nuevo recurso compartido", "confirmByTyping": "Confirma escribiendo", "below": "abajo.", "deleteFilesConfirmationMsg": "Eliminar eliminará inmediatamente el contenido de tu organización. Se pueden recuperar hasta por 30 días.", "deleteFile": "eliminar-archivo", "everyOneInOrgWillLoseAccess": "Todos en la organización perderán el acceso", "theFileWillBeDeleted": "El archivo será eliminado", "allSelectedFilesWillBeDeleted": "Todo el contenido seleccionado será eliminado", "dashboard": "Panel de control", "delete": "Eliminar", "deleteAndReAssign": "<PERSON>asi<PERSON><PERSON> y eliminar", "deleteAndReAssignStatusDescription": "Hay casos que usan esta etiqueta, limpia el estado{br}o elige un nuevo estado para asignarles", "deleteCaseConfirmationMsg": "Eliminar un caso elimina todos los archivos y datos y no se puede deshacer", "deleteOnlyStatusDescription": "Hay casos que usan esta etiqueta, elige un nuevo{br}estado para asignarles", "deleteStatus": "Eliminar etiqueta de estado", "deleteStatusDescription": "¿Estás seguro de que deseas eliminar esta etiqueta de estado{br}? Esta acción no se puede deshacer.", "deleteTag": "Eliminar etiqueta", "deleteTagDescription": "¿Estás seguro de que deseas eliminar la etiqueta {tag} tag?", "deleteTags": "Eliminar etiquetas", "deleteTagsDescription": "¿Estás seguro de que deseas eliminar las etiquetas seleccionadas?", "description": "Descripción", "editCase": "<PERSON><PERSON> caso", "editRetention": "Editar retención", "editStatusLabels": "Editar etiquetas de estado", "editTags": "Editar etiquetas", "evidenceData": "Datos de evidencia", "evidenceType": "Tipo de evidencia", "evidenceTypesWithColon": "Tipos de evidencia:", "fileOwner": "Propietario del archivo", "fileStatus": "Estado del archivo", "fileUploader": "Subidor de archivos", "goToNewCase": "Ir al nuevo caso", "inactive": "Inactivo", "keyword": "Palabra clave", "move": "Mover", "nameStatusAndAssignColor": "Nombra tu estado y asigna un color.", "nameTagAndChooseVisibility": "Agrega hasta diez etiquetas a la vez. Las etiquetas tienen un máximo de quince caracteres. Sepáralas con una coma.", "noCaseSelected": "<PERSON><PERSON><PERSON> caso seleccionado", "noCasesFoundDescription": "No has creado ningún caso. Haz clic en el botón Agregar nuevo caso para comenzar.", "noFilesFound": "No se encontraron archivos del caso", "noFilesFoundDescription": "Sube archivos a tu caso para ver los resultados de cognición{br}y compartir hallazgos.", "nothingFound": "<PERSON>da encontrado", "overview": "Resumen", "permissions": "<PERSON><PERSON><PERSON>", "pleaseSelectCase": "Elige un caso de la tabla para ver sus detalles aquí.", "setVisibility": "Establecer visibilidad", "setColor": "Establecer color", "presetColors": "Colores predefinidos:", "color": "Color", "resetAll": "Restablecer todo", "resetFilters": "Restablecer filtros", "retentionDate": "Fecha de retención", "save": "Guardar", "saveChanges": "Guardar cambios", "search": "Buscar", "selectAStatus": "Seleccionar un estado", "selectStoredLocation": "Selecciona la ubicación donde se almacenará este contenido.", "emptyState": "Estado vacío", "settings": "Configuraciones", "share": "Compartir", "shareCase": "Compartir caso", "shares": "Compartidos", "someThingWentWrong": "Algo salió mal...", "statusName": "Nombre del estado", "statuses": "Estados", "tagName": "Nombre de la etiqueta(s)", "tags": "Etiquetas", "unableToLoadData": "No se encontraron datos o no se pudieron cargar.{br}Intenta actualizar esta página o vuelve más tarde.", "unableToUploadFile": "No se pudo subir el archivo", "uploadFileSuccess": "Archivo {name} subido con éxito", "refreshPage": "Actualiza la página e inténtalo de nuevo", "summary": "Resumen", "NoResults": "Sin resultados", "NoEngineRun": "Sin ejecución de motor", "uploadDate": "Fecha de subida", "uploadFile(s)": "Subir archivo(s)", "uploadFiles": "Subir archivos", "caseDetail": "Detalle del caso", "viewCaseDetails": "Ver detalles del caso", "yesDeleteCase": "Sí, eliminar caso", "youAreAboutDeleteCase": "Estás a punto de eliminar un caso", "yesDeleteFiles": "Sí, eliminar archivos", "youCanStartAddingFiles": "Puedes empezar a agregar archivos e información del caso.", "selectAFolder": "Mover al caso", "chooseCase": "Elige un caso activo para mover el(los) archivo(s).", "chooseCaseAddFiles": "Choose an active case to add file(s) to.", "selectACasePlaceholder": "Seleccionar un caso", "casePendingDelete": "Este caso está pendiente de eliminación y no se puede seleccionar.", "caseDeletedSuccessfully": "Eliminación del caso en progreso. Se eliminará en breve", "caseDeletionFailed": "¡Error al eliminar el caso!", "caseUpdatedSuccess": "¡Caso actualizado con éxito!", "caseUpdateFailed": "¡Error al actualizar el caso!", "searchPage": "Buscar y gestionar archivos", "fileType": "Tipo de archivo", "defaultEmpty": "--", "wordsInTranscription": "Palabras en la transcripción", "faceDetections": "Detecciones faciales", "objectDescriptors": "Descriptores de objetos", "vehicleRecognition": "Reconocimiento de vehículos", "licensePlateRecognition": "Reconocimiento de matrículas", "sceneClassification": "Clasificación de escenas", "textRecognition": "Reconocimiento de texto", "callRecording": "Grabación de llamada 911", "arrestReport": "Informe de arresto", "bodyWornCamera": "<PERSON><PERSON><PERSON> corporal", "bookingPhoto": "Foto de ficha", "citizenSubmittedVideo": "Vídeo enviado por ciudadano", "crimeScenePhoto": "Foto de la escena del crimen", "inCarVideo": "Vídeo en el coche", "interviewAudioRecording": "Grabación de audio de entrevista", "interviewRoomRecording": "Grabación de sala de entrevistas", "mobileDeviceExtraction": "Extracción de dispositivo móvil", "securityCameraVideo": "Vídeo de cámara de seguridad", "video": "Vídeo", "audio": "Audio", "document": "Documento", "image": "Imagen", "title": "<PERSON><PERSON><PERSON><PERSON>", "noResultsFound": "No se encontraron resultados", "refineAndTryAgain": "Refina tu término de búsqueda e inténtalo de nuevo.", "confirmDiscardChanges": "¿Estás seguro de que deseas descartar tus cambios?", "addStatusConfirm": "Confirmación de agregar estado", "addTagConfirm": "Confirmación de agregar etiqueta", "wouldYouLikeToSaveYourChanges": "¿Te gustaría guardar tus cambios?", "wouldYouLikeToSaveYourChangesBeforeClosing": "¿Te gustaría guardar tus cambios antes de cerrar?", "selectedTagsWillAllBeSetToThisOption": "Las etiquetas seleccionadas se establecerán en esta opción.", "selectedStatusesWillAllBeSetToThisOption": "Los estados seleccionados se establecerán en esta opción.", "setTagVisibility": "Establecer visibilidad de la etiqueta", "setStatusVisibility": "Establecer visibilidad del estado", "fileName": "Nombre del archivo", "filename": "Nombre del archivo", "sourceName": "Nombre de la fuente", "caseId": "ID del caso", "dateUploaded": "Fecha de subida", "selectUpToTenTags": "Selecciona hasta 10 etiquetas preconfiguradas", "tagsSelected": "Etiquetas seleccionadas", "filePendingDelete": "Este archivo está pendiente de eliminación y no se puede seleccionar.", "fileDeletedSuccessfully": "Eliminación del archivo en progreso. Se eliminará en breve.", "fileDeletionFailed": "Error al eliminar el archivo.", "yesDeleteFile": "Sí, eliminar archivo", "deleteFileConfirmationMsg": "Eliminar un archivo no se puede deshacer", "viewCase": "Ver caso", "uploaded": "Subido", "uploadedWithColons": "Subido:", "viewFile": "Ver archivo", "addToCase": "Agregar al caso", "moveToAnotherCase": "Mover a otro caso", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "Archivo", "files": "Archivos", "addTagSuccess": "Etiqueta agregada con éxito", "addTagFailure": "Error al agregar etiqueta", "saveTagsSuccess": "Etiquetas guardadas con éxito", "saveTagsFailure": "Error al guardar etiquetas", "addStatusSuccess": "Estado agregado con éxito", "addStatusFailure": "Error al agregar estado", "saveStatusesSuccess": "Estados guardados con éxito", "saveStatusesFailure": "Error al guardar estados", "filters": "<PERSON><PERSON><PERSON>", "reset": "Restablecer", "status": "Estado", "apply": "Aplicar", "showAll": "<PERSON><PERSON> todo", "allowedLetters": "Solo se <PERSON>en letras, n<PERSON><PERSON><PERSON>, _ y -", "saveStatusesConcurrentModificationError": "Los estados fueron modificados por otro usuario. Puede que necesites actualizar la página.", "saveTagsConcurrentModificationError": "Las etiquetas fueron modificadas por otro usuario. Puede que necesites actualizar la página.", "saveModifiedStatusesMessage": "Los estados fueron modificados por otro usuario. <PERSON><PERSON><PERSON> 'Descartar' tus cambios o 'Guardar', sobrescribiendo sus datos.", "saveModifiedTagsMessage": "Las etiquetas fueron modificadas por otro usuario. <PERSON><PERSON><PERSON> 'Descartar' tus cambios o 'Guardar', sobrescribiendo sus datos.", "selectACategory": "Selecciona una categoría para comenzar", "dataModified": "<PERSON><PERSON> modificados", "discardChanges": "Descartar cambios", "discard": "Descar<PERSON>", "fileMovedSuccess": "Archivo movido con éxito", "invalidCharacterErrorMessage": "<PERSON><PERSON><PERSON>, solo se permiten \"-\" y \"_\".", "statusLabelNameAlreadyTaken": "El nombre de la etiqueta de estado ya está en uso.", "editMetadata": "Editar metada<PERSON>", "editMetadataWarning": "Cualquier cambio en los metadatos del recurso es permanente y no se puede deshacer.", "fileSize": "Tamaño del archivo", "fileFormat": "Formato del archivo", "fileId": "ID del archivo", "duration": "Duración", "source": "Fuente", "confirmSaveChanges": "Confirmación de cambios", "fileNameRequired": "El nombre del archivo es obligatorio", "caseNameRequired": "El nombre del caso es obligatorio", "showTotalResults": "Mostrando {total} resultados", "sortCategory": "Categoría de orden : ", "view": "Vista :", "recentlyUploaded": "Subido recientemente", "grouped": "Agrupado", "unGrouped": "No agrupado", "showAllCategories": "Mostrar todas las categorías", "hideIfNoResults": "Ocultar si no hay resultados", "transcription": "Transcripción", "faceRecognition": "Reconocimiento facial", "objectDetection": "Detección de objetos", "metadata": "Metadatos", "filterSearchWarning": "Las búsquedas por palabra clave pueden tardar un poco en cargar.", "no": "No", "found": "Encontrado", "reAssignTo": "Reasignar a:", "noOptionsLeft": "¡No quedan opciones!", "noDataAvailable": "No hay datos disponibles", "lightMode": "<PERSON>do claro", "darkMode": "<PERSON><PERSON> oscuro", "space": " ", "blurImages": "Desenfocar imá<PERSON>s", "all": "Todo", "none": "<PERSON><PERSON><PERSON>", "results": "Resul<PERSON><PERSON>", "cadId": "ID CAD", "callerPhoneNumber": "Número de teléfono del llamante", "reportNumber": "Número de informe", "officerName": "Nombre del oficial", "badgeId": "ID de placa", "deviceId": "ID del dispositivo", "deviceNumber": "Número de dispositivo", "cameraPhysicalLocation": "Ubicación física de la cámara", "cameraPhysicalAddress": "Dirección física de la cámara", "locationTimeline": "Cronología de ubicación", "lastName": "Apellido", "firstName": "Nombre", "dateOfBirth": "Fecha de nacimiento", "citizenName": "Nombre del ciudadano", "cameraType": "Tipo <PERSON>", "evidenceTechnician": "Técnico de evidencia", "unitNumber": "Número de unidad", "interviewer": "Entrevistador", "interviewee": "Entrevistado", "interviewRoom": "Sala de entrevistas", "deviceName": "Nombre del dispositivo", "deviceType": "Tipo de dispositivo", "deviceModel": "Modelo de dispositivo", "deviceRegisteredOwner": "Propietario registrado del dispositivo", "cameraFacingDirection": "Dirección de la cámara", "metadataUpdated": "Metadatos actualizados", "assetStatus": "Estado del recurso", "createdBy": "<PERSON><PERSON>o por", "contentType": "Tipo de contenido", "contentTypesWithColon": "Tipos de contenido:", "failedToUpdateMetadata": "Error al actualizar metadatos", "case-evidence-list-item": "{type} ({count})", "foundString": "Resul<PERSON><PERSON>", "foundFullTextString": "Texto sin formato", "libraries": "Bibliotecas", "entities": "Entidades", "getLibrariesFailed": "Error al obtener bibliotecas", "getEntitiesFailed": "Error al obtener entidades", "enteredMoreThanTenTags": "Has introducido más de 10 etiquetas. Por favor, introduce hasta 10 etiquetas.", "invalidCharacterInTag": "Carácter inválido en {tag}. Solo se permiten letras, números, _ y -.", "tagNameIsTooLong": "La etiqueta {tag} es demasiado larga. Introduce una etiqueta de máximo 15 caracteres.", "tagNameAlreadyTaken": "Las siguientes etiquetas ya existen: {tags}", "startExploring": "Comenzar a explorar", "searchForAnything": "Busca cualquier cosa. Te ayudaremos a encontrarlo.", "clickTheCategoryWheel": "Haz clic en la rueda de categorías para elegir categorías de búsqueda.", "invalidCase": "El caso ha sido eliminado o no existe. Selecciona otro caso.", "noneOperatorWarning": "Por favor, selecciona un operador antes de seleccionar uno nuevo", "somethingWrongSearch": "Algo salió mal en tu solicitud de búsqueda. ({gqlRequestId})", "metadataCouldNotBeFound": "No se pudieron encontrar los metadatos", "failedToRetrieveMetadata": "Error al recuperar metadatos", "noRootFolder": "No se ha creado ninguna carpeta raíz. Por favor, contacta con tu administrador.", "removeOperator": "Eliminar operador", "invalidExpression": "Expresión inválida", "lackOfOpenParenthesis": "Falta un paréntesis de apertura", "lackOfCloseParenthesis": "Falta un paréntesis de cierre", "redundantParenthesis": "Paréntesis redundante", "shareName": "Compartir nombre", "message": "Men<PERSON><PERSON>", "expirationDate": "Fecha de expiración", "recipients": "Des<PERSON><PERSON><PERSON>", "addRecipient": "Agregar destinatario", "youAreAboutToShareSensitiveContent": "Está a punto de compartir contenido y archivos confidenciales con estos destinatarios", "emailAddresses": "Direcciones de correo electrónico", "createShare": "<PERSON><PERSON><PERSON>", "createShareSubtitle": "Seleccione los archivos a los que desea que los destinatarios tengan acceso y elija cuándo expira el recurso compartido.", "shareNameRequired": "Compartir El nombre es obligatorio", "messageRequired": "El mensaje es obligatorio", "expirationDateRequired": "La fecha de vencimiento es obligatoria", "emailRequired": "Se requiere correo electrónico", "firstNameRequired": "El nombre es obligatorio", "lastNameRequired": "El apellido es obligatorio", "expirationDateNotGreaterThanNow": "La fecha de vencimiento debe ser mayor que la fecha actual", "emailBadFormat": "El correo electrónico no es un formato válido", "loading": "Cargando...", "cognitionFiltersNotAvailable": "Face and object search requires cognition libraries to exist in the organization.", "failedGetCaseSchemaId": "Failed to get case schema ID", "failedGetStatusSchemaId": "Failed to get status schema ID", "failedGetTagSchemaId": "Failed to get tag schema ID", "failedGetEvidenceTypeSchemaId": "Failed to get evidenceType schema ID", "noStatus": "Sin estado", "caseTags": "Etiquetas de caso", "caseTagSettings": "Configuración de Etiquetas de Caso", "password": "Contraseña (Opcional)", "showPassword": "Mostrar contraseña", "rowPerPage": "<PERSON>las por página", "visibility": "Visibilidad"}