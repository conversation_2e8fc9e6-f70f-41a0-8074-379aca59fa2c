import { deepOrange, orange } from '@mui/material/colors';
import { createTheme } from '@mui/material/styles';
// import { defaultThemeProps } from './defaultThemeProps';
import { defaultOverrides } from './defaultThemeOverrides';
import { sharedTypography } from './sharedTypography';
import { type VeritoneThemeOptions } from './themeInterfaces';

// these colors still need to be set
const colors = {
  primaryMain: orange[500],
  secondaryMain: deepOrange[900],
  textPrimary: '#D8D8D8',
  textSecondary: '#5C6269',
  tableheadColor: '#465364',
  tableRowSelectedBackground: '#F2F5F9',
  buttonContainedPrimaryBackgroundColor: '#1871E8',
  buttonContainedPrimaryHoverBackground: '#0D62D2',
  buttonDisabledColor: '#afafaf',
  buttonDisabledBackground: '#e4e4e4',
  buttonOutlinedBackground: '#9e9e9e',
  linkColor: '#1565c0',
  iconRootColor: '#555F7C',
  iconRootHover: '#1B1D1F',
  iconPrimaryHover: '#0D62D2',
  backgroundColor: '#101010',
  secondaryBackgroundColor: '#171717',
  backgroundPaper: '#D5DFE9',
  divider: '#9e9e9e',
};

export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: colors.primaryMain,
    },
    secondary: {
      main: colors.secondaryMain,
    },
    text: {
      primary: colors.textPrimary,
      secondary: colors.textSecondary,
    },
    background: {
      default: colors.backgroundColor,
    },
    divider: colors.divider,
    button: {
      containedPrimaryHoverBackground:
        colors.buttonContainedPrimaryHoverBackground,
      containedPrimaryBackgroundColor:
        colors.buttonContainedPrimaryBackgroundColor,
      disabledColor: colors.buttonDisabledColor,
      disabledBackground: colors.buttonDisabledBackground,
      outlineBackground: colors.buttonOutlinedBackground,
    },
  },
  spacing: 5,
  typography: sharedTypography(),
  overrides: {
    MuiDivider: {
      root: {
        background: colors.divider,
      },
    },
  },
} as VeritoneThemeOptions);

darkTheme.components = defaultOverrides(colors);

export default darkTheme;
