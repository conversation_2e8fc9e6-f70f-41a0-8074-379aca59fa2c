import LOCALES from './locales';
import messages from './messages';
import { IntlProvider } from 'react-intl';
import { Fragment, ReactNode } from 'react';

const DEFAULT_LOCALE = LOCALES.ENGLISH;

const Provider = ({ children, locale }: Props) => {
  const [_, localeMessages] = Object.entries(messages).find(([key]) =>
    locale.includes(key)
  ) || [undefined, messages[DEFAULT_LOCALE]];

  return (
    <IntlProvider
      locale={locale}
      textComponent={Fragment}
      messages={localeMessages}
      defaultLocale={DEFAULT_LOCALE}
    >
      {children}
    </IntlProvider>
  );
};

interface Props {
  readonly children: ReactNode | undefined;
  readonly locale: string;
}

export default Provider;
