import './index.scss';
import Dialog from '@components/Dialog';
import { I18nTranslate } from '@i18n';
import {
  FormControlLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
} from '@mui/material';
import { OptionsType } from '@pages/Settings';
import { useAppDispatch } from '@store/hooks';
import {
  selectRowSelection,
  selectSearchCases,
  StatusTagRow,
  updateReassignStatuses,
} from '@store/modules/settings/slice';
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

enum DeleteStatusOptions {
  deleteOnly = 'delete-only',
  deleteAndReassign = 'delete-and-reassign',
}

interface Props {
  open: boolean;
  selectedTab: OptionsType;
  singleRow?: StatusTagRow | null;
  rows: StatusTagRow[];
  onClose: () => void;
  removeSelectedRow?: () => void;
  handleDeleteRows: (rowId: string[]) => void;
}

const DeleteDialog = ({
  open,
  selectedTab,
  singleRow,
  rows,
  onClose,
  removeSelectedRow,
  handleDeleteRows,
}: Props) => {
  const dispatch = useAppDispatch();
  const intl = I18nTranslate.Intl();

  const [deleteStatusOption, setDeleteStatusOption] =
    useState<DeleteStatusOptions>(DeleteStatusOptions.deleteOnly);
  const [reassignStatusId, setReassignStatusId] = useState<string | null>(null);

  const searchCases = useSelector(selectSearchCases);
  const rowSelection = useSelector(selectRowSelection);

  const selectedRowIds = Object.keys(rowSelection);

  const renderDeleteTagContent = () => (
    <span className="delete-dialog__description">
      {singleRow
        ? I18nTranslate.TranslateMessage('deleteTagDescription', {
            tag: <strong>{singleRow?.name}</strong>,
          })
        : I18nTranslate.TranslateMessage('deleteTagsDescription')}
    </span>
  );

  const unSelectedRow = useMemo(() => {
    if (singleRow) {
      return rows.filter((row) => row.id !== singleRow.id);
    } else {
      return rows.filter((row) => !rowSelection[row.id]);
    }
  }, [rows, singleRow]);

  const renderDeleteStatusContent = () => {
    if (!searchCases.totalResults) {
      return (
        <span className="delete-dialog__description">
          {I18nTranslate.TranslateMessage('deleteStatusDescription', {
            br: <br key="deleteStatusDescriptionBr" />,
          })}
        </span>
      );
    }

    const isDeleteStatusOption = (
      value: string
    ): value is DeleteStatusOptions => {
      const optionSet = new Set<string>(Object.values(DeleteStatusOptions));
      return optionSet.has(value);
    };

    const handleChangeRadio = (
      _e: React.ChangeEvent<HTMLInputElement>,
      value: string
    ) => {
      if (isDeleteStatusOption(value)) {
        setDeleteStatusOption(value);
        if (value === DeleteStatusOptions.deleteAndReassign) {
          setReassignStatusId(unSelectedRow[0]?.id || null);
        }
      }
    };

    return (
      <Stack spacing={4} mb={2} className="delete-dialog__content">
        <p className="delete-dialog__description">
          {!searchCases.totalResults
            ? I18nTranslate.TranslateMessage('deleteStatusDescription', {
                br: <br key="deleteStatusDescriptionBr" />,
              })
            : deleteStatusOption === DeleteStatusOptions.deleteOnly
              ? I18nTranslate.TranslateMessage('deleteOnlyStatusDescription', {
                  br: <br key="deleteOnlyStatusDescriptionBr" />,
                })
              : I18nTranslate.TranslateMessage(
                  'deleteAndReAssignStatusDescription',
                  {
                    br: <br key="deleteAndReAssignStatusDescriptionBr" />,
                  }
                )}
        </p>
        <RadioGroup
          row
          className="delete-dialog__radio-group"
          value={deleteStatusOption}
          onChange={handleChangeRadio}
        >
          <FormControlLabel
            value={DeleteStatusOptions.deleteOnly}
            control={<Radio />}
            label={I18nTranslate.TranslateMessage('clearStatus')}
          />
          <FormControlLabel
            value={DeleteStatusOptions.deleteAndReassign}
            control={<Radio />}
            label={I18nTranslate.TranslateMessage('deleteAndReAssign')}
          />
        </RadioGroup>
        {deleteStatusOption === DeleteStatusOptions.deleteAndReassign && (
          <Stack direction="row" justifyContent="space-between">
            <p className="delete-dialog__Reassign-label">
              {I18nTranslate.TranslateMessage('reAssignTo')}
            </p>
            <Select
              fullWidth
              className="delete-dialog__Reassign-select"
              value={reassignStatusId}
              renderValue={(selected) => {
                const status = unSelectedRow.find(
                  (status) => status.id === selected
                );
                return status?.name ?? '';
              }}
              onChange={(e) => setReassignStatusId(e.target.value)}
            >
              {unSelectedRow.length ? (
                unSelectedRow.map((status) => (
                  <MenuItem key={status.id} value={status.id}>
                    {status.name}
                  </MenuItem>
                ))
              ) : (
                <p className="delete-dialog__no-option">
                  {I18nTranslate.TranslateMessage('noOptionsLeft')}
                </p>
              )}
            </Select>
          </Stack>
        )}
      </Stack>
    );
  };

  const handleClose = () => {
    onClose();
    setDeleteStatusOption(DeleteStatusOptions.deleteOnly);
    setReassignStatusId(null);
  };

  const handleDeleteStatuses = () => {
    if (singleRow) {
      handleDeleteRows([singleRow.id]);
    } else {
      handleDeleteRows(selectedRowIds);
    }
    if (
      deleteStatusOption === DeleteStatusOptions.deleteAndReassign &&
      reassignStatusId
    ) {
      dispatch(
        updateReassignStatuses({
          deletedStatusIds: singleRow ? [singleRow.id] : selectedRowIds,
          reassignStatusId,
          cases: searchCases.results,
        })
      );
    }
    handleClose();
  };

  const handleDeleteTags = () => {
    if (singleRow) {
      handleDeleteRows([singleRow.id]);
    } else {
      handleDeleteRows(selectedRowIds);
    }
    handleClose();
  };

  const handleConfirmDelete = () => {
    if (selectedTab === OptionsType.status) {
      handleDeleteStatuses();
    } else {
      handleDeleteTags();
    }
    removeSelectedRow?.();
  };

  return (
    <Dialog
      open={open}
      disableConfirm={false}
      title={
        selectedTab === OptionsType.status
          ? intl.formatMessage({ id: 'deleteStatus' })
          : selectedRowIds.length <= 1
            ? intl.formatMessage({ id: 'deleteTag' })
            : intl.formatMessage({ id: 'deleteTags' })
      }
      onClose={handleClose}
      onConfirm={handleConfirmDelete}
      isDelete
      confirmText={
        selectedTab === OptionsType.tag
          ? intl.formatMessage({ id: 'delete' })
          : !searchCases.totalResults
            ? intl.formatMessage({ id: 'delete' })
            : deleteStatusOption === DeleteStatusOptions.deleteOnly
              ? intl.formatMessage({ id: 'clearAll' })
              : intl.formatMessage({ id: 'deleteAndReAssign' })
      }
    >
      {selectedTab === OptionsType.tag
        ? renderDeleteTagContent()
        : renderDeleteStatusContent()}
    </Dialog>
  );
};

export default DeleteDialog;
