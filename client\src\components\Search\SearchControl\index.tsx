import './index.scss';
import {
  Add,
  DeleteWithLines,
  DeleteX,
  FolderMove,
  ListView,
  Settings,
} from '@assets/icons';
import AddToCaseDialog from '@components/AddToCaseDialog';
import Dialog from '@components/Dialog';
import CategoryMenu from '@components/Search/SearchControl/CategoryMenu';
import StringSelect, {
  Option,
  Selectbackground,
} from '@components/StringSelect';
import { I18nTranslate } from '@i18n';
import { GridView } from '@mui/icons-material';
import {
  Button,
  IconButton,
  PopoverOrigin,
  SelectChangeEvent,
} from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import { deleteFile } from '@store/modules/caseManager/slice';
import {
  changeSearchView,
  changeSortCategory,
  selectCategories,
  selectSearchFiles,
  selectSearchView,
  selectSelectedResults,
  selectShowDeleteSelectedFilesDialog,
  selectSortCategory,
  selectViewType,
  setShowAddToCaseDialog,
  setShowDeleteSelectedFilesDialog,
  toggleViewType,
  unfileFromCase,
  updateSelectedResults,
} from '@store/modules/search/slice';
import {
  getSearchViewLocalStorage,
  isSearchView,
  SearchView,
  setSearchViewLocalStorage,
  SortCategory,
  ViewType,
} from '@utils/local-storage';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

const sortCategoryOptions: Option[] = [
  {
    label: I18nTranslate.TranslateMessage('recentlyUploaded'),
    value: SortCategory.RecentlyUploaded,
  },
  { label: 'None', value: SortCategory.None },
];

const searchViewOptions: Option[] = [
  {
    label: I18nTranslate.TranslateMessage('grouped'),
    value: SearchView.Grouped,
  },
  {
    label: I18nTranslate.TranslateMessage('unGrouped'),
    value: SearchView.UnGrouped,
  },
];

const selectMenuPosition: {
  anchorOrigin: PopoverOrigin;
  transformOrigin: PopoverOrigin;
} = {
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'right',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'right',
  },
};

const SearchControl = () => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();

  const [gearAnchorEl, setGearAnchorEl] = useState<HTMLElement | null>(null);

  const viewType = useSelector(selectViewType);
  const searchView = useSelector(selectSearchView);
  const sortCategory = useSelector(selectSortCategory);
  const selectedResults = useSelector(selectSelectedResults);
  const searchFilesState = useSelector(selectSearchFiles);
  const categories = useSelector(selectCategories);
  const selectedCategories = categories.filter((c) => c.checked);
  const isDeleteSelectedFilesDialogOpen = useSelector(
    selectShowDeleteSelectedFilesDialog
  );

  const [resultCount, setResultCount] = useState(-1);
  const [anyOver10000, setAnyOver10000] = useState(false);

  useEffect(() => {
    if (
      searchView === SearchView.UnGrouped &&
      searchFilesState.ungroupedSearch.status === 'success'
    ) {
      const { totalResults } = searchFilesState.ungroupedSearch.data
        ?.searchMedia.jsondata ?? {
        from: 0,
        to: 1,
        limit: 1,
        totalResults: { value: 0, relation: '' },
      };
      setResultCount(totalResults.value);
      setAnyOver10000(totalResults.relation === 'gte');
    } else if (searchView === SearchView.Grouped) {
      let isAnyOver10000 = false;

      const count = Object.entries(searchFilesState.groupedSearch).reduce(
        (acc, [key, value]) => {
          const isASelectedCategory = selectedCategories.some(
            (c) => c.category === key
          );

          if (isASelectedCategory && value?.status === 'success') {
            const { totalResults } = value.data?.searchMedia.jsondata ?? {
              from: 0,
              to: 1,
              limit: 1,
              totalResults: { value: 0, relation: '' },
            };

            if (totalResults.relation === 'gte') {
              isAnyOver10000 = true;
            }

            return acc + totalResults.value;
          }
          return acc;
        },
        0
      );

      setAnyOver10000(isAnyOver10000);
      setResultCount(count);
    } else {
      setResultCount(-1);
    }
  }, [searchFilesState, searchView, selectedCategories]);

  const isSelected = !!selectedResults.length;
  const hasRowWithCaseId = useMemo(
    () => selectedResults.some((row) => row.caseId),
    [selectedResults]
  );

  const handleToggleViewType = () => {
    dispatch(toggleViewType());
  };

  const onChangeView = (e: SelectChangeEvent<string>) => {
    const { value } = e.target;
    if (isSearchView(value)) {
      setSearchViewLocalStorage(value);
    }
  };

  const onExited = () => {
    const searchView = getSearchViewLocalStorage();
    dispatch(changeSearchView(searchView));
  };

  const onChangeSortCategory = (e: SelectChangeEvent<string>) => {
    const { value } = e.target;
    dispatch(changeSortCategory(value));
  };

  const onAddToCase = () => {
    dispatch(setShowAddToCaseDialog(true));
  };

  const handleCheckedFileDeletionConfirm = () => {
    for (const result of selectedResults) {
      if (result.id) {
        dispatch(unfileFromCase({ tdoId: result.id }));
        dispatch(deleteFile({ tdoId: result.id }));
      }
    }
    dispatch(updateSelectedResults([]));
    dispatch(setShowDeleteSelectedFilesDialog(false));
  };

  const handleCheckedDeletionCancel = () => {
    dispatch(setShowDeleteSelectedFilesDialog(false));
  };

  return (
    <div className="search-page__search-control">
      <div className="search-control__total-results">
        {resultCount !== -1
          ? intl.formatMessage(
              { id: 'showTotalResults' },
              { total: `${anyOver10000 ? '>' : ''}${resultCount}` }
            )
          : ''}
      </div>
      <div className="search-control__group-buttons">
        {isSelected ? (
          <>
            {!hasRowWithCaseId ? (
              <Button
                className="search-control__actions-button"
                onClick={onAddToCase}
              >
                <Add />
                <span>
                  {intl.formatMessage({
                    id: 'addToCase',
                    defaultMessage: 'Add to Case',
                  })}
                </span>
              </Button>
            ) : (
              <Button
                className="search-control__actions-button"
                onClick={onAddToCase}
              >
                <FolderMove />
                <span>
                  {intl.formatMessage({
                    id: 'moveToAnotherCase',
                    defaultMessage: 'Move to Another Case',
                  })}
                </span>
              </Button>
            )}
            <Button
              className="search-control__actions-button"
              onClick={() => dispatch(setShowDeleteSelectedFilesDialog(true))}
            >
              <DeleteWithLines />
              <span>
                {intl.formatMessage({
                  id: 'delete',
                  defaultMessage: 'Delete',
                })}
              </span>
            </Button>
          </>
        ) : (
          <>
            {viewType === ViewType.GRID && (
              <div className="search-control__actions-select">
                <span>
                  {intl.formatMessage({
                    id: 'sortCategory',
                    defaultMessage: 'Sort Category:',
                  })}
                </span>
                <StringSelect
                  options={sortCategoryOptions}
                  onChange={onChangeSortCategory}
                  selectValue={sortCategory}
                  menuPosition={selectMenuPosition}
                  menuListClassName="search-control__menu-list"
                  height={28}
                  minWidth={159}
                  background={Selectbackground.Secondary}
                />
              </div>
            )}
            <div className="search-control__actions-select">
              <span>
                {intl.formatMessage({
                  id: 'view',
                  defaultMessage: 'View',
                })}
              </span>
              <StringSelect
                options={searchViewOptions}
                onChange={onChangeView}
                selectValue={searchView}
                height={28}
                minWidth={159}
                background={Selectbackground.Secondary}
                menuListClassName="search-control__menu-list search-view-list"
                menuPosition={selectMenuPosition}
                onExited={onExited}
                showCheckIcon
              />
            </div>
            <IconButton
              className="search-control__icon-button"
              data-testid="gear-icon"
              onClick={(e) => setGearAnchorEl(e.currentTarget)}
            >
              <Settings />
            </IconButton>
            <IconButton
              onClick={handleToggleViewType}
              className="search-control__icon-button"
              data-testid="view-type-toggle-button"
            >
              {viewType === ViewType.GRID ? (
                <ListView data-testid="list-view-icon" />
              ) : (
                <GridView data-testid="grid-view-icon" />
              )}
            </IconButton>
          </>
        )}
      </div>
      <CategoryMenu
        searchView={searchView}
        open={Boolean(gearAnchorEl)}
        anchorEl={gearAnchorEl}
        onClose={() => setGearAnchorEl(null)}
      />
      <AddToCaseDialog />
      <Dialog
        open={isDeleteSelectedFilesDialogOpen}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleCheckedDeletionCancel}
        onConfirm={handleCheckedFileDeletionConfirm}
        confirmText={intl.formatMessage({ id: 'yesDeleteFiles' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        useTypeConfirmation
        disableConfirm={false}
        data-testid="delete-selected-files-dialog"
      >
        <div>
          {I18nTranslate.TranslateMessage('deleteFilesConfirmationMsg')}
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('allSelectedFilesWillBeDeleted')}
          </span>
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('everyOneInOrgWillLoseAccess')}
          </span>
        </div>
      </Dialog>
    </div>
  );
};

export default SearchControl;
