import { Document, Image, VideoCamera } from '@assets/icons';
import GraphicEqIcon from '@mui/icons-material/GraphicEq';
import { configureAppStore } from '@store/index';
import { toggleOpenEditDrawer } from '@store/modules/metadata/slice';
import {
  SearchSliceState,
  initialState as searchInitialState,
} from '@store/modules/search/slice';
import { act, fireEvent, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ViewType } from '@utils/local-storage';
import { JSX } from 'react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router';
import {
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
  vi,
} from 'vitest';
import SearchTable, { SearchResult } from '.';
import { render } from '../../../../../test/render';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '@store/modules/caseManager/slice';
import {
  CaseDetailSliceState,
  initialState as caseDetailInitialState,
} from '@store/modules/caseDetail/slice';
import AddToCaseDialog from '@components/AddToCaseDialog';

const initialState: {
  caseManager: CaseManagerSliceState;
  caseDetail: CaseDetailSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
  search: SearchSliceState;
} = {
  caseManager: caseManagerInitialState,
  caseDetail: caseDetailInitialState,
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: '',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
  search: searchInitialState,
};

const fileTypeIcons: Record<string, JSX.Element> = {
  video: <VideoCamera />,
  audio: <GraphicEqIcon />,
  document: <Document />,
  image: <Image />,
  default: <Document />,
};
const getFileIcon = (fileType: string) =>
  fileTypeIcons[fileType] || fileTypeIcons.default;

const mockSearchResults: SearchResult[] = Array.from(
  { length: 5 },
  (_, index) => ({
    id: `file_${index + 1}`,
    description: `This is a description for file ${index + 1}`,
    fileName: `File_${index + 1}`,
    fileType: 'txt',
    fileIcon: getFileIcon('txt'),
    duration: 22.5,
    parentTreeObjectIds: [`parent_${index % 5}`],
    createdByName: `User_${index % 3}`,
    createdTime: new Date(2024, 0, index + 1).toISOString(),
    updatedTime: new Date(2024, 1, index + 1).toISOString(),
    caseId: `case_${index + 1}_1234567890`,
    retentionDate: new Date(2025, 0, index + 1).toISOString(),
    fileDuration: 123456,
    thumbnailUrl: `https://picsum.photos/200/300?random=${index}`,
  })
);

const mockSearchResultsWithoutCaseId = mockSearchResults.map((result) => ({
  ...result,
  caseId: undefined,
}));

const mockPagination: {
  from: number;
  to: number;
  limit: number;
  totalResults: { value: number; relation: string };
} = {
  from: 0,
  to: 10,
  limit: 50,
  totalResults: { value: 10, relation: '' },
};

describe('SearchTable', () => {
  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {};
  });

  beforeEach(() => {
    localStorage.clear();
  });

  afterEach(() => {
    localStorage.clear();
  });

  vi.mock('@tanstack/react-virtual', () => ({
    useVirtualizer: vi.fn(() => ({
      getVirtualItems: () => [
        {
          index: 0,
          size: 69,
          start: 0,
          end: 69,
          key: 0,
          measureElement: vi.fn(),
        },
        {
          index: 1,
          size: 69,
          start: 69,
          end: 138,
          key: 1,
          measureElement: vi.fn(),
        },
        {
          index: 2,
          size: 69,
          start: 138,
          end: 207,
          key: 2,
          measureElement: vi.fn(),
        },
        {
          index: 3,
          size: 69,
          start: 207,
          end: 276,
          key: 3,
          measureElement: vi.fn(),
        },
        {
          index: 4,
          size: 69,
          start: 276,
          end: 345,
          key: 4,
          measureElement: vi.fn(),
        },
      ],
      getTotalSize: () => 138,
      measure: vi.fn(),
      scrollToIndex: vi.fn(),
    })),
  }));

  it('should render empty state start exploring when there are no searchParams', () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={[]}
          loading={false}
          pagination={mockPagination}
        />
      </MemoryRouter>
    );

    expect(
      screen.queryByTestId('check-box__check-all')
    ).not.toBeInTheDocument();

    // Start exploring is shown because there are no searchParams
    expect(screen.getByText('Start Exploring')).toBeInTheDocument();
  });

  it('should render empty state when there are searchParams but no search results', () => {
    const store = configureAppStore({
      ...initialState,
      search: {
        selectedResults: [],
        categories: [
          {
            id: 'filename',
            category: 'filename',
            color: 'var(--background-filename)',
            expanded: true,
            checked: true,
          },
          {
            id: 'transcription',
            category: 'transcription',
            color: 'var(--background-transcription)',
            expanded: true,
            checked: false,
          },
        ],
        ...initialState.search.searchFiles,
        searchFiles: {
          searchParams: {
            keywordSearchQuery: 'test',
            searchResultType: 'ungrouped',
            checkedResultCategories: ['transcription'],
            pagination: { ungrouped: { offset: 0, limit: 50 } },
          },
        },
      },
    });

    store.dispatch = vi.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <SearchTable
            searchResults={[]}
            loading={false}
            pagination={mockPagination}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(
      screen.queryByTestId('check-box__check-all')
    ).not.toBeInTheDocument();

    // No results found is shown because there are searchParams but no search results
    expect(screen.getByText('No Results Found')).toBeInTheDocument();
  });

  it('should render empty state when there are searchParams but no categories selected', () => {
    const store = configureAppStore({
      ...initialState,
      search: {
        selectedResults: [],
        categories: [
          {
            id: 'filename',
            category: 'filename',
            color: 'var(--background-filename)',
            expanded: true,
            checked: false,
          },
          {
            id: 'transcription',
            category: 'transcription',
            color: 'var(--background-transcription)',
            expanded: true,
            checked: false,
          },
        ],
        ...initialState.search.searchFiles,
        searchFiles: {
          searchParams: {
            keywordSearchQuery: 'test',
            searchResultType: 'ungrouped',
            checkedResultCategories: ['transcription'],
            pagination: { ungrouped: { offset: 0, limit: 50 } },
          },
        },
      },
    });

    store.dispatch = vi.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <SearchTable
            searchResults={[]}
            loading={false}
            pagination={mockPagination}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(
      screen.queryByTestId('check-box__check-all')
    ).not.toBeInTheDocument();

    // user is told to select a category to start
    expect(screen.getByText('Select a category to start')).toBeInTheDocument();
  });

  it('should render when having search results', () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={mockSearchResults}
          loading={false}
          pagination={mockPagination}
        />
      </MemoryRouter>
    );

    expect(screen.queryByTestId('check-box__check-all')).toBeInTheDocument();
    expect(
      screen.queryByText(mockSearchResults[0].fileName)
    ).toBeInTheDocument();
  });

  it('should render when search results are loading', () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={mockSearchResults}
          loading
          pagination={mockPagination}
        />
      </MemoryRouter>
    );

    expect(screen.getByTestId('table-body_loading-icon')).toBeInTheDocument();
  });

  it('should handle checkbox correctly', async () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={mockSearchResults}
          loading={false}
          pagination={mockPagination}
        />
      </MemoryRouter>
    );

    const checkAll = screen
      .getByTestId('check-box__check-all')
      .querySelector('input');
    const firstRowCheckbox = screen
      .getByTestId(`check-box__check-row-${mockSearchResults[0].id}`)
      .querySelector('input');
    const secondRowCheckbox = screen
      .getByTestId(`check-box__check-row-${mockSearchResults[1].id}`)
      .querySelector('input');

    expect(checkAll?.checked).toBe(false);
    expect(firstRowCheckbox?.checked).toBe(false);
    expect(secondRowCheckbox?.checked).toBe(false);

    fireEvent.click(checkAll!);

    await waitFor(() => {
      expect(checkAll?.checked).toBe(true);
      expect(
        screen
          .getByTestId(`check-box__check-row-${mockSearchResults[0].id}`)
          .querySelector('input')?.checked
      ).toBe(true);
      expect(
        screen
          .getByTestId(`check-box__check-row-${mockSearchResults[1].id}`)
          .querySelector('input')?.checked
      ).toBe(true);
    });
  });

  it('should show Action Menu when clicking on the action button', () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={mockSearchResults}
          loading={false}
          pagination={mockPagination}
        />
      </MemoryRouter>
    );
    const actionButton = screen.getAllByTestId('search-table-menu')[0];
    fireEvent.click(actionButton);
    expect(screen.getByText('Move to Another Case')).toBeInTheDocument();
    expect(screen.getByText('View File')).toBeInTheDocument();
    expect(screen.getByText('Delete')).toBeInTheDocument();
  });

  it('should show Action Menu when clicking on the action button w/o caseId', () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={mockSearchResultsWithoutCaseId}
          loading={false}
          pagination={mockPagination}
        />
      </MemoryRouter>
    );
    const actionButton = screen.getAllByTestId('search-table-menu')[0];
    fireEvent.click(actionButton);
    expect(screen.getByText('Add to Case')).toBeInTheDocument();
    expect(screen.getByText('View File')).toBeInTheDocument();
    expect(screen.getByText('Delete')).toBeInTheDocument();
  });

  it('should dispatch show Add to Case Dialog when clicking Add to Case from the Action Menu', async () => {
    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <SearchTable
            searchResults={mockSearchResultsWithoutCaseId}
            loading={false}
            pagination={mockPagination}
          />
          <AddToCaseDialog />
        </MemoryRouter>
      </Provider>
    );

    const actionButton = screen.getAllByTestId('search-table-menu')[0];
    act(() => {
      fireEvent.click(actionButton);
    });

    expect(screen.getByText('Add to Case')).toBeInTheDocument();
    expect(screen.getByText('View File')).toBeInTheDocument();
    expect(screen.getByText('Delete')).toBeInTheDocument();

    act(() => {
      fireEvent.click(screen.getByText('Add to Case'));
    });

    await waitFor(() => {
      expect(screen.getByTestId('add-to-case-dialog')).toBeInTheDocument();
      expect(screen.getByText('Add to Case')).toBeInTheDocument();
    });
  });

  it("should dispatch action to open edit metadata drawer when choose 'Edit Metadata' option", async () => {
    const store = configureAppStore();
    store.dispatch = vi.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <SearchTable
            searchResults={mockSearchResults}
            loading={false}
            pagination={mockPagination}
          />
        </MemoryRouter>
      </Provider>
    );

    const actionButton = screen.getAllByTestId('search-table-menu')[0];

    userEvent.click(actionButton);
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
    });

    userEvent.click(screen.getByText('Edit Metadata'));
    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledWith(
        toggleOpenEditDrawer('file_1')
      );
    });
  });

  it('should toggle blur state and save to localStorage', () => {
    const store = configureAppStore({
      ...initialState,
      search: {
        ...initialState.search,
        viewType: ViewType.GRID,
      },
    });
    render(
      <Provider store={store}>
        <MemoryRouter>
          <SearchTable
            searchResults={mockSearchResults}
            loading={false}
            pagination={mockPagination}
          />
        </MemoryRouter>
      </Provider>
    );

    const blurSwitch = screen.getByTestId('blur-switch').querySelector('input');
    expect(blurSwitch).toBeInTheDocument();
    expect(blurSwitch?.checked).toBe(false);

    fireEvent.click(blurSwitch!);
    expect(blurSwitch?.checked).toBe(true);
    expect(localStorage.getItem('investigate-set-blur')).toBe('true');

    fireEvent.click(blurSwitch!);
    expect(blurSwitch?.checked).toBe(false);
    expect(localStorage.getItem('investigate-set-blur')).toBe('false');
  });

  it('should select a range of rows when holding Shift and clicking checkbox', async () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={mockSearchResults}
          loading={false}
          pagination={mockPagination}
        />
      </MemoryRouter>
    );

    const firstRowCheckbox = screen
      .getByTestId(`check-box__check-row-${mockSearchResults[0].id}`)
      .querySelector('input');
    fireEvent.click(firstRowCheckbox!);
    expect(firstRowCheckbox?.checked).toBe(true);

    const forthRowCheckbox = screen
      .getByTestId(`check-box__check-row-${mockSearchResults[3].id}`)
      .querySelector('input');
    fireEvent.click(forthRowCheckbox!, { shiftKey: true });

    await waitFor(() => {
      expect(firstRowCheckbox?.checked).toBe(true);
      expect(forthRowCheckbox?.checked).toBe(true);
    });
  });

  it('should unselect a row when unchecking its checkbox', async () => {
    render(
      <MemoryRouter>
        <SearchTable
          searchResults={mockSearchResults}
          loading={false}
          pagination={mockPagination}
        />
      </MemoryRouter>
    );

    const firstRowCheckbox = screen
      .getByTestId(`check-box__check-row-${mockSearchResults[0].id}`)
      .querySelector('input');
    fireEvent.click(firstRowCheckbox!);
    expect(firstRowCheckbox?.checked).toBe(true);

    fireEvent.click(firstRowCheckbox!);
    await waitFor(() => {
      expect(firstRowCheckbox?.checked).toBe(false);
    });
  });

  it('should focus the first file card with the down arrow', async () => {
    const store = configureAppStore({
      ...initialState,
      search: {
        ...initialState.search,
        viewType: ViewType.GRID,
      },
    });
    render(
      <Provider store={store}>
        <MemoryRouter>
          <SearchTable
            searchResults={mockSearchResults}
            loading={false}
            pagination={mockPagination}
          />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(document.activeElement?.classList).not.toContain('file-card');
    });

    fireEvent.keyUp(document.body, { key: 'ArrowDown' });

    await waitFor(() => {
      expect(document.activeElement?.getAttribute('data-index')).toBe('0');
    });
  });

  it('should focus on the next card with the right arrow, then previous with the left arrow', async () => {
    const store = configureAppStore({
      ...initialState,
      search: {
        ...initialState.search,
        viewType: ViewType.GRID,
      },
    });
    render(
      <Provider store={store}>
        <MemoryRouter>
          <SearchTable
            searchResults={mockSearchResults}
            loading={false}
            pagination={mockPagination}
          />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(document.activeElement?.classList).not.toContain('file-card');
    });

    fireEvent.keyUp(document.body, { key: 'ArrowDown' });

    await waitFor(() => {
      expect(document.activeElement?.getAttribute('data-index')).toBe('0');
    });

    fireEvent.keyUp(document.body, { key: 'ArrowRight' });

    await waitFor(() => {
      expect(document.activeElement?.getAttribute('data-index')).toBe('1');
    });
    fireEvent.keyUp(document.body, { key: 'ArrowLeft' });

    await waitFor(() => {
      expect(document.activeElement?.getAttribute('data-index')).toBe('0');
    });
  });
});
