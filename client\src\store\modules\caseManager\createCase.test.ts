import { InvestigateCase } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';
import { describe, expect, it } from 'vitest';
import config from '../../../../config.json';
import { createCase } from './createCase';
// not unit test. just a driver to help troubleshoot gql api
describe('gql api client for troubleshoot', () => {
  it.skip('createCase', async () => {
    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = '4cfe23f1-094e-42bf-8b39-37c5e9d06e01';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const dataRegistryId = config.registryIds.caseRegistryId;

    const me = await gql.me();
    const rootFolder = await gql.getRootFolder();
    const folderContentTemplateSchemaId =
      await gql.getSDOSchemaId(dataRegistryId);
    // const caseDate =  '2015-01-29';
    const caseDate = new Date().toISOString();
    const investigateCase = {
      caseName: 'JiaCase1',
      caseId: '123',
      description: "Jia's Case",
      caseDate,
      preconfiguredTagIds: ['tagKey1'],
      statusId: '234',
    } as InvestigateCase;
    if (!rootFolder?.id) {
      throw new Error('no rootFolder found');
    }
    const got = await createCase({
      investigateCase,
      rootFolderId: rootFolder.id,
      folderContentTemplateSchemaId,
      userId: me.id,
      userName: me.name,
      gql,
    });
    console.log('got =====>', got);
    expect(got).toBeDefined();
  }, 100000);
});
