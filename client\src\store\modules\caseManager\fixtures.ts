export const mockCaseSearchResult = {
  searchMedia: {
    jsondata: {
      results: [
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-03T18:26:18.215Z',
          caseName: 'JiaCase1',
          folderId: '123',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "<PERSON><PERSON>'s Case",
          createdDateTime: '2025-02-03T18:26:18.215Z',
          modifiedDateTime: '2025-02-03T18:26:18.215Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '03fc3da0-95a6-4477-bc86-9d17be1fa76c',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-03T18:45:38.174Z',
          caseName: 'JiaCase1',
          folderId: '32b2434d-c531-4be7-accf-334b310a0682',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "<PERSON><PERSON>'s <PERSON>",
          createdDateTime: '2025-02-03T18:45:43.798Z',
          modifiedDateTime: '2025-02-03T18:45:43.798Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '9d33e08e-63ce-47b6-a9f1-8fb43aa3b928',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-03T22:08:08.399Z',
          caseName: 'JiaCase1',
          folderId: 'b0c395f3-691f-4462-a165-9bb872f60210',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "Jia's Case",
          createdDateTime: '2025-02-03T22:08:11.177Z',
          modifiedDateTime: '2025-02-03T22:08:11.177Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '5ff80b24-5868-47be-afd5-5105f0c2a314',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-04T00:33:55.631Z',
          caseName: 'JiaCase1',
          folderId: 'd4f03ba1-1418-4c29-b1d0-b03eba1a2acc',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "Jia's Case",
          createdDateTime: '2025-02-04T00:34:00.113Z',
          modifiedDateTime: '2025-02-04T00:34:00.113Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '9409dca5-9867-4d67-9395-d1688d25f61f',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-04T00:35:56.770Z',
          caseName: 'JiaCase1',
          folderId: 'a6d5e6ee-64c2-481d-aefb-b116d6d59e32',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "Jia's Case",
          createdDateTime: '2025-02-04T00:35:57.568Z',
          modifiedDateTime: '2025-02-04T00:35:57.568Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '4689e1b0-29df-4265-a343-79f2b8e19bef',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-04T00:48:11.084Z',
          caseName: 'JiaCase1',
          folderId: '1d2c691d-2eb1-4788-bd62-5e211f448ff8',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "Jia's Case",
          createdDateTime: '2025-02-04T00:48:18.643Z',
          modifiedDateTime: '2025-02-04T00:48:18.643Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '0432560d-aef7-4107-8e4e-4c7f4780c99f',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-04T00:51:02.907Z',
          caseName: 'JiaCase1',
          folderId: '0b2767d5-03c9-4367-9dd9-d07e5e1af690',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "Jia's Case",
          createdDateTime: '2025-02-04T00:51:05.555Z',
          modifiedDateTime: '2025-02-04T00:51:05.555Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '99203556-4d8f-4171-8d42-ce88fa4bfc2d',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-03T18:27:10.691Z',
          caseName: 'JiaCase1',
          folderId: 'a2aa0cfa-980c-4451-b1e1-b1de9bfb9f99',
          createdBy: '',
          description: "Jia's Case",
          createdDateTime: '2025-02-03T18:27:13.665Z',
          modifiedDateTime: '2025-02-03T18:27:13.665Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: 'db0feb95-eafc-49eb-af08-a374a3cc4456',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-04T00:57:06.589Z',
          caseName: 'JiaCase1',
          folderId: 'd23f621f-05ca-473c-9822-e2f34a923e4d',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "Jia's Case",
          createdDateTime: '2025-02-04T00:57:10.150Z',
          modifiedDateTime: '2025-02-04T00:57:10.150Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '1c095cf2-acff-41d0-a8d6-244e63cbf858',
        },
        {
          caseId: '123',
          statusId: 'active',
          caseDate: '2025-02-04T01:01:53.677Z',
          caseName: 'JiaCase1',
          folderId: 'e7ea6333-de79-4581-a2a7-9b68979d0032',
          createdBy: '9aa0aa08-0950-4e49-b471-fdeceed12f89',
          description: "Jia's Case",
          createdDateTime: '2025-02-04T01:01:59.148Z',
          modifiedDateTime: '2025-02-04T01:01:59.148Z',
          preconfiguredTagIds: ['South Los Angeles'],
          id: '330bc35a-c680-4aed-88cb-b61dad07de3f',
        },
      ],
      totalResults: 12,
      limit: 10,
      from: 0,
      to: 9,
      searchToken: '9c12db80-e3fc-11ef-900e-1f688fd8a2a6',
      timestamp: 1738785956,
    },
  },
};

export const searchQueryNameSortedWithTwoStatuses = {
  gqlEndpoint: 'apiroot123/graphQLEndpoint123',
  query: `query searchMedia($search: JSONData!) {
      searchMedia(search: $search) {
        jsondata
      }
    }`,
  token: 'sessionToken123',
  variables: {
    search: {
      index: ['mine'],
      limit: 10,
      offset: 0,
      query: {
        conditions: [
          {
            operator: 'exists',
            name: 'toBeDeletedTime',
            not: true,
          },
          {
            field: 'caseId.fulltext',
            operator: 'query_string',
            value: '*event name search text*',
          },
          {
            field: 'statusId',
            operator: 'terms',
            not: false,
            values: ['statusId1'],
          },
        ],
        operator: 'and',
      },
      sort: [
        {
          field: 'caseId',
          order: 'desc',
        },
        {
          field: 'createdDateTime',
          order: 'desc',
        },
      ],
      type: 'folderContentTemplateSchemaId123',
    },
  },
  veritoneAppId: 'veritoneAppId123',
};

export const softDeleteFileQuery = {
  gqlEndpoint: 'apiroot123/graphQLEndpoint123',
  query: `
    mutation softDeleteTdo($tdoId: ID!, $details: JSONData!) {
      updateTDO(input: {
        id: $tdoId
        details: $details
      }) {
        id
      }
    }`,
  token: 'sessionToken123',
  variables: {
    details: {
      addToIndex: true,
      name: 'Walking Tour 4-i-build10.mp4',
      tags: [
        {
          value: 'veritone_track',
        },
        {
          key: 'toBeDeletedTime',
          value: '2025-02-18T17:32:40.000Z',
        },
      ],
      veritoneFile: {
        createdByName: 'firstname lastname',
        fileName: 'Walking Tour 4-i-build10.mp4',
        fileType: 'video/mp4',
      },
      veritonePermissions: {
        acls: [
          {
            groupId: 'f534544-5368-4c6a-b0d6-095435b07a',
            permission: 'owner',
          },
        ],
        isPublic: false,
      },
      veritoneProgram: {
        programLiveImage: '',
      },
    },
    tdoId: '1234567890',
  },
  veritoneAppId: 'veritoneAppId123',
};

export const searchQueryTagSortedCaseDateAscPage2 = {
  gqlEndpoint: 'apiroot123/graphQLEndpoint123',
  query: `query searchMedia($search: JSONData!) {
      searchMedia(search: $search) {
        jsondata
      }
    }`,
  token: 'sessionToken123',
  variables: {
    search: {
      index: ['mine'],
      limit: 10,
      offset: 10,
      query: {
        conditions: [
          {
            operator: 'exists',
            name: 'toBeDeletedTime',
            not: true,
          },
          {
            field: 'preconfiguredTagIds',
            operator: 'terms',
            values: ['tagId123'],
          },
        ],
        operator: 'and',
      },
      sort: [
        {
          field: 'caseDate',
          order: 'asc',
        },
        {
          field: 'createdDateTime',
          order: 'desc',
        },
      ],
      type: 'folderContentTemplateSchemaId123',
    },
  },
  veritoneAppId: 'veritoneAppId123',
};
