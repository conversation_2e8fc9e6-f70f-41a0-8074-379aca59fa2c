import './index.scss';
import ColorPicker from '@components/ColorPicker';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { Button, PopoverOrigin } from '@mui/material';
import cn from 'classnames';
import { useRef, useState } from 'react';

interface Props {
  className?: string;
  onChange?: (color: string) => void;
  color?: string;
  presetColors?: string[];
  disableEdit?: boolean;
  anchorOrigin?: PopoverOrigin;
  transformOrigin?: PopoverOrigin;
  isCreateStatus?: boolean;
  isDeleted?: boolean;
}

const CellColorPicker = ({
  className,
  onChange,
  color,
  presetColors,
  disableEdit,
  anchorOrigin,
  transformOrigin,
  isCreateStatus,
  isDeleted,
}: Props) => {
  const anchor = useRef<HTMLButtonElement>(null);
  const [isOpen, toggle] = useState(false);
  const pickerColor = color ?? '#000000';

  return (
    <div className={cn('color-picker', className)} data-testid="color-picker">
      <Button
        className={cn('color-picker__button-swatch', {
          open: isOpen,
          deleted: isDeleted,
        })}
        onClick={() => !disableEdit && toggle(true)}
        disabled={disableEdit || isDeleted}
        ref={anchor}
      >
        <div
          className="color-picker__swatch"
          data-testid="color-picker-swatch"
          style={{ backgroundColor: pickerColor }}
        />
        {!disableEdit && <ArrowDropDownIcon />}
      </Button>
      <ColorPicker
        anchor={anchor}
        open={isOpen}
        toggle={toggle}
        color={pickerColor}
        onChange={onChange}
        presetColors={presetColors}
        anchorOrigin={
          anchorOrigin ?? {
            vertical: 'top',
            horizontal: 'left',
          }
        }
        transformOrigin={
          transformOrigin ?? {
            vertical: 'top',
            horizontal: 'right',
          }
        }
        classname={
          isCreateStatus
            ? 'color-picker__create-status'
            : 'color-picker-popover'
        }
      />
    </div>
  );
};

export default CellColorPicker;
