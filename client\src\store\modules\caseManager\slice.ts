import { Case } from '@components/CaseManager/CaseTable';
import { I18nTranslate } from '@i18n';
import { GetThunkAPI, PayloadAction } from '@reduxjs/toolkit';
import {
  BasicUserInfo,
  CaseFilterValue,
  CaseResult,
  CaseSearchResults,
  InvestigateCase,
  InvestigateCaseSDO,
} from '@shared-types/types';
import { createAppSlice } from '@store/createAppSlice';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/index';
import { getSchemaId } from '@store/modules/config/getSchema';
import { fetchStatuses, fetchTags } from '@store/modules/settings/slice';
import { ApiStatus } from '@store/types';
import { getApiAuthToken } from '@store/utils';
import { GQLApi, SortBy } from '@utils/helpers';
import {
  addCaseStatusToLocalStorage,
  CASES_FOLDER_KEY,
  CaseStatus,
  EDIT_CASE_KEY,
  getLocalStorage,
  setLocalStorage,
} from '@utils/local-storage';
import { enqueueSnackbar } from 'notistack';
import { checkCasesExists } from './checkCasesExists';
import { checkCreateRootFolder } from './checkCreateRootFolder';
import { createCase } from './createCase';
import { deleteCaseBySdoId } from './deleteCase';
import { fetchFileName } from './fetchFileName';
import { getCase } from './getCase';
import { getNameOrEmail, getOwner } from '@utils/getOwners';
import { searchCases as searchCasesFn } from './searchCases';
import { softDeleteFile } from './softDeleteFile';
import { updateCase } from './updateCase';

export interface CaseManagerSliceState {
  createCase: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  deleteCase: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  rootFolder: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  folderContentTemplateSchema: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  cases: {
    status: ApiStatus;
    error?: string;
    data: CaseSearchResults['searchMedia']['jsondata'];
  };
  caseValidity: {
    status: 'idle' | 'loading' | 'complete' | 'failure';
    validCases: CaseSearchResults['searchMedia']['jsondata']['results'][0]['folderId'][];
    inValidCases: CaseSearchResults['searchMedia']['jsondata']['results'][0]['folderId'][];
  };
  sortBy: SortBy;
  sortDirection: 'desc' | 'asc';
  selectedCase: {
    status: ApiStatus;
    data: InvestigateCaseSDO;
  };
  editingCaseFolderId?: string;
  deleteFile: {
    status: ApiStatus;
    error?: string;
  };
  userInfo: Record<string, BasicUserInfo['basicUserInfo']>;
  isCaseDrawerOpen: boolean;
  isShareDrawerOpen: boolean;
  pollInterval?: NodeJS.Timeout;
  limit: number;
  offset: number;
  caseFilter: CaseFilterValue;
  statusMenuSelectedId: string;
  rowDataTableContext?: CaseResult;
}

export const initialState: CaseManagerSliceState = {
  createCase: {
    status: 'idle',
    error: '',
    id: '',
  },
  deleteCase: {
    status: 'idle',
    error: '',
    id: '',
  },
  rootFolder: {
    status: 'idle',
    error: '',
    id: '',
  },
  folderContentTemplateSchema: {
    status: 'idle',
    error: '',
    id: '',
  },
  cases: {
    status: 'idle',
    error: '',
    data: {
      results: [],
      totalResults: 0,
      limit: 0,
      from: 0,
      to: 0,
    },
  },
  sortBy: SortBy.CaseDate,
  sortDirection: 'desc',
  selectedCase: {
    status: 'idle',
    data: {
      sdoId: '',
      createdDateTime: '',
      modifiedDateTime: '',
      caseId: '',
      description: '',
      caseDate: '',
      statusId: '',
      preconfiguredTagIds: [],
      folderId: '',
      createdBy: '',
      modifiedBy: '',
    },
  },
  deleteFile: {
    status: 'idle',
    error: '',
  },
  userInfo: {},
  isCaseDrawerOpen: false,
  isShareDrawerOpen: false,
  limit: 50,
  offset: 0,
  editingCaseFolderId: '',
  caseFilter: {},
  caseValidity: {
    status: 'idle',
    validCases: [],
    inValidCases: [],
  },
  statusMenuSelectedId: '',
  rowDataTableContext: {
    caseId: '',
    statusId: '',
    caseDate: '',
    caseName: '',
    folderId: '',
    createdBy: '',
    description: '',
    createdDateTime: '',
    modifiedDateTime: '',
    preconfiguredTagIds: [],
    id: '',
  },
};

export const caseManagerSlice = createAppSlice({
  name: 'caseManager',
  initialState,
  reducers: (create) => {
    const createThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();
    return {
      setSort: create.reducer(
        (
          state,
          action: PayloadAction<{
            sortBy: SortBy;
            sortDirection: 'asc' | 'desc';
          }>
        ) => {
          state.sortBy = action.payload.sortBy;
          state.sortDirection = action.payload.sortDirection;
        }
      ),
      setLimit: create.reducer((state, action: PayloadAction<number>) => {
        state.limit = action.payload;
      }),
      setOffset: create.reducer((state, action: PayloadAction<number>) => {
        state.offset = action.payload;
      }),
      setCaseFilter: create.reducer(
        (state, action: PayloadAction<CaseFilterValue>) => {
          state.caseFilter = action.payload;
        }
      ),
      createNewCase: createThunk(
        async (investigateCase: InvestigateCase, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const rootFolderId = selectRootFolderId(state);
          if (!rootFolderId) {
            throw new Error('no root folder found');
          }
          const folderContentTemplateSchemaId =
            selectFolderContentTemplateSchema(state).id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }
          const { userId, kvp } = state.user.user;
          const folderId = await createCase({
            investigateCase,
            rootFolderId,
            folderContentTemplateSchemaId,
            userId,
            userName: `${kvp?.firstName ?? ''} ${kvp?.lastName ?? ''}`,
            gql,
          });

          if (!folderId) {
            throw new Error('Case creation failed.');
          }

          // Add new created folderId to local storage
          const lsFolderIds = getLocalStorage<string[]>(CASES_FOLDER_KEY) || [];
          setLocalStorage(CASES_FOLDER_KEY, [...lsFolderIds, folderId]);
          return folderId;
        },
        {
          pending: (state) => {
            state.createCase.status = 'loading';
            state.cases.status = 'loading';
            state.createCase.id = '';
          },
          fulfilled: (state, action) => {
            // TODO: handle create case success
            state.createCase.status = 'complete';
            state.cases.status = 'complete';
            state.createCase.id = action.payload;

            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseCreatedSuccessfully'),
              {
                variant: 'success',
              }
            );
          },
          rejected: (state, action) => {
            // TODO: handle create case failure
            state.createCase.status = 'failure';
            state.createCase.id = '';
            state.cases.status = 'failure';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseCreationFailed'),
              {
                variant: 'error',
              }
            );
            console.error('failed to create case', action.error);
          },
        }
      ),
      deleteCase: createThunk(
        async (sdoId: string, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const folderContentTemplateSchemaId =
            selectFolderContentTemplateSchema(state).id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }
          await deleteCaseBySdoId({
            folderContentTemplateSchemaId,
            sdoId,
            userId: state.user.user.userId,
            gql,
          });
          return sdoId;
        },
        {
          pending: (state) => {
            state.deleteCase.status = 'loading';
            state.deleteCase.id = '';
          },
          fulfilled: (state, action) => {
            state.deleteCase.status = 'complete';
            state.deleteCase.id = action.payload;
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseDeletedSuccessfully'),
              {
                variant: 'success',
              }
            );
          },

          rejected: (state, _action) => {
            // TODO: handle create case failure
            state.deleteCase.status = 'failure';
            state.deleteCase.id = '';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseDeletionFailed'),
              {
                variant: 'error',
              }
            );
          },
        }
      ),
      rootFolder: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const folderId = await checkCreateRootFolder(gql);
          return folderId;
        },
        {
          pending: (state) => {
            state.rootFolder.status = 'loading';
            state.rootFolder.id = '';
          },
          fulfilled: (state, action) => {
            state.rootFolder.status = 'complete';
            state.rootFolder.id = action.payload;
          },
          rejected: (state, action) => {
            // TODO: add snack bar for failure
            state.rootFolder.status = 'failure';
            state.rootFolder.id = '';
            console.error('failed to create case', action.error);
          },
        }
      ),
      folderContentTemplateSchema: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const dataRegistryId = config.registryIds.caseRegistryId;
          const id = await getSchemaId(dataRegistryId, gql);
          return id;
        },
        {
          pending: (state) => {
            state.folderContentTemplateSchema.status = 'loading';
            state.folderContentTemplateSchema.id = '';
          },
          fulfilled: (state, action) => {
            state.folderContentTemplateSchema.status = 'complete';
            state.folderContentTemplateSchema.id = action.payload;
          },
          rejected: (state, action) => {
            state.folderContentTemplateSchema.status = 'failure';
            state.folderContentTemplateSchema.id = '';
            console.error('failed to get case schemaId', action.error);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('failedGetCaseSchemaId'),
              { variant: 'error' }
            );
          },
        }
      ),
      searchCases: createThunk(
        async (
          payload: {
            filteredStatusId?: string[];
            isPolling?: boolean;
          },
          thunkAPI: GetThunkAPI<{
            getState: () => RootState;
            extra: { http: HttpClient };
          }>
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const { filteredStatusId } = payload;

          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const folderContentTemplateSchemaId =
            state.caseManager.folderContentTemplateSchema.id;

          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }

          const { offset, limit, sortBy, sortDirection } = state.caseManager;
          const {
            caseId: caseIdSearchText,
            tagIds,
            statusId,
          } = state.caseManager.caseFilter;

          return await searchCasesFn({
            caseIdSearchText,
            tagIds,
            statusIds: statusId ? [statusId] : undefined,
            limit,
            offset,
            sortBy,
            sortDirection,
            folderContentTemplateSchemaId,
            gql,
            caseStatuses: filteredStatusId ? filteredStatusId : undefined,
          });
        },
        {
          pending: (state, action) => {
            const isPolling = action.meta.arg.isPolling;
            if (!isPolling) {
              state.cases.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const editedCases = getLocalStorage<Case[]>(EDIT_CASE_KEY) || [];
            const updatedResults = action.payload.results.map((result) => {
              const editedCase = editedCases.find(
                (edited) => edited.folderId === result.folderId
              );
              return editedCase ? { ...result, ...editedCase } : result;
            });
            const updatedPayload = {
              ...action.payload,
              results: updatedResults,
            };

            state.cases.data = updatedPayload;
            state.cases.status = 'complete';

            // Folder IDs from API
            const folderResultIds = action.payload.results.map(
              (caseResult) => caseResult.folderId
            );
            // Folder IDs from local storage
            const folderLSIds =
              getLocalStorage<string[]>(CASES_FOLDER_KEY) || [];
            // Update local storage, remove folders that are available in the results of API
            const updatedFolderIds = folderLSIds.filter(
              (id) => !folderResultIds.includes(id)
            );
            setLocalStorage(CASES_FOLDER_KEY, updatedFolderIds);
          },
          rejected: (state, action) => {
            state.cases.status = 'failure';
            console.error('failed to search cases', action.error);
          },
        }
      ),
      checkCase: createThunk(
        async (caseId: string, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;

          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          return await checkCasesExists([caseId], gql);
        },
        {
          pending: (state) => {
            state.caseValidity.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.caseValidity.validCases = action.payload.validCases;
            state.caseValidity.inValidCases = [
              ...state.caseValidity.inValidCases,
              ...action.payload.inValidCases,
            ];
            state.caseValidity.status = 'complete';
          },
          rejected: (state, action) => {
            state.caseValidity.status = 'failure';

            console.error('failed to check cases', action.error);
          },
        }
      ),
      checkCases: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;

          const searchCases = state.caseManager.cases.data.results.map(
            (caseResult) => caseResult.folderId
          );
          const existingValidCases = state.caseManager.caseValidity.validCases;
          const existingInValidCases =
            state.caseManager.caseValidity.inValidCases;
          const casesToCheck = searchCases.filter(
            (caseId) =>
              !existingValidCases.includes(caseId) &&
              !existingInValidCases.includes(caseId)
          );

          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          return await checkCasesExists(casesToCheck, gql);
        },
        {
          pending: (state, _) => {
            state.caseValidity.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.caseValidity.validCases = [
              ...state.caseValidity.validCases,
              ...action.payload.validCases,
            ];
            state.caseValidity.inValidCases = [
              ...state.caseValidity.inValidCases,
              ...action.payload.inValidCases,
            ];
            state.caseValidity.status = 'complete';
          },
          rejected: (state, action) => {
            state.caseValidity.status = 'failure';
            state.caseValidity.validCases = [];
            state.caseValidity.inValidCases = [];
            console.error('failed to check cases', action.error);
          },
        }
      ),
      pollCases: createThunk((_, thunkAPI) => {
        const { signal, dispatch, getState } = thunkAPI;
        const state = getState() as RootState;
        const pollingMillis = state.config.casePollingInterval ?? 12000;

        let dispatchPromise: DispatchPromise;

        const pollInterval = setInterval(() => {
          dispatchPromise = dispatch(searchCases({ isPolling: true }));
        }, pollingMillis);

        signal.addEventListener('abort', () => {
          clearInterval(pollInterval);
          dispatchPromise?.abort();
        });
      }),
      pollStatusesTags: createThunk((_, thunkAPI) => {
        const { signal, dispatch, getState } = thunkAPI;
        const state = getState() as RootState;
        const pollingMillis = state.config.caseStatusTagsPollInterval ?? 12000;
        let dispatchPromiseStatuses: DispatchPromise;
        let dispatchPromiseTags: DispatchPromise;

        const pollInterval = setInterval(() => {
          dispatchPromiseStatuses = dispatch(
            fetchStatuses({ isPolling: true })
          );
          dispatchPromiseTags = dispatch(fetchTags({ isPolling: true }));
        }, pollingMillis);

        signal.addEventListener('abort', () => {
          clearInterval(pollInterval);
          dispatchPromiseStatuses?.abort();
          dispatchPromiseTags?.abort();
        });
      }),
      getCaseSDO: createThunk(
        async (folderId: string, thunkAPI) => {
          const { getState, dispatch } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const userInfo = state.caseManager.userInfo;
          const caseDetail = await getCase(folderId, gql);
          const { createdBy, createdByName } = caseDetail;
          if (!createdBy) {
            return { ...caseDetail, createdByName: 'N/A' };
          } else if (userInfo && createdBy) {
            const user = userInfo[createdBy];
            if (user) {
              return { ...caseDetail, createdByName: getNameOrEmail(user) };
            }
          }
          const caseOwner = await getOwner({
            data: {
              createdBy,
              createdByName,
            },
            gql,
            dispatch,
            userInfo,
          });

          return { ...caseDetail, createdByName: caseOwner };
        },
        {
          pending: (state) => {
            state.selectedCase.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.selectedCase.data = action.payload;
            state.selectedCase.status = 'complete';
          },
          rejected: (state, action) => {
            state.selectedCase.status = 'failure';
            console.error('failed to load case for editing', action.error);
          },
        }
      ),
      saveCase: createThunk(
        async (
          caseSdo: Omit<InvestigateCaseSDO, 'modifiedDateTime'>,
          thunkAPI
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }

          const folderContentTemplateSchemaId =
            selectFolderContentTemplateSchema(state).id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const updateInvestigateCase = {
            ...caseSdo,
            modifiedDateTime: new Date().toISOString(),
            modifiedBy: state.user.user.userId,
          };

          const { data } = await updateCase(
            caseSdo.sdoId,
            folderContentTemplateSchemaId,
            updateInvestigateCase,
            gql
          );
          return data;
        },
        {
          pending: (state) => {
            state.selectedCase.status = 'loading';
            console.log('case updating');
          },
          fulfilled: (state, action) => {
            state.selectedCase.status = 'complete';
            state.selectedCase.data = {
              ...state.selectedCase.data,
              ...action.payload,
            };
            state.cases.data.results = state.cases.data.results.map(
              (caseItem) =>
                caseItem.folderId === action.payload.folderId
                  ? { ...caseItem, ...action.payload }
                  : caseItem
            );
            const now = Date.now();
            const newStatus: CaseStatus = {
              id: state.selectedCase.data.sdoId,
              statusId: action.payload.statusId,
              expiresAt: now + 7500,
            };

            addCaseStatusToLocalStorage(newStatus);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseUpdatedSuccess'),
              {
                variant: 'success',
              }
            );
            const lsEditedCases = getLocalStorage<Case[]>(EDIT_CASE_KEY) || [];
            const updatedEditedCases = lsEditedCases.filter(
              (editedCase) => editedCase.folderId !== action.payload.folderId
            );
            setLocalStorage(EDIT_CASE_KEY, [
              ...updatedEditedCases,
              action.payload,
            ]);
          },
          rejected: (state) => {
            state.selectedCase.status = 'failure';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseUpdateFailed'),
              {
                variant: 'error',
              }
            );
            console.error('failed to update case');
          },
        }
      ),
      deleteFile: createThunk(
        async ({ tdoId }: { tdoId: string }, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          await softDeleteFile({ tdoId, gql });
        },
        {
          pending: (state) => {
            state.deleteFile.status = 'loading';
          },
          fulfilled: (state) => {
            state.deleteFile.status = 'complete';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('fileDeletedSuccessfully'),
              {
                variant: 'success',
              }
            );
          },
          rejected: (state) => {
            state.deleteFile.status = 'failure';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('fileDeletionFailed'),
              {
                variant: 'error',
              }
            );
          },
        }
      ),
      toggleCaseDrawer: create.reducer(
        (state, action: PayloadAction<string | undefined>) => {
          // If payload is undefined, create new case, otherwise edit case
          state.isCaseDrawerOpen = !state.isCaseDrawerOpen;
          state.editingCaseFolderId = action.payload;
          state.selectedCase.status = 'idle';
        }
      ),
      toggleShareDrawer: create.reducer((state) => {
        state.isShareDrawerOpen = !state.isShareDrawerOpen;
      }),
      setUserInfo: create.reducer(
        (state, action: PayloadAction<BasicUserInfo['basicUserInfo']>) => {
          state.userInfo[action.payload.id] = action.payload;
        }
      ),
      fileUploadedSuccessfully: createThunk(
        async ({ tdoId }: { tdoId: string }, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }

          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          return await fetchFileName(tdoId, gql);
        },
        {
          fulfilled: (_state, action) => {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('uploadFileSuccess', {
                name: action.payload,
              }),
              {
                variant: 'success',
              }
            );
          },
          rejected: (_state, action) => {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('uploadFileSuccess', {
                name: action.meta.arg.tdoId,
              }),
              {
                variant: 'error',
              }
            );
          },
        }
      ),
      setStatusMenuSelectedId: create.reducer(
        (state, action: PayloadAction<string>) => {
          state.statusMenuSelectedId = action.payload;
        }
      ),
      setRowTableContextMenu: create.reducer(
        (state, action: PayloadAction<CaseResult>) => {
          state.rowDataTableContext = action.payload;
        }
      ),
    };
  },
  selectors: {
    selectRootFolderId: (state) => state.rootFolder.id,
    selectFolderContentTemplateSchema: (state) =>
      state.folderContentTemplateSchema,
    selectIsCaseDrawerOpen: (app) => app.isCaseDrawerOpen,
    selectCaseData: (state) => state.selectedCase.data,
    selectCaseStatus: (state) => state.selectedCase.status,
    selectCases: (state) => state.cases,
    selectSortBy: (state) => state.sortBy,
    selectSortDirection: (state) => state.sortDirection,
    selectEditingCaseFolderId: (state) => state.editingCaseFolderId,
    selectLimit: (state) => state.limit,
    selectOffset: (state) => state.offset,
    selectCreateCase: (state) => state.createCase,
    selectCreateCaseStatus: (state) => state.createCase.status,
    selectCaseFilter: (state) => state.caseFilter,
    selectValidCases: (state) => state.caseValidity.validCases,
    selectInvalidCases: (state) => state.caseValidity.inValidCases,
    selectCaseValidityStatus: (state) => state.caseValidity.status,
    selectStatusMenuSelectedId: (state) => state.statusMenuSelectedId,
    selectRowTableContext: (state) => state.rowDataTableContext,
  },
});

export const {
  searchCases,
  pollCases,
  pollStatusesTags,
  createNewCase,
  deleteCase,
  rootFolder,
  folderContentTemplateSchema,
  toggleCaseDrawer,
  getCaseSDO,
  saveCase,
  deleteFile,
  setLimit,
  setOffset,
  setSort,
  setCaseFilter,
  setUserInfo,
  checkCases,
  checkCase,
  fileUploadedSuccessfully,
  toggleShareDrawer,
  setStatusMenuSelectedId,
  setRowTableContextMenu,
} = caseManagerSlice.actions;

export const {
  selectRootFolderId,
  selectFolderContentTemplateSchema,
  selectIsCaseDrawerOpen,
  selectCaseData,
  selectEditingCaseFolderId,
  selectCaseStatus,
  selectCases,
  selectLimit,
  selectOffset,
  selectCreateCase,
  selectCreateCaseStatus,
  selectSortBy,
  selectSortDirection,
  selectCaseFilter,
  selectValidCases,
  selectInvalidCases,
  selectCaseValidityStatus,
  selectStatusMenuSelectedId,
  selectRowTableContext,
} = caseManagerSlice.selectors;

export const { actions: caseManagerActions, reducer: caseManagerReducer } =
  caseManagerSlice;
