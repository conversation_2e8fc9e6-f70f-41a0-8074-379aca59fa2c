import './index.scss';
import { noStatusLabelsTagsFound, searchEmptyExplore } from '@assets/images';
import { I18nTranslate } from '@i18n';

interface EmptyStateProps {
  startExploring?: boolean;
  selectACategory?: boolean;
}

const EmptyState = ({ startExploring, selectACategory }: EmptyStateProps) => {
  const intl = I18nTranslate.Intl();
  return (
    <tr>
      <td className="empty-state__container">
        {selectACategory ? (
          <>
            <img
              // TODO: Define real type for images - not just declare an empty module
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              src={searchEmptyExplore}
              alt={intl.formatMessage({ id: 'emptyState' })}
            />
            <h2>{intl.formatMessage({ id: 'selectACategory' })}</h2>
            <p>{intl.formatMessage({ id: 'clickTheCategoryWheel' })}</p>
          </>
        ) : startExploring ? (
          <>
            <img
              // TODO: image imports should have a proper type definition
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              src={searchEmptyExplore}
              alt={intl.formatMessage({ id: 'emptyState' })}
              draggable={false}
            />
            <h2>{intl.formatMessage({ id: 'startExploring' })}</h2>
            <p>{intl.formatMessage({ id: 'searchForAnything' })}</p>
          </>
        ) : (
          <>
            <img
              // TODO: image imports should have a proper type definition
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              src={noStatusLabelsTagsFound}
              alt={intl.formatMessage({ id: 'emptyState' })}
              draggable={false}
            />
            <h2>{intl.formatMessage({ id: 'noResultsFound' })}</h2>
            <p>{intl.formatMessage({ id: 'refineAndTryAgain' })}</p>
          </>
        )}
      </td>
    </tr>
  );
};

export default EmptyState;
