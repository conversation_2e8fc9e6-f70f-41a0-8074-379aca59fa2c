import AutocompleteVirtualizedSelect from '@components/AutocompleteVirtualizedSelect';
import './index.scss';
import { I18nTranslate } from '@i18n';
import CloseIcon from '@mui/icons-material/Close';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  IconButton,
} from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import {
  moveFile,
  searchFolders,
  selectCurrentCaseId,
  selectFolders,
  selectMoveFileDialogBulkFileIds,
  selectMoveFileDialogIsBulk,
  selectSelectedFileId,
  setCurrentCaseId,
  toggleMoveFileDialog,
} from '@store/modules/caseDetail/slice';
import { setShowMoveToCaseDialog } from '@store/modules/search/slice';
import { savePendingMoveFileToLocalStorage } from '@utils/saveToLocalStorage';
import { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

interface Props {
  open: boolean;
  context: string;
  currentFolderId: string;
}

export default function MoveFileDialog({
  open,
  context,
  currentFolderId,
}: Props) {
  const dispatch = useAppDispatch();
  const intl = I18nTranslate.Intl();
  const folders = useSelector(selectFolders);

  const [selectedCaseId, setSelectedCaseId] = useState<string | undefined>();
  const selectedFileId = useSelector(selectSelectedFileId);
  const moveFileDialogIsBulk = useSelector(selectMoveFileDialogIsBulk);
  const currentCaseId = useSelector(selectCurrentCaseId);
  const moveFileDialogBulkFileIds = useSelector(
    selectMoveFileDialogBulkFileIds
  );

  useEffect(() => {
    if (!currentCaseId && currentFolderId) {
      dispatch(setCurrentCaseId(currentFolderId));
    }
  }, [currentCaseId, currentFolderId]);

  const handleConfirmBulk = () => {
    moveFileDialogBulkFileIds.forEach((fileId) => {
      const currentCaseFolderId =
        currentFolderId ??
        folders.data.results.find((folder) => folder.folderId === currentCaseId)
          ?.folderId ??
        '';
      const selectedCase = folders.data.results.find(
        (folder) => folder.id === selectedCaseId
      );

      if (fileId && currentCaseFolderId && selectedCase) {
        dispatch(
          moveFile({
            fileId,
            oldFolderId: currentCaseFolderId,
            newFolderId: selectedCase.folderId,
          })
        );
        savePendingMoveFileToLocalStorage(
          fileId,
          currentCaseFolderId,
          selectedCase.folderId
        );
      }
    });

    setSelectedCaseId(undefined);
    if (context === 'search') {
      dispatch(setShowMoveToCaseDialog(false));
    } else if (context === 'caseDetail') {
      dispatch(toggleMoveFileDialog());
    }
  };

  const handleConfirm = () => {
    const currentCaseFolderId =
      currentFolderId ??
      folders.data.results.find((folder) => folder.folderId === currentCaseId)
        ?.folderId ??
      '';
    const selectedCase = folders.data.results.find(
      (folder) => folder.id === selectedCaseId
    );

    if (selectedFileId && currentCaseFolderId && selectedCase) {
      dispatch(
        moveFile({
          fileId: selectedFileId,
          oldFolderId: currentCaseFolderId,
          newFolderId: selectedCase.folderId,
        })
      );
      savePendingMoveFileToLocalStorage(
        selectedFileId,
        currentCaseFolderId,
        selectedCase.folderId
      );
    }
    setSelectedCaseId(undefined);
    if (context === 'search') {
      dispatch(setShowMoveToCaseDialog(false));
    } else if (context === 'caseDetail') {
      dispatch(toggleMoveFileDialog());
    }
  };

  const handleClose = () => {
    if (context === 'search') {
      dispatch(setShowMoveToCaseDialog(false));
    } else if (context === 'caseDetail') {
      dispatch(toggleMoveFileDialog());
    }
    setSelectedCaseId(undefined);
  };

  const filteredFolders = folders.data.results.filter(
    (folder) => folder.id !== currentCaseId
  );

  const handleCaseChange = (value: string) => {
    setSelectedCaseId(value);
  };

  const loadMoreItems = useCallback(() => {
    dispatch(
      searchFolders({
        offset: folders.data.to + 1,
        limit: folders.data.limit,
      })
    );
  }, [dispatch, folders.data.to, folders.data.limit]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      className="move-file-dialog"
      data-testid="move-file-dialog"
    >
      <div className="move-file-dialog__header">
        <div className="move-file-dialog__title">
          {intl.formatMessage({ id: 'selectAFolder' })}
        </div>
        <IconButton onClick={handleClose}>
          <CloseIcon />
        </IconButton>
      </div>

      <div className="move-file-dialog__sub_title">
        {intl.formatMessage({ id: 'chooseCase' })}
      </div>

      <DialogContent data-testid="move-file-dropdown">
        {folders.data.results.length === 0 && folders.status === 'loading' ? (
          <div className="loader-wrapper">
            <CircularProgress />
          </div>
        ) : (
          <AutocompleteVirtualizedSelect
            options={filteredFolders
              .map((folder) => ({
                id: folder.id,
                label: folder.caseId,
              }))
              .filter((caseFolder) => caseFolder.id !== currentCaseId)}
            value={selectedCaseId}
            hasMore={folders.data.totalResults > folders.data.to}
            loadMoreItems={loadMoreItems}
            placeholder={intl.formatMessage({ id: 'selectACasePlaceholder' })}
            onChange={handleCaseChange}
            loading={folders.status}
          />
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} className="cancel-button">
          {I18nTranslate.TranslateMessage('close')}
        </Button>
        <Button
          onClick={moveFileDialogIsBulk ? handleConfirmBulk : handleConfirm}
          disabled={!selectedCaseId}
          className="confirm-button"
          data-testid="confirm-button"
        >
          {I18nTranslate.TranslateMessage('move')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
