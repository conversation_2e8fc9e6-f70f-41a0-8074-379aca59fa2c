.create-case {
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;

  .create-case__title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    background-color: var(--background-primary);
  }

  .create-case__heading-title {
    font-size: 18px;
    font-weight: 600;
    margin-left: 30px;
    color: var(--text-primary)
  }

  .create-case__field-title {
    font-size: 12px;
    line-height: 30px;
    flex: 1
  }

  .create-case__field-title-disabled {
    font-size: 12px;
    line-height: 30px;
    color: #B3BEC4;
  }

  .create-case__main-container {
    flex: 1;
    padding: 30px;
    background-color: var(--background-primary);
  }

  .create-case__case-name-id-container {
    display: flex;
    gap: 10px;

    .Sdk-MuiInputBase-input {
      padding: 14px;
    }

    .create-case-id {
      width: 60%;
    }
  }

  .create-case__vertical-field-gap {
    margin-top: 27px;
    position: relative;
  }

  .create-case__description-input {
    font-size: 14px;
    padding-bottom: 20px;
  }

  .create-case__description-count {
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 12px;
    color: var(--text-secondary);
    text-align: right;
    padding: 5px 10px;
  }

  .create-case__footer-container {
    height: 60px;
    background-color: var(--background-primary);
  }

  .create-case__action-button-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    margin-right: 30px;
    margin-bottom: 10px;
  }

  .create-case__cancel-button {
    margin-right: 15px;
    text-transform: none;
  }

  .create-case__case-status-date-container {
    display: flex;
    gap: 10px;
  }

  .create-case__status-form-field {
    min-width: 250px;

    .Sdk-MuiSelect-select {
      padding: 14px 14px;
    }
    .create-case__clear-icon {
      width: 15px;
      height: 15px;
      opacity: 0.6;
      color: var(--button-inner);
      padding: 2px;
      border-radius: 50%;
      background-color: var(--chip-background-grey);
      margin-right: 18px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .create-case__date-picker-form-field {
    width: 170px;

    .Sdk-MuiInputBase-input {
      padding: 14px 14px;
    }
  }
}
