import { describe, it, expect } from 'vitest';
import en from './en-US.json';
import es from './es-SP.json';
import fr from './fr-FR.json';

describe('i18n key diff', () => {
  it('should not have missing keys in es-SP compared to en-US', () => {
    const enKeys = Object.keys(en);
    const esKeys = Object.keys(es);
    const missingInEs = enKeys.filter((key) => !esKeys.includes(key));
    if (missingInEs.length) {
      console.log('Missing in es-SP:', missingInEs);
    }
    expect(missingInEs).toEqual([]);
  });

  it('should not have missing keys in fr-FR compared to en-US', () => {
    const enKeys = Object.keys(en);
    const frKeys = Object.keys(fr);
    const missingInFr = enKeys.filter((key) => !frKeys.includes(key));
    if (missingInFr.length) {
      console.log('Missing in fr-FR:', missingInFr);
    }
    expect(missingInFr).toEqual([]);
  });
});
