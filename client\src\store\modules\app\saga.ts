import { Action, UnknownAction } from '@reduxjs/toolkit';
import {
  folderContentTemplateSchema,
  rootFolder,
} from '@store/modules/caseManager/slice';
import {
  evidenceTypeSchema,
  onSetConfig,
  selectConfig,
  statusSchema,
  tagSchema,
} from '@store/modules/config/slice';
import { modules, User } from '@veritone/glc-redux';
import { find, isEmpty } from 'lodash';
import {
  all,
  call,
  delay,
  Effect,
  fork,
  put,
  race,
  select,
  take,
  takeLatest,
} from 'redux-saga/effects';
import { appActions, bootFinished, booting } from './slice';

const {
  user: {
    fetchUser,
    fetchEnabledApps,
    FETCH_USER,
    FETCH_USER_SUCCESS,
    FETCH_USER_FAILURE,
    FETCH_USER_APPLICATIONS,
    FETCH_USER_APPLICATIONS_SUCCESS,
    FETCH_USER_APPLICATIONS_FAILURE,
    LOGOUT,
    LOGOUT_FAILURE,
    LOGOUT_SUCCESS,
    selectUser,
  },
  auth: { setOAuthToken, OAUTH_GRANT_FLOW_SUCCESS },
  config: { getConfig },
} = modules;

export const getAppStartupDependencies = function* (): Generator<
  unknown,
  void,
  unknown
> {
  // fetch stuff
  yield all([
    // TODO: Remove any when redux-saga types are updated
    put(fetchEnabledApps()),
    // ...other app dependencies
  ]);

  // wait for results
  const actions: unknown[] = [
    // @ts-expect-error TODO: Rewrite to use object/key accepting version of `race`
    ...(yield race([
      take(FETCH_USER_APPLICATIONS_SUCCESS),
      // @ts-expect-error TODO: Fix the type of `take` or rewrite so this works - why an array?
      take([
        // requestError
        (a: UnknownAction) => a.type === FETCH_USER_APPLICATIONS && a.error,
        // api error
        FETCH_USER_APPLICATIONS_FAILURE,
      ]),
    ])),
    // ...etc
  ];

  const error = find(actions, { error: true });
  if (error) {
    console.error('there was an error', error);
  }
};

export const redirectToVeritoneInternalLogin = function* () {
  // TODO: type getConfig correctly
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const config: Window['config'] = yield select(getConfig);
  if (config?.loginRoute) {
    // TODO: Should we be validating loginRoute is a string? are we okay with other types?
    // eslint-disable-next-line @typescript-eslint/no-base-to-string
    const loginRoute = new URL(String(config?.loginRoute));
    if (loginRoute.hostname.match(/\.veritone\.com$/)) {
      const redirect = new URL(window.location.href);
      if (redirect.hostname.match(/\.veritone\.com$/)) {
        loginRoute.searchParams.set('redirect', redirect.href);
        window.location.href = loginRoute.href;
      } else {
        window.location.href = loginRoute.href;
      }
    }
  }
};

export const fetchUserWithStoredTokenOrCookie = function* (): Generator<
  Effect,
  User | false,
  unknown
> {
  // @ts-expect-error TODO: Fix the types here
  const existingOAuthToken: string | undefined = yield call(
    [localStorage, 'getItem'],
    'OAuthToken'
  );

  if (existingOAuthToken) {
    yield put(setOAuthToken(existingOAuthToken));
  }

  yield put(fetchUser());

  // @ts-expect-error TODO: Rewrite to use the object/key accepting version of `race`
  const [successAction] = yield race([
    take(FETCH_USER_SUCCESS),
    // @ts-expect-error TODO: Fix the type of `take`
    take([
      (a: Action) => a.type === FETCH_USER && 'error' in a && a.error,
      FETCH_USER_FAILURE,
    ]),
  ]);

  // todo: this could differentiate between auth error (expired token) and failure (api error)
  // TODO: Implement/Use action creators from glc-redux
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access
  return successAction ? successAction.payload : false;
};

export function* storeTokenAfterSuccessfulOAuthGrant() {
  yield takeLatest(
    OAUTH_GRANT_FLOW_SUCCESS,
    function* (action: ReturnType<typeof setOAuthToken>) {
      yield call([localStorage, 'setItem'], 'OAuthToken', action.payload);
    }
  );
}

export const redirectAndAwaitOAuthGrant = function* () {
  yield put(bootFinished());

  // retry boot after logging in
  yield take(OAUTH_GRANT_FLOW_SUCCESS);
  yield put(booting());
};

export function* clearStoredTokenAfterLogout() {
  yield takeLatest(LOGOUT, clearStoredTokenAfterLogoutHandle);
}

export function* clearStoredTokenAfterLogoutHandle() {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const user: User = yield select(selectUser);
  if (!isEmpty(user)) {
    yield call([localStorage, 'removeItem'], 'OAuthToken');

    yield take([LOGOUT_FAILURE, LOGOUT_SUCCESS]);
    yield delay(1000);
    // TODO: Fix the types - using typed-redux-saga here would likely help
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const { loginRoute }: { loginRoute: string | undefined | null } =
      yield select(selectConfig);
    try {
      const loginUrl = new URL((loginRoute || '').toString());
      loginUrl.searchParams.set('redirect', window.location.origin);
      window.location.assign(loginUrl.href);
    } catch {
      console.warn('Warning: Login route is not a valid url');
    }
  }
}

export function* watchAppBootHandle() {
  // Define the watchAppBootHandle function before using it
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const config: Window['config'] = yield select(getConfig);
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const user: User = yield fetchUserWithStoredTokenOrCookie();

  yield put(onSetConfig(config));

  if (user) {
    // login success with stored credentials or cookie
    yield getAppStartupDependencies();

    yield put(rootFolder());
    yield put(folderContentTemplateSchema());
    yield put(statusSchema());
    yield put(tagSchema());
    yield put(evidenceTypeSchema());

    yield put(bootFinished());
  } else {
    if (config?.useOAuthGrant) {
      yield redirectAndAwaitOAuthGrant();
    } else {
      yield redirectToVeritoneInternalLogin();
    }
  }
}

export function* watchAppBoot() {
  yield takeLatest(appActions.booting, watchAppBootHandle);
}

export function* appSagas() {
  yield all([
    fork(watchAppBoot),
    fork(storeTokenAfterSuccessfulOAuthGrant),
    fork(clearStoredTokenAfterLogout),
  ]);
}

export default appSagas;
