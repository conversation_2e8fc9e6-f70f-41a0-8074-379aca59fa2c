.case-details {
  flex: 1;
  border-radius: 8px;
  background: var(--background-secondary);
  min-width: 425px;
  overflow-y: scroll;

  .loading-icon {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-tabs {
    border-bottom: 1px solid var(--app-status-tab-border);

    .Sdk-MuiTab-root {
      color: var(--text-primary);
      font-weight: 500;
      font-size: 14px;
    }

    .Sdk-MuiTabs-flexContainer {
      justify-content: center;
    }

    .Sdk-MuiTabs-indicator {
      background-color: black;
      height: 2px;
    }
  }

  &__content {
    padding: 30px 40px 0 40px;
    overflow: visible;

    .buttons-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .text-button {
        color: white;
        height: 40px;
        padding: 0 25px;
        background: var(--button-dark-blue);
        text-transform: capitalize;
      }

      .group-icon-buttons {
        display: flex;
        margin-right: -10px;

        .icon-button {
          color: var(--text-primary);
        }
      }
    }

    .case__title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 10px;
      gap: 20px;

      .group-icon-buttons {
        display: flex;
        margin-right: -10px;

        .icon-button {
          color: var(--text-primary);
        }
      }
    }

    .case-id {
      font-size: 12px;
      margin-bottom: 15px;
    }

    .case-status {
      margin-bottom: 22px;
    }

    .case-dates {
      gap: 30px;
      display: flex;
      flex-direction: row;
      margin-bottom: 20px;
    }

    .case-detail {
      font-size: 14px;
      white-space: nowrap;
    }

    .case-detail-bold {
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
    }

    .case-owner {
      gap: 10px;
      display: flex;
      margin-bottom: 20px;
    }

    .case-description {
      font-size: 14px;
      margin-bottom: 20px;
    }

    .case-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-bottom: 22px;
    }

    .case-tag {
      font-size: 10px;
      font-weight: bold;
      line-height: 16px;
      padding: 5px 15px;
      border-radius: 90px;
      background-color: var(--chip-background-grey);
      white-space: nowrap;
      overflow: hidden;
      max-width: 200px;
      text-overflow: ellipsis;
    }
  }

  .case-evidence {
    padding: 0 40px 30px 40px;

    .case-evidence-title {
      font-size: 14px;
      font-weight: bold;
    }

    .case-evidence-list {
      columns: 2;
      margin: 10px 0;
      max-height: 100px;
      padding-left: 20px;

      .case-evidence-list-item {
        font-size: 11px;
        text-transform: capitalize;
      }
    }

    .case-evidence-loading {
      display: flex;
    }
  }
}
