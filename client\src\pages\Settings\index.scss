.settings {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 10px;
  margin-right: 15px;
  margin-bottom: 15px;
  flex-direction: column;

  .settings__header {
    gap: 15px;
    display: flex;
    margin-left: 21px;
    align-items: center;
    padding-bottom: 26px;

    .settings__title {
      height: 26px;
      display: flex;
      font-size: 14px;
      align-items: center;
      color: var(--text-primary);
    }
  }

  .settings__content {
    flex: 1;
    gap: 15px;
    display: flex;
    overflow: auto;
    flex-direction: row;

    .settings__tabs {
      height: 100%;
      min-width: 345px;
      max-width: 345px;
      border-radius: 8px;
      padding: 24px 15px;
      background: var(--background-secondary);

      .settings__tabs-description {
        font-size: 14px;
        margin: 0 5px 15px;
        color: var(--text-primary);
      }

      .settings__tabs-container {
        .settings__tab {
          align-items: flex-start;
          color: var(--text-primary);
          background: var(--background-secondary);
          border-bottom: 1px solid var(--app-status-tab-border);

          &.selected {
            background: var(--background-primary);
            border-bottom: 1px solid var(--app-status-tab-border);
          }
        }
      }
    }

    .settings__option-container {
      flex: 1;
      display: flex;
      padding: 24px 15px;
      border-radius: 8px;
      flex-direction: column;
      background: var(--background-secondary);

      .settings__option-header {
        display: flex;
        font-size: 14px;
        font-weight: 700;
        padding: 0 15px 25px;
        color: var(--text-primary);
        justify-content: space-between;

        .settings__option-header-buttons {
          gap: 20px;
          display: flex;

          .settings__option-header-button-text {
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 5px;
          }

          .button-clicked {
            background: var(--background-primary);
          }

          .settings__option-header-button {
            gap: 2px;
            color: var(--text-primary);
            border: 1px solid var(--text-primary);

            &.editMode {
              border: none;
              color: white;
              background: var(--button-dark-blue);
            }

            &.disabled {
              border: none;
              background: none;
              color: var(--button-text-disabled);
            }
          }

          .Mui-disabled {
            border: none;
            background: none;
            color: var(--button-text-disabled);
          }
        }
      }

      .settings__reorder-table {
        flex-basis: fit-content;
        height: calc(100% - 65px);
      }
    }
  }
}

.settings__dialog-description {
  font-size: 16px;
  margin-bottom: 20px;
  color: var(--text-primary);
  text-align: start;
  
}

.settings__dialog-radio-group {
  margin-top: 20px;
  column-gap: 40px;

  .Sdk-MuiFormControlLabel-root {
    margin-left: 0;
    margin-right: 0;
  }
}

.delete-dialog__description {
  font-size: 16px;
  text-align: left;
  margin: 0;
}

.error {
  color: red;
}

.settings__dialog-text-field-container {
  .Sdk-MuiInputBase-root input {
    padding: 14px 14px;
    width: 402px;
  }
}

.settings__dialog-error {
  display: flex;
  align-items: center;
  color: var(--button-destructive-action);
  margin-left: 5px;
  word-break: break-word;
  overflow-wrap: break-word;
}

.settings__dialog-color-picker {
  height: 51px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.23);
}

.settings__dialog-text-field-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
}

.settings__dialog-label {
  padding-left: 5px;
  font-size: 12px;
  color: var(--text-primary);
}
