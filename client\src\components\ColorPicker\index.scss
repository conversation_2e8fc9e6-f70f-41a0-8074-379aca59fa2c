.color-picker__popover {
  display: flex;
  flex-flow: column;
  justify-content: space-around;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  width: 264px;

  .react-colorful {
    width: 234px;
    height: 184px;

    .react-colorful__saturation {
      border-radius: 0;
    }

    .react-colorful__hue {
      margin-top: 16px;
      height: 8px;
      border-radius: 20px;

      .react-colorful__hue-pointer {
        width: 8px;
        height: 8px;
        border-radius: 16px;
        border: 2px solid var(--color-picker-hue-pointer);
      }
    }
  }

  .color-picker__hex-input {
    width: 86px;
    height: 28px;

    input {
      width: 100%;
      height: 100%;
      text-align: center;
      font-size: 14px;
      text-transform: uppercase;
      border: solid 1px var(--border-color);
      border-radius: 4px;
    }
  }

  .color-picker__popover-presets-heading {
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;
    text-align: left;
    width: 100%;
  }

  .color-picker__popover-presets {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .color-picker__popover-presets-swatch {
      width: 24px;
      height: 24px;
      margin: 4px;
      border-radius: 24px;
      cursor: pointer;

      &:hover {
        box-shadow: 0 0 0 2px var(--text-disabled);
        border: solid 2px var(--background-primary);
      }
    }
  }
}