import { isBoolean } from 'lodash';

const SET_BLUR_KEY = 'investigate-set-blur';

export const getBlurValue = () => {
  const blurValue = localStorage.getItem(SET_BLUR_KEY);
  if (blurValue === null) {
    return false;
  }
  const blur: unknown = JSON.parse(blurValue);
  if (isBoolean(blur)) {
    return blur;
  }
  return false;
};

export const setBlurValueLocalStorage = (blurValue: boolean) => {
  localStorage.setItem(SET_BLUR_KEY, blurValue.toString());
};
