interface EnvConfig {
  baseUrl: string;
  apiRoot: string;
}

export const envData: { [key: string]: EnvConfig } = {
  local: {
    baseUrl: 'https://local.veritone.com:4200',
    apiRoot: 'https://api.stage.us-1.veritone.com',
  },
  dev: {
    baseUrl: 'https://investigate.dev.us-1.veritone.com',
    apiRoot: 'https://api.dev.us-1.veritone.com',
  },
  stage: {
    baseUrl: 'https://investigate.stage.us-1.veritone.com',
    apiRoot: 'https://api.stage.us-1.veritone.com',
  },
  prod: {
    baseUrl: 'https://investigate.veritone.com',
    apiRoot: 'https://api.veritone.com',
  },
};
