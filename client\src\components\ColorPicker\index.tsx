import './index.scss';
import { I18nTranslate } from '@i18n';
import { Popover, PopoverOrigin } from '@mui/material';
import cn from 'classnames';
import { HexColorInput, HexColorPicker } from 'react-colorful';

interface Props {
  onChange?: (color: string) => void;
  color?: string;
  presetColors?: string[];
  open: boolean;
  anchor: React.RefObject<HTMLElement | null>;
  toggle: (open: boolean) => void;
  anchorOrigin: PopoverOrigin;
  transformOrigin: PopoverOrigin;
  classname?: string;
}

const ColorPicker = ({
  color,
  presetColors,
  onChange,
  open,
  anchor,
  toggle,
  anchorOrigin,
  transformOrigin,
  classname,
}: Props) => {
  const presets = presetColors ?? [
    '#EF4444',
    '#F97316',
    '#FACC15',
    '#4ADE80',
    '#2DD4BF',
    '#3B82F6',
    '#6467F2',
    '#EC4899',
    '#F43F5E',
    '#D946EF',
    '#8B5CF6',
    '#0EA5E9',
    '#10B981',
    '#84CC16',
  ];

  return (
    <Popover
      classes={{ paper: cn('color-picker__popover', classname) }}
      id="color-picker-popover"
      open={open}
      anchorEl={anchor.current}
      onClose={() => toggle(false)}
      anchorOrigin={anchorOrigin}
      transformOrigin={transformOrigin}
    >
      <HexColorPicker
        color={color}
        onChange={(color) => {
          onChange?.(color);
        }}
        data-testid="hex-color-picker"
      />
      <div
        className="color-picker__hex-input"
        data-testid="color-picker-hex-input"
      >
        <HexColorInput
          color={color}
          onChange={(newColor) => {
            onChange?.(newColor);
          }}
          prefixed
        />
      </div>
      <div className="color-picker__popover-presets-heading">
        {I18nTranslate.TranslateMessage('presetColors')}
      </div>
      <div className="color-picker__popover-presets">
        {presets.map((presetColor) => (
          <div
            key={`PresetColor-${presetColor}`}
            className="color-picker__popover-presets-swatch"
            style={{ background: presetColor }}
            onClick={() => {
              onChange?.(presetColor);
            }}
          />
        ))}
      </div>
    </Popover>
  );
};

export default ColorPicker;
