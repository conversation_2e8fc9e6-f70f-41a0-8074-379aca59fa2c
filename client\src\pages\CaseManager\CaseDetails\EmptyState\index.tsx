import './index.scss';
import { CaseDetailNullState } from '@assets/icons';

const EmptyState = ({ title, description }: Props) => (
  <div className="case-detail-empty-state">
    <CaseDetailNullState className="empty-state-image" />
    <h2>{title}</h2>
    <p>{description}</p>
  </div>
);

export interface Props {
  title: React.ReactElement;
  description: React.ReactElement;
}

export default EmptyState;
