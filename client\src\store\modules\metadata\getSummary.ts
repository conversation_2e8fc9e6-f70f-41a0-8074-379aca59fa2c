import { EngineResult } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

const summarizationEngineCategoryId = '24ffd12d-fcda-4f47-8843-b2e5dbfbb01f';

export async function getSummary(
  tdoId: string,
  gql: GQLApi
): Promise<
  | { reason: 'noEngineRun' | 'noResults'; text?: string }
  | { reason?: 'noEngineRun' | 'noResults'; text: string }
> {
  // get the engine results for the tdo
  const engineResults = await gql.getEngineResults({
    tdoId,
    engineCategoryId: summarizationEngineCategoryId,
  });
  // engine has not run yet
  if (engineResults.length === 0) {
    return { reason: 'noEngineRun' };
  }
  const latestEngineResult = pickLatestEngineResult(engineResults);
  // engine has run but no summary
  if (latestEngineResult.jsondata.summary === undefined) {
    return { reason: 'noResults' };
  }
  return { text: latestEngineResult.jsondata.summary };
}

export function pickLatestEngineResult(engineResults: EngineResult[]) {
  let engineResult = engineResults[0];
  for (const result of engineResults) {
    if (
      result.jsondata.modifiedDateTime > engineResult.jsondata.modifiedDateTime
    ) {
      engineResult = result;
    }
  }
  return engineResult;
}
