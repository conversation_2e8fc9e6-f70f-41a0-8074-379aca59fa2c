import axios, {
  AxiosProgressEvent,
  AxiosRequestConfig,
  RawAxiosRequestHeaders,
} from 'axios';

export default class HttpClient {
  env: Record<string, unknown> | undefined;
  defaultHeaders: RawAxiosRequestHeaders;

  constructor(env?: Record<string, unknown>) {
    this.env = env;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    };
  }

  buildUrl(relativeUrl: string) {
    const { protocol, hostname } = window.location;
    const nodeEnv = this.env?.nodeEnv;
    const apiPort =
      typeof nodeEnv === 'string' && nodeEnv.includes('dev') ? ':3002' : '';
    const baseUrl = `${protocol}//${hostname}${apiPort}/api/v1`;

    return relativeUrl.startsWith('/')
      ? baseUrl + relativeUrl
      : `${baseUrl}/${relativeUrl}`;
  }

  buildHeaders(headers?: RawAxiosRequestHeaders): RawAxiosRequestHeaders {
    return { ...this.defaultHeaders, ...headers };
  }

  get<R>(signal?: AbortSignal) {
    return (
      relativeUrl: string,
      headers?: RawAxiosRequestHeaders,
      config: Partial<AxiosRequestConfig> = {}
    ) =>
      this.makeRequest<R>({
        relativeUrl,
        headers,
        method: 'get',
        config,
        signal,
      });
  }

  post<R>(signal?: AbortSignal) {
    return (
      relativeUrl: string,
      body?: unknown,
      headers?: RawAxiosRequestHeaders
    ) =>
      this.makeRequest<R>({
        relativeUrl,
        headers,
        body,
        method: 'post',
        signal,
      });
  }

  patch<R>(signal?: AbortSignal) {
    return (
      relativeUrl: string,
      body: unknown,
      headers?: RawAxiosRequestHeaders
    ) =>
      this.makeRequest<R>({
        relativeUrl,
        body,
        headers,
        method: 'patch',
        signal,
      });
  }

  put<R>(signal?: AbortSignal) {
    return (
      relativeUrl: string,
      body: unknown,
      headers?: RawAxiosRequestHeaders,
      onUploadProgress: (progress: AxiosProgressEvent) => void = () => undefined
    ) =>
      this.makeRequest<R>({
        relativeUrl,
        body,
        headers,
        method: 'put',
        onUploadProgress,
        signal,
      });
  }

  delete<R = never>(signal?: AbortSignal) {
    return (
      relativeUrl: string,
      body?: unknown,
      headers?: RawAxiosRequestHeaders
    ) =>
      this.makeRequest<R>({
        relativeUrl,
        body,
        headers,
        method: 'delete',
        signal,
      });
  }

  raw<R>({
    url,
    data = {},
    headers = {},
    method = 'GET',
    onUploadProgress = () => undefined,
    signal,
    timeout,
  }: {
    url: string;
    data?: unknown;
    headers?: RawAxiosRequestHeaders;
    method?: string;
    onUploadProgress?: (progress: AxiosProgressEvent) => undefined;
    signal?: AbortSignal | undefined;
    timeout?: number;
  }) {
    return axios.request<R>({
      url,
      headers,
      data,
      method,
      onUploadProgress,
      signal,
      timeout,
    });
  }

  makeRequest<R>({
    relativeUrl,
    body,
    headers,
    method,
    config,
    onUploadProgress,
    signal,
  }: {
    relativeUrl: string;
    body?: unknown;
    headers?: RawAxiosRequestHeaders;
    method: string;
    config?: Partial<AxiosRequestConfig>;
    onUploadProgress?: (progress: AxiosProgressEvent) => void;
    signal?: AbortSignal;
  }) {
    return axios.request<R>({
      url: this.buildUrl(relativeUrl),
      headers: this.buildHeaders(headers),
      data: body,
      withCredentials: false,
      method,
      onUploadProgress,
      maxRedirects: 0,
      signal,
      ...config,
    });
  }
}
