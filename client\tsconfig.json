{
  // "files": ["./*.mjs", "./**/*"],
  "references": [{ "path": "./tsconfig.app.json" }, { "path": "./tsconfig.node.json" }],
  "include": ["../libs/**/*.ts", "./node_modules/@types/**/*.d.ts", "../node_modules/@types/**/*.d.ts"],
  "compilerOptions": {
    "baseUrl": ".",
    "jsx": "react-jsx",
    "types": ["node", "@testing-library/react", "react"],
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "allowJs": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "target": "ES2022",
    "paths": {
      "@pages/*": ["src/pages/*"],
      "@components/*": ["src/components/*"],
      "@assets/images": ["src/assets/images"],
      "@assets/icons": ["src/assets/icons"],
      "@i18n": ["src/i18n"],
      "@hooks": ["src/hooks"],
      "@shared-types/*": ["src/types/*"],
      "@utils/*": ["src/utils/*"],
      "@store/*": ["src/store/*"],
      "@theme": ["src/theme"],
    }
  }
}
