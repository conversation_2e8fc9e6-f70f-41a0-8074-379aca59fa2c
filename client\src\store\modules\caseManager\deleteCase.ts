import { CaseSDO } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function deleteCaseBySdoId({
  folderContentTemplateSchemaId,
  sdoId,
  userId,
  gql,
}: {
  folderContentTemplateSchemaId: string;
  sdoId: string;
  userId: string;
  gql: GQLApi;
}) {
  const caseSdo = await gql.getSDO<CaseSDO>({
    schemaId: folderContentTemplateSchemaId,
    id: sdoId,
  });
  const now = new Date();
  const nowStr = now.toISOString();
  const data: CaseSDO = {
    ...caseSdo.data,
    modifiedBy: userId,
    modifiedDateTime: nowStr,
    toBeDeletedTime: nowStr,
  };

  await gql.updateSDO({
    schemaId: folderContentTemplateSchemaId,
    id: sdoId,
    data,
  });
  return sdoId;
}
