import { describe, expect, it } from 'vitest';
import {
  FaceDetectionTerm,
  MathOperators,
  ObjectDetectionTerm,
  isParamTermValid,
} from './slice';

describe('isParamTermValid', () => {
  it('happy path', () => {
    const input = [
      { type: 'entity', entityId: '1' },
      { type: 'operator', operator: 'and' },
      { type: 'entity', entityId: '2' },
    ] as (FaceDetectionTerm | ObjectDetectionTerm)[];
    const got = isParamTermValid(input);
    expect(got.isValid).toBeTruthy();
  });
});

describe('isParamTermValid - test metrics', () => {
  const cases = [
    ['', true],
    ['1', true],
    ['(1)', true],
    ['1|2', true],
    ['(1|2)', true],
    ['(1)|(2)', true],
    ['(1|2)&3', true],
    ['((1|2)&3)', true],
    ['((1|2)&3)|4', true],
    ['(1|2)&(3|4)', true],
    ['((1|2)&(3|4))', true],
    ['1|(2&3)', true],
    ['1|((2&3)|4)', true],
    ['1|(2&(3|4))', true],
    ['&', false],
    ['(', false],
    [')', false],
    ['()', false],
    ['(1', false],
    ['1)', false],
    ['(1|2', false],
    ['1|2)', false],
    ['(1|2)3', false],
    ['1(2|3)', false],
    ['(1|2)(3|4)', false],
    ['1|()|2)', false],
    ['&1', false],
    ['1&', false],
    ['1&&2', false],
    ['(&1&2)', false],
    ['(1&2&)', false],
    ['&(1&2)', false],
    ['(1&2)&', false],
  ];
  it.each(cases)('given %s', (input, expected) => {
    const terms = toTerms(input as string);
    const got = isParamTermValid(terms);
    expect(got.isValid).toBe(expected);
  });
});

function toTerms(input: string) {
  const terms = [] as FaceDetectionTerm[];
  for (let i = 0; i < input.length; i++) {
    const term = {} as FaceDetectionTerm;
    const char = input[i];
    if (char === '&') {
      term.type = 'operator';
      term.operator = MathOperators.AND;
    } else if (char === '|') {
      term.type = 'operator';
      term.operator = MathOperators.OR;
    } else if (char === '(') {
      term.type = 'operator';
      term.operator = MathOperators.OPEN_PARENTHESIS;
    } else if (char === ')') {
      term.type = 'operator';
      term.operator = MathOperators.CLOSE_PARENTHESIS;
    } else {
      term.type = 'entity';
      term.entityId = char;
    }
    terms.push(term);
  }
  return terms;
}
