.filter {
  height: 100%;
  padding: 0 15px;
  min-width: 334px;
  border-radius: 8px;
  background-color: var(--background-secondary);
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 66px;

  form {
    height: 100%;
  }

  .filter__content {
    overflow-y: auto;
    height: 100%;
  }
}

.filter-search {
  padding: 20px 0 15px 0;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .filter-search-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    padding: 0 3px;

    .filter-search-title__reset {
      color: var(--text-link);
      cursor: pointer;
      font-size: 10px;

      &:hover {
        opacity: 0.9;
      }
    }
  }

  .filter-search-searchbar {
    &__input {
      border-radius: 28px;
      background: var(--background-primary);
      height: 40px;
      padding-right: 6px;

      &>input {
        padding-left: 5px;
        font-size: 14px;
      }
    }
  }
}

.filter-search-warning {
  padding: 0 15px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #9e9e9e;
}

.filter-cognition {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-evidence-type {
  display: flex;
  flex-direction: column;

  .Sdk-MuiFormControlLabel-root {
    margin: 0;
    padding: 5px 0;

    &>.Sdk-MuiTypography-root {
      font-size: 14px;
    }

    &:nth-child(even) {
      background: var(--background-primary);
    }
  }
}

.filter-file-type {
  display: flex;
  flex-direction: column;

  .Sdk-MuiFormControlLabel-root {
    margin: 0;
    padding: 5px 0;

    .form-control-label {
      display: flex;
      align-items: center;
      gap: 10px;

      &>svg {
        font-size: 19px;
      }

      &>span {
        font-size: 14px;
      }
    }
  }
}

.filter-panel__footer-actions {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  height: 66px;
  padding: 0 15px 0 34px;
  align-items: center;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid var(--table-border-color);
  background-color: var(--background-secondary);

  .filter-panel__reset-button {
    font-size: 10px;
    font-weight: 400;

    &.Mui-disabled {
      background-color: transparent;
      color: var(--button-text-disabled);
    }
  }

  .filter-panel__submit-button {
    width: 130px;
    height: 36px;
    background-color: var(--button-dark-blue);
    color: var(--button-text-submit);
    border-radius: 4px;

    &.Mui-disabled {
      background-color: var(--button-background-disabled);
      color: var(--button-text-disabled);
    }
  }
}

.filter-panel-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: var(--background-primary);

  &__container {
    height: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      height: 60px;
      padding: 0 10px 0 20px;

      &>p {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
      }

      &>.Sdk-MuiIconButton-root {
        margin-right: -10px;
        color: var(--text-primary);
      }
    }

    &-filter {
      flex: 1;
      background-color: var(--background-primary);
      max-height: 818px;
      margin-left: 10px;
      padding: 0 10px;
      border: 1px solid var(--border-color);
      overflow: hidden;

      & .Sdk-MuiInputBase-root:not(.filter-search-searchbar__input) {
        height: 30px;
        font-size: 12px;
        line-height: 30px;
        box-sizing: border-box;
        display: flex;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        &>.Sdk-MuiOutlinedInput-input:not(.Sdk-MuiSelect-select) {
          padding: 5px 5px 6px;
          margin-top: 2px;
        }

        &>.Sdk-MuiSelect-select {
          display: flex;
          align-items: center;
          padding: 0px;
          margin-left: 5px;
        }
      }
    }
  }

  &__footer {
    gap: 10px;
    padding: 20px 0;
    display: flex;
    justify-content: flex-end;
    background-color: var(--background-primary);

    &-apply-button {
      color: #ffffff;
    }
  }
}
