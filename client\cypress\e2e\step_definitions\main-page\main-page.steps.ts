import { Before, Given, Then } from '@badeball/cypress-cucumber-preprocessor';

Before(() => {
  cy.LoginLandingPage();
});

Given('The main page is loaded', () => {
  cy.get('button[data-test="nav-tab-case-manager"]').click();
  cy.contains('Case Management');
});

Then('The user clicks on Settings button', () => {
  cy.get('button[data-test="nav-tab-settings"]').click();
  cy.contains('Below you can find key settings');
});
