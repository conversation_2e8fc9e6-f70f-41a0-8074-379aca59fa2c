import Settings from '@pages/Settings';
import { screen, within } from '@testing-library/dom';
import { act } from '@testing-library/react';
import { ThemeProvider } from '@theme';
import { SnackbarProvider } from 'notistack';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Layout from '.';
import { render } from '../../../../test/render';

vi.mock(import('notistack'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useSnackbar: () => ({
      enqueueSnackbar: vi.fn(),
      closeSnackbar: vi.fn(),
    }),
    enqueueSnackbar: vi.fn(),
  };
});

describe('Layout', () => {
  beforeEach(() => {
    Object.defineProperty(window, 'aiware', {
      value: {
        on: vi.fn(),
        store: {
          getState: () => ({
            auth: {
              user: {
                preferredLanguage: 'en',
              },
            },
          }),
        },
      },
    });
  });

  it('should show modal save change when redirecting to another page while edit in settings', () => {
    vi.mock('react-router', async () => {
      const actual = await vi.importActual('react-router');
      return {
        ...actual,
        Outlet: () => <Settings />,
        useNavigate: () => vi.fn(),
        // mock blocked route to show save changes confirmation dialog
        useBlocker: vi.fn(() => ({
          state: 'blocked',
        })),
        useLocation: vi.fn(() => ({
          pathname: '/settings',
          search: '',
          hash: '',
          state: null,
          key: 'test-key',
        })),
      };
    });

    vi.useFakeTimers();
    render(
      <ThemeProvider>
        <SnackbarProvider>
          <Layout />
        </SnackbarProvider>
      </ThemeProvider>
    );

    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(screen.getByText('Settings')).toBeTruthy();
    // show save changes confirmation dialog if route is blocked
    expect(
      within(screen.getByTestId('status-dialog-title')).getByText(
        /save changes/i
      )
    ).toBeTruthy();
  });
});
