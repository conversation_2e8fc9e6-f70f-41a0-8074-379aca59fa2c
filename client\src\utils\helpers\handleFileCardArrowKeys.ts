import { VFile } from '@shared-types/types';

export const handleFileCardArrowKeys = (e: KeyboardEvent, files: VFile[]) => {
  e.stopPropagation();
  e.preventDefault();

  const currentCard = document.activeElement;
  const currentCardLeft =
    document.activeElement?.getBoundingClientRect().left || 0;
  const currentCardTop =
    document.activeElement?.getBoundingClientRect().top || 0;
  const isCard = currentCard?.classList.contains('file-card');
  const index = parseInt(currentCard?.getAttribute('data-index') || '0', 10);

  if (isCard && e.key === 'ArrowRight') {
    const nextCard = document.querySelector<HTMLElement>(
      `.file-card[data-index="${index + 1}"]`
    );
    nextCard?.focus();
  }
  if (isCard && e.key === 'ArrowLeft') {
    const nextCard = document.querySelector<HTMLElement>(
      `.file-card[data-index="${index - 1}"]`
    );
    nextCard?.focus();
  }

  if (isCard && e.key === 'ArrowDown') {
    for (let i = index + 1; i < files.length; i++) {
      const nextCard = document.querySelector<HTMLElement>(
        `.file-card[data-index="${i}"]`
      );
      const nextLeft = nextCard?.getBoundingClientRect().left || 0;
      const nextTop = nextCard?.getBoundingClientRect().top || 0;

      if (nextLeft === currentCardLeft && nextTop > currentCardTop) {
        nextCard?.focus();
        break;
      }
    }
  }
  if (isCard && e.key === 'ArrowUp') {
    for (let i = index - 1; i >= 0; i--) {
      const nextCard = document.querySelector<HTMLElement>(
        `.file-card[data-index="${i}"]`
      );

      const nextLeft = nextCard?.getBoundingClientRect().left || 0;
      const nextTop = nextCard?.getBoundingClientRect().top || 0;

      if (nextLeft === currentCardLeft && nextTop < currentCardTop) {
        nextCard?.focus();
        break;
      }
    }
  }
  if (
    !isCard &&
    ['ArrowRight', 'ArrowLeft', 'ArrowDown', 'ArrowUp'].includes(e.key)
  ) {
    const firstCard = document.querySelector<HTMLElement>(
      `.file-card[data-index="0"]`
    );

    firstCard?.focus();
  }
};
