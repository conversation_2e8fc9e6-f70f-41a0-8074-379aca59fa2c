import { FilterFormValue } from '@shared-types/types';
import { configureAppStore } from '@store/index';
import { fireEvent, screen } from '@testing-library/react';
import { ControllerRenderProps } from 'react-hook-form';
import { Provider } from 'react-redux';
import { describe, expect, it, vi } from 'vitest';
import CognitionAutocomplete, { Mode } from '.';
import { render } from '../../../test/render';

vi.mock('@mui/material/Tooltip', () => ({
  default: (props: { title: string; children: React.ReactNode }) => (
    <div data-testid="tooltip-mock">
      {props.title} {props.children}
    </div>
  ),
}));

describe('CognitionAutocomplete', () => {
  it('should only render terms in both autocomplete and preview', () => {
    const initialState = {
      search: {
        entitySearch: { status: 'idle', data: [] },
        librarySearch: { status: 'idle', data: [] },
        objectSearch: { status: 'idle', data: [] },
      },
    };

    const store = configureAppStore(initialState);

    const mockField: ControllerRenderProps<FilterFormValue, 'faceDetections'> =
      {
        value: [
          {
            id: 'string-id-1',
            value: 'test-1',
            type: 'value',
            cognitionType: 'string',
            isValid: false,
            alias: 'A',
          },
        ],
        onChange: vi.fn(),
        onBlur: vi.fn(),
        name: 'faceDetections',
        ref: vi.fn(),
      };

    render(
      <Provider store={store}>
        <CognitionAutocomplete
          field={mockField}
          mode={Mode.FACE}
          disableLibraryCognitionForms={false}
        />
      </Provider>
    );
    expect(
      screen.queryByText(
        'Face and object search requires cognition libraries to exist in the organization.'
      )
    ).not.toBeInTheDocument();
    expect(screen.getByText('test-1')).toBeInTheDocument();
    expect(
      screen.getByTestId(`cognition-preview-item-string-id-1`).textContent
    ).toBe('A');
  });

  it('the autocomplete should be disabled if the disabled flag is true', () => {
    const initialState = {
      search: {
        entitySearch: { status: 'idle', data: [] },
        librarySearch: { status: 'idle', data: [] },
        objectSearch: { status: 'idle', data: [] },
      },
    };

    const store = configureAppStore(initialState);

    const mockField: ControllerRenderProps<FilterFormValue, 'faceDetections'> =
      {
        value: [
          {
            id: 'string-id-1',
            value: 'test-1',
            type: 'value',
            cognitionType: 'string',
            isValid: false,
            alias: 'A',
          },
        ],
        onChange: vi.fn(),
        onBlur: vi.fn(),
        name: 'faceDetections',
        ref: vi.fn(),
      };

    render(
      <Provider store={store}>
        <CognitionAutocomplete
          field={mockField}
          mode={Mode.FACE}
          disableLibraryCognitionForms
        />
      </Provider>
    );

    fireEvent.mouseEnter(screen.getByTestId(`cognition-autocomplete-input`));
    fireEvent.mouseOver(screen.getByTestId(`cognition-autocomplete-input`));

    expect(screen.getByTestId(`tooltip-mock`)).toBeInTheDocument();
    expect(screen.getByTestId(`cognition-autocomplete-input`)).toHaveClass(
      'Mui-disabled'
    );
    expect(
      screen.getByText(
        'Face and object search requires cognition libraries to exist in the organization.'
      )
    ).toBeInTheDocument();
  });
});
