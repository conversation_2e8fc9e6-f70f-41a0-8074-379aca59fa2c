import './index.scss';
import CreateEditCaseDrawer from '@components/CreateCase';
import Drawer from '@components/Drawer';
import EditMetadataDrawer from '@components/EditMetadata';
import Filter from '@components/FilterPanel';
import NavBar from '@components/NavBar';
import { Box, CircularProgress } from '@mui/material';
import { unstable_ClassNameGenerator } from '@mui/material/className';
import { selectOpenFilterDrawer } from '@store/modules/caseDetail/slice';
import { selectIsCaseDrawerOpen } from '@store/modules/caseManager/slice';
import { selectOpenMetadataDrawer } from '@store/modules/metadata/slice';
import { ReactElement, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Outlet } from 'react-router';

const DRAWER_WIDTH = 600;
const FILTER_DRAWER_WIDTH = 350;

unstable_ClassNameGenerator.configure((componentName) =>
  componentName.replace(/^Mui/, 'Sdk-Mui')
);

const mapLanguage = (language: string) =>
  ({
    en: 'en-US' as const,
    fr: 'fr-FR' as const,
    es: 'es-ES' as const,
  })[language] || 'en-US';

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const createEditCase = <CreateEditCaseDrawer />;
const editMetadata = <EditMetadataDrawer />;
const filterPanel = <Filter isCaseDetail />;

const Layout = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [currDrawer, setCurrDrawer] = useState<ReactElement>(createEditCase);

  const isCaseDrawerOpen = useSelector(selectIsCaseDrawerOpen);
  const isMetadataDrawerOpen = useSelector(selectOpenMetadataDrawer);
  const isOpenFilterDrawer = useSelector(selectOpenFilterDrawer);

  const openDrawer =
    isCaseDrawerOpen || isMetadataDrawerOpen || isOpenFilterDrawer;

  const drawerWidth =
    currDrawer === filterPanel ? FILTER_DRAWER_WIDTH : DRAWER_WIDTH;

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (window.aiware) {
        setIsInitialized(true);
        return;
      }
    }, 500);
    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    /* Handle aiWARE preferredLanguage */
    const aiWARE = async () => {
      while (
        !window.aiware ||
        !window.aiware?.store?.getState().auth?.user?.preferredLanguage
      ) {
        await delay(500);
      }

      const preferredLanguage =
        window.aiware.store.getState().auth?.user?.preferredLanguage;
      if (preferredLanguage) {
        localStorage.setItem('language', mapLanguage(preferredLanguage));
      }

      window.aiware.on(
        'languageChange',
        function (error: Error, data: unknown) {
          if (
            !error &&
            data &&
            typeof data === 'object' &&
            'preferredLanguage' in data &&
            typeof data.preferredLanguage === 'string'
          ) {
            const preferredLanguage = mapLanguage(data.preferredLanguage);
            localStorage.setItem('language', preferredLanguage);
          }
        }
      );
    };

    /* Run async function */
    aiWARE();
  }, []);

  useEffect(() => {
    if (isCaseDrawerOpen) {
      setCurrDrawer(createEditCase);
    } else if (isMetadataDrawerOpen) {
      setCurrDrawer(editMetadata);
    } else if (isOpenFilterDrawer) {
      setCurrDrawer(filterPanel);
    }
  }, [isCaseDrawerOpen, isMetadataDrawerOpen, isOpenFilterDrawer]);

  return isInitialized ? (
    <Box className="app-container">
      <NavBar />
      <Outlet />
      <Drawer
        onClose={() => {}}
        width={drawerWidth}
        anchor={'right'}
        persistent={false}
        open={openDrawer}
      >
        {/* Rename below */}
        {currDrawer}
      </Drawer>
    </Box>
  ) : (
    <CircularProgress className="loading-progress" size={75} />
  );
};

export default Layout;
