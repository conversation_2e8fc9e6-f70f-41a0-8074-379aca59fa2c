import './index.scss';
import {
  Document,
  Image,
  Search as SearchIcon,
  VideoCamera,
  Audio,
} from '@assets/icons';
import CognitionAutocomplete, {
  CognitionItem,
  Mode,
} from '@components/CognitionAutocomplete';
import { I18nTranslate } from '@i18n';
import { Close as CloseIcon } from '@mui/icons-material';
import {
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  IconButton,
  TextField,
} from '@mui/material';
import { FilterFormValue } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import { toggleOpenFilterDrawer } from '@store/modules/caseDetail/slice';
import { selectCaseData } from '@store/modules/caseManager/slice';
import {
  EvidenceType,
  FileType,
  resetSearchToDefault,
  searchFiles,
  queryUserLibraries,
  selectCategories,
  selectFilterParams,
  selectQueryUserLibraries,
  selectFormValues,
  selectSearchView,
  selectSort,
  setFormValues,
  updateSelectedResults,
} from '@store/modules/search/slice';
import { selectFetchedStatuses } from '@store/modules/settings/slice';
import { voidWrapper } from '@utils/helpers';
import { NO_STATUS_ID } from '@utils/helpers/validateStatusCondition';
import { Category, SearchView } from '@utils/local-storage';
import {
  cleanValidTokens,
  formFaceDetectionsToSearchParams,
  formObjectDetectionsToSearchParams,
} from '@utils/validationTokens';
import {
  Controller,
  ControllerRenderProps,
  FormProvider,
  useForm,
  useFormContext,
  UseFormSetError,
} from 'react-hook-form';
import { useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import CaseInformation from './CaseInfo';
import FilterAccordion from './FilterAccordion';
import cn from 'classnames';

const cognitions = [
  {
    id: 0,
    placeholder: 'filename',
    name: 'filename',
  },
  {
    id: 1,
    placeholder: 'wordsInTranscription',
    name: 'wordsInTranscription',
  },
  {
    id: 2,
    placeholder: 'faceDetections',
    name: 'faceDetections',
  },
  {
    id: 3,
    placeholder: 'objectDescriptors',
    name: 'objectDescriptors',
  },
  {
    id: 4,
    placeholder: 'vehicleRecognition',
    name: 'vehicleRecognition',
  },
  {
    id: 5,
    placeholder: 'licensePlateRecognition',
    name: 'licensePlateRecognition',
  },
  // {
  //   id: 6,
  //   placeholder: 'sceneClassification',
  //   name: 'sceneClassification',
  // },
  {
    id: 7,
    placeholder: 'textRecognition',
    name: 'textRecognition',
  },
];

const evidenceTypes = [
  {
    id: 1,
    type: 'callRecording',
    name: 'callRecording',
  },
  {
    id: 2,
    type: 'arrestReport',
    name: 'arrestReport',
  },
  {
    id: 3,
    type: 'bodyWornCamera',
    name: 'bodyWornCamera',
  },
  {
    id: 4,
    type: 'bookingPhoto',
    name: 'bookingPhoto',
  },
  {
    id: 5,
    type: 'citizenSubmittedVideo',
    name: 'citizenSubmittedVideo',
  },
  {
    id: 6,
    type: 'crimeScenePhoto',
    name: 'crimeScenePhoto',
  },
  {
    id: 7,
    type: 'inCarVideo',
    name: 'inCarVideo',
  },
  {
    id: 8,
    type: 'interviewAudioRecording',
    name: 'interviewAudioRecording',
  },
  {
    id: 9,
    type: 'interviewRoomRecording',
    name: 'interviewRoomRecording',
  },
  {
    id: 10,
    type: 'mobileDeviceExtraction',
    name: 'mobileDeviceExtraction',
  },
  {
    id: 11,
    type: 'securityCameraVideo',
    name: 'securityCameraVideo',
  },
];

const fileTypes = [
  {
    id: 1,
    icon: <VideoCamera />,
    type: 'video',
    name: 'video',
  },
  {
    id: 2,
    icon: <Audio />,
    type: 'audio',
    name: 'audio',
  },
  {
    id: 3,
    icon: <Document />,
    type: 'document',
    name: 'document',
  },
  {
    id: 4,
    icon: <Image />,
    type: 'image',
    name: 'image',
  },
];

interface FilterpanelProps {
  className?: string;
  isCaseDetail?: boolean;
  isValid?: boolean;
  disableLibraryCognitionForms: boolean;
  setFormError?: UseFormSetError<FilterFormValue>;
  searchFileAction?: (
    searchParams: FilterFormValue,
    curSearchView?: SearchView,
    abortSignal?: AbortSignal
  ) => ReturnType<typeof searchFiles>;
}

const FilterPanel = ({
  className,
  isCaseDetail,
  searchFileAction,
  setFormError,
  isValid,
  disableLibraryCognitionForms,
}: FilterpanelProps) => {
  const intl = I18nTranslate.Intl();

  const { getValues, control, reset, handleSubmit, formState } =
    useFormContext<FilterFormValue>();

  const dispatch = useAppDispatch();
  const searchView = useSelector(selectSearchView);

  const isKeyofFormValue = (key: string): key is keyof FilterFormValue =>
    key in getValues();

  const handleReset = () => {
    reset(filterFormDefaultValues);
    dispatch(resetSearchToDefault());
  };

  const onSubmit = (data: FilterFormValue) => {
    dispatch(
      setFormValues({
        ...filterFormDefaultValues,
        ...data,
        uploadDate: {
          startDate: data.uploadDate?.startDate ?? '',
          endDate: data.uploadDate?.endDate ?? '',
        },
        retentionDate: data.retentionDate ?? '',
        faceDetections: Array.isArray(data.faceDetections)
          ? data.faceDetections.filter(
              (item): item is CognitionItem =>
                !!item && typeof item.id === 'string'
            )
          : [],
        objectDescriptors: Array.isArray(data.objectDescriptors)
          ? data.objectDescriptors.filter(
              (item): item is CognitionItem =>
                !!item && typeof item.id === 'string'
            )
          : [],
        callRecording: !!data.callRecording,
        arrestReport: !!data.arrestReport,
        bodyWornCamera: !!data.bodyWornCamera,
        bookingPhoto: !!data.bookingPhoto,
        citizenSubmittedVideo: !!data.citizenSubmittedVideo,
        crimeScenePhoto: !!data.crimeScenePhoto,
        inCarVideo: !!data.inCarVideo,
        interviewAudioRecording: !!data.interviewAudioRecording,
        interviewRoomRecording: !!data.interviewRoomRecording,
        mobileDeviceExtraction: !!data.mobileDeviceExtraction,
        securityCameraVideo: !!data.securityCameraVideo,
        video: !!data.video,
        audio: !!data.audio,
        document: !!data.document,
        image: !!data.image,
      })
    );

    if (searchFileAction && isValid) {
      dispatch(searchFileAction(data, searchView));
      dispatch(updateSelectedResults([]));
    }
  };
  const submitDisabled = !formState.isDirty || !isValid;

  return (
    <div
      className={cn('filter', className && { [className]: true })}
      data-testid="search-filter"
    >
      {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
      <form onSubmit={handleSubmit(onSubmit)} className="filter-panel">
        <div className="filter__content">
          <div className="filter-search">
            <div className="filter-search-title">
              <span className="filter-search-title__keyword">
                {intl.formatMessage({ id: 'keyword' })}
              </span>
            </div>
            <div className="filter-search-searchbar">
              <Controller
                control={control}
                name="search"
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    slotProps={{
                      input: {
                        startAdornment: <SearchIcon />,
                        className: 'filter-search-searchbar__input',
                      },
                      htmlInput: { 'data-testid': 'keyword-search-input' },
                    }}
                    variant="outlined"
                    color="primary"
                  />
                )}
              />
            </div>
          </div>
          <div className="filter-search-warning">
            {intl.formatMessage({ id: 'filterSearchWarning' })}
          </div>

          <FilterAccordion
            title={intl.formatMessage({ id: 'caseInformation' })}
            defaultExpanded
          >
            <CaseInformation isCaseDetail={isCaseDetail} />
          </FilterAccordion>
          <Divider />
          <FilterAccordion title={intl.formatMessage({ id: 'advancedSearch' })}>
            <div className="filter-cognition" data-testid="filter-cognition">
              {cognitions.map(({ id, placeholder, name }) => {
                if (isKeyofFormValue(name)) {
                  return (
                    <Controller
                      key={id}
                      name={name}
                      control={control}
                      render={({ field }) => {
                        if (
                          name === 'faceDetections' ||
                          name === 'objectDescriptors'
                        ) {
                          return (
                            <CognitionAutocomplete
                              disableLibraryCognitionForms={
                                disableLibraryCognitionForms
                              }
                              field={
                                field as ControllerRenderProps<
                                  FilterFormValue,
                                  'faceDetections' | 'objectDescriptors'
                                >
                              }
                              mode={
                                name === 'faceDetections'
                                  ? Mode.FACE
                                  : Mode.OBJECT
                              }
                              setFormError={setFormError}
                            />
                          );
                        } else {
                          return (
                            <TextField
                              {...field}
                              fullWidth
                              placeholder={intl.formatMessage({
                                id: placeholder,
                              })}
                              margin="normal"
                              className="filter-cognition-item"
                            />
                          );
                        }
                      }}
                    />
                  );
                }
                return null;
              })}
            </div>
          </FilterAccordion>
          <Divider />
          <FilterAccordion title={intl.formatMessage({ id: 'evidenceType' })}>
            <div
              className="filter-evidence-type"
              data-testid="filter-evidence-type"
            >
              {evidenceTypes.map(({ id, type, name }) => {
                if (isKeyofFormValue(name)) {
                  return (
                    <Controller
                      key={id}
                      name={name}
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...field}
                              checked={!!field.value}
                              sx={{ mr: 2 }}
                              data-testid={`evidence-type-${name}`}
                            />
                          }
                          label={intl.formatMessage({ id: type })}
                        />
                      )}
                    />
                  );
                }
                return null;
              })}
            </div>
          </FilterAccordion>
          <Divider />
          <FilterAccordion title={intl.formatMessage({ id: 'fileType' })}>
            <div className="filter-file-type" data-testid="filter-file-type">
              {fileTypes.map(({ id, icon, type, name }) => {
                if (isKeyofFormValue(name)) {
                  return (
                    <Controller
                      key={id}
                      name={name}
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...field}
                              checked={!!field.value}
                              sx={{ mr: 2 }}
                              data-testid={`file-type-${name}`}
                            />
                          }
                          label={
                            <div className="form-control-label">
                              {icon}
                              <span>{intl.formatMessage({ id: type })}</span>
                            </div>
                          }
                        />
                      )}
                    />
                  );
                }
                return null;
              })}
            </div>
          </FilterAccordion>
        </div>
        {!isCaseDetail && (
          <div className="filter-panel__footer-actions">
            <Button
              className="filter-panel__reset-button"
              data-testid="filter-panel-reset-button"
              onClick={() => handleReset()}
            >
              {intl.formatMessage({ id: 'resetFilters' })}
            </Button>
            <Button
              type="submit"
              className="filter-panel__submit-button"
              data-testid="filter-panel-submit-button"
              disabled={submitDisabled}
            >
              {intl.formatMessage({ id: 'search' })}
            </Button>
          </div>
        )}
      </form>
    </div>
  );
};

interface FilterPanelWrapperProps {
  isValid?: boolean;
  disableLibraryCognitionForms: boolean;
  searchFileAction: (
    searchParams: FilterFormValue,
    curSearchView?: SearchView,
    abortSignal?: AbortSignal
  ) => ReturnType<typeof searchFiles>;
}

const FilterPanelWrapper = ({
  searchFileAction,
  disableLibraryCognitionForms,
}: FilterPanelWrapperProps) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const selectedCase = useSelector(selectCaseData);

  const {
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useFormContext<FilterFormValue>();

  const handleCloseFilterPanel = () => {
    dispatch(toggleOpenFilterDrawer());
  };

  useEffect(() => {
    reset(filterFormDefaultValues);
  }, []);

  const onSubmit = (data: FilterFormValue) => {
    // Remove the caseId and caseStatus from the search params,
    // and set the caseFolderId if it exists.  Faster to search by folderId
    if (selectedCase.folderId) {
      data.caseId = '';
      data.caseStatus = '';
      data.caseFolderId = selectedCase.folderId;
    }
    dispatch(searchFileAction(data));
    handleCloseFilterPanel();
  };

  return (
    <div className="filter-panel-wrapper">
      <div className="filter-panel-wrapper__container">
        <div className="filter-panel-wrapper__container-header">
          <p>{intl.formatMessage({ id: 'filters' })}</p>
          <IconButton onClick={handleCloseFilterPanel}>
            <CloseIcon />
          </IconButton>
        </div>
        <FilterPanel
          className="filter-panel-wrapper__container-filter"
          isCaseDetail
          disableLibraryCognitionForms={disableLibraryCognitionForms}
        />
        <div className="filter-panel-wrapper__footer">
          <Button onClick={handleCloseFilterPanel} color="inherit">
            {intl.formatMessage({ id: 'cancel' })}
          </Button>
          <Button
            type="submit"
            variant="contained"
            onClick={voidWrapper(handleSubmit(onSubmit))}
            disabled={!!Object.keys(errors).length || !isDirty}
            className="filter-panel-wrapper__footer-apply-button"
          >
            {intl.formatMessage({ id: 'apply' })}
          </Button>
        </div>
      </div>
    </div>
  );
};

interface FilterProps {
  isCaseDetail?: boolean;
}

export const filterFormDefaultValues: FilterFormValue = {
  search: '',
  metadata: '',
  // case information
  caseStatus: '',
  filterCaseTagIds: [],
  caseId: '',
  uploadDate: {
    startDate: '',
    endDate: '',
  },
  // retentionDate: renderDefaultDate(),
  fileStatus: '',
  // advanced search
  filename: '',
  wordsInTranscription: '',
  faceDetections: [],
  objectDescriptors: [],
  vehicleRecognition: '',
  licensePlateRecognition: '',
  // sceneClassification: '',
  textRecognition: '',
  // evidence type
  callRecording: false,
  arrestReport: false,
  bodyWornCamera: false,
  bookingPhoto: false,
  citizenSubmittedVideo: false,
  crimeScenePhoto: false,
  inCarVideo: false,
  interviewAudioRecording: false,
  interviewRoomRecording: false,
  mobileDeviceExtraction: false,
  securityCameraVideo: false,
  // file type
  video: false,
  audio: false,
  document: false,
  image: false,
};

const Filter = ({ isCaseDetail }: FilterProps) => {
  const dispatch = useAppDispatch();

  const [disableLibraryCognitionForms, setDisableLibraryCognitionForms] =
    useState(false);

  const selectedCase = useSelector(selectCaseData);
  const selectedCaseId = isCaseDetail ? selectedCase.caseId : '';
  const selectedCaseStatus = isCaseDetail ? selectedCase.statusId : '';
  const selectedCaseTags = isCaseDetail ? selectedCase.preconfiguredTagIds : [];
  const savedFormValues = useSelector(
    isCaseDetail ? selectFilterParams : selectFormValues
  );
  const queryUserLibrariesState = useSelector(selectQueryUserLibraries);
  const searchView = useSelector(selectSearchView);
  const existingStatuses = useSelector(selectFetchedStatuses);

  filterFormDefaultValues.caseId = selectedCaseId || '';
  filterFormDefaultValues.caseStatus = selectedCaseStatus || '';
  filterFormDefaultValues.filterCaseTagIds = selectedCaseTags || [];

  const methods = useForm<FilterFormValue>({
    mode: isCaseDetail ? 'onSubmit' : 'onChange',
    defaultValues: {
      ...filterFormDefaultValues,
      ...savedFormValues,
    },
  });

  const categories = useSelector(selectCategories);
  const fileSort = useSelector(selectSort);

  useEffect(() => {
    dispatch(queryUserLibraries());
  }, []);

  useEffect(() => {
    const { orgHasLibraries, status } = queryUserLibrariesState;

    if (status === 'complete') {
      setDisableLibraryCognitionForms(!orgHasLibraries);
    }
  }, [queryUserLibrariesState]);

  const searchFileAction = (
    searchParams: FilterFormValue,
    curSearchView?: SearchView
  ) => {
    const validFaceDetection = cleanValidTokens(
      searchParams.faceDetections || []
    );

    const faceDetectionsTerms =
      formFaceDetectionsToSearchParams(validFaceDetection);

    const validObjectDescriptors = cleanValidTokens(
      searchParams.objectDescriptors || []
    );

    const objectDescriptorsTerms = formObjectDetectionsToSearchParams(
      validObjectDescriptors
    );

    const isNoStatusFilter = searchParams.caseStatus === NO_STATUS_ID;

    return searchFiles({
      params: {
        keywordSearchQuery: searchParams.search?.trim(),
        searchResultType:
          curSearchView && curSearchView === SearchView.Grouped
            ? 'grouped'
            : 'ungrouped',
        checkedResultCategories: categories.map((category: Category) => {
          switch (category.id) {
            case 'filename':
              return 'filename';
            case 'transcription':
              return 'transcription';
            case 'license-plate-recognition':
              return 'licensePlateRecognition';
            case 'metadata':
              return 'metadata';
            case 'face-recognition':
              return 'faceRecognition';
            case 'object-detection':
              return 'objectDetection';
            case 'vehicle-recognition':
              return 'vehicleRecognition';
            // case 'scene-classification':
            //   return 'sceneClassification';
            case 'text-recognition':
            default:
              return 'textRecognition';
          }
        }),
        // checkedResultCategories: ['transcription', 'faceRecognition', 'objectDetection', 'sceneClassification', 'textRecognition'],
        sort: { type: fileSort.type, order: fileSort.order },
        uploadDateFilter: {
          startDate: searchParams.uploadDate.startDate,
          endDate: searchParams.uploadDate.endDate,
        },
        caseIdFilter: [searchParams.caseId?.trim()],
        caseStatusesFilter: {
          field: 'statusId',
          operator: 'terms',
          not: isNoStatusFilter,
          values: isNoStatusFilter
            ? existingStatuses.map((status) => status.id)
            : [searchParams.caseStatus?.trim()],
        },
        caseTagsFilter: {
          field: 'preconfiguredTagIds',
          operator: 'terms',
          values: searchParams.filterCaseTagIds?.map((tag) => tag.trim()),
        },
        cognitionFilters: {
          filename: searchParams.filename,
          wordsInTranscript: searchParams.wordsInTranscription,
          faceDetection: {
            terms: faceDetectionsTerms || [],
          },
          objectDetection: {
            terms: objectDescriptorsTerms || [],
          },
          vehicleDetection: searchParams.vehicleRecognition?.trim(),
          licensePlateDetection: searchParams.licensePlateRecognition?.trim(),
          // sceneDetection: searchParams.sceneClassification?.trim(),
          textRecognition: searchParams.textRecognition?.trim(),
          metadata: searchParams.metadata?.trim(),
        },
        evidenceTypeFilter: [
          searchParams.callRecording ? '911 Call Recording' : '',
          searchParams.arrestReport ? 'Arrest Reporting' : '',
          searchParams.bodyWornCamera ? 'Body Worn Camera' : '',
          searchParams.bookingPhoto ? 'Booking Photo' : '',
          searchParams.citizenSubmittedVideo ? 'Citizen Submitted Video' : '',
          searchParams.crimeScenePhoto ? 'Crime Scene Photo' : '',
          searchParams.inCarVideo ? 'In Car Video' : '',
          searchParams.interviewAudioRecording
            ? 'Interview Audio Recording'
            : '',
        ].filter((e): e is EvidenceType => !!e),
        fileTypeFilter: [
          searchParams.video ? 'Video' : '',
          searchParams.audio ? 'Audio' : '',
          searchParams.document ? 'Document' : '',
          searchParams.image ? 'Image' : '',
        ].filter((e): e is FileType => !!e),
        folderIdsFilter: searchParams.caseFolderId
          ? [searchParams.caseFolderId]
          : [],
        pagination: {
          ungrouped: { offset: 0, limit: 50 },
          filename: {
            offset: 0,
            limit: 10,
          },
          transcription: {
            offset: 0,
            limit: 10,
          },
          faceRecognition: {
            offset: 0,
            limit: 10,
          },
          objectDetection: {
            offset: 0,
            limit: 10,
          },
          vehicleRecognition: {
            offset: 0,
            limit: 10,
          },
          licensePlateRecognition: {
            offset: 0,
            limit: 10,
          },
          // sceneClassification: {
          //   offset: 0,
          //   limit: 10,
          // },
          textRecognition: {
            offset: 0,
            limit: 10,
          },
          metadata: {
            offset: 0,
            limit: 10,
          },
        },
      },
      isPolling: false,
    });
  };

  useEffect(() => {
    if (methods.formState.isValid && methods.formState.isDirty) {
      dispatch(searchFileAction(methods.getValues(), searchView));
    }
  }, [searchView, fileSort.type, fileSort.order]);

  return (
    <FormProvider {...methods}>
      {isCaseDetail ? (
        <FilterPanelWrapper
          disableLibraryCognitionForms={disableLibraryCognitionForms}
          searchFileAction={searchFileAction}
          isValid={methods.formState.isValid}
        />
      ) : (
        <FilterPanel
          disableLibraryCognitionForms={disableLibraryCognitionForms}
          setFormError={methods.setError}
          searchFileAction={searchFileAction}
          isValid={methods.formState.isValid}
        />
      )}
    </FormProvider>
  );
};

export default Filter;
