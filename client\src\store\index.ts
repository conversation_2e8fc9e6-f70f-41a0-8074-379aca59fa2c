import { combineSlices, configureStore } from '@reduxjs/toolkit';
import HttpClient from '@store/dependencies/httpClient';
import { appSlice } from '@store/modules/app/slice';
import {
  caseDetailSlice,
  CaseDetailSliceState,
} from '@store/modules/caseDetail/slice';
import {
  caseManagerSlice,
  CaseManagerSliceState,
} from '@store/modules/caseManager/slice';
import { configSlice, ConfigSliceState } from '@store/modules/config/slice';
import {
  metadataSlice,
  MetadataSliceState,
} from '@store/modules/metadata/slice';
import { searchSlice, SearchSliceState } from '@store/modules/search/slice';
import {
  settingsSlice,
  SettingsSliceState,
} from '@store/modules/settings/slice';
import rootSaga from '@store/rootSaga';
import { AuthState, modules, UserState } from '@veritone/glc-redux';
import { apiMiddleware } from 'redux-api-middleware';
import { createLogger as createLoggerMiddleware } from 'redux-logger';
import createSagaMiddleware, { Middleware } from 'redux-saga';

const { auth, user, config } = modules;
const { namespace: userNamespace, reducer: userReducer } = user;
// TODO: Fix the types of the auth module exported by glc-redux
// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
const { namespace: authNamespace, reducer: authReducer } = auth;

const rootReducer = combineSlices(
  appSlice,
  configSlice,
  caseManagerSlice,
  caseDetailSlice,
  settingsSlice,
  searchSlice,
  metadataSlice,
  {
    [userNamespace]: userReducer,
    // TODO: Fix the types of the auth module exported by glc-redux
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    [authNamespace]: authReducer,
    // TODO: type state properly
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    [config.namespace]: (state = window.config ?? {}) => state,
  }
);

const shouldHaveDevTools = () =>
  location.href.includes('stage') || location.href.includes('local.veritone');

export const configureAppStore = (preloadedState = {}) => {
  const reduxSagaMonitorOptions = {};
  const sagaMiddleware = createSagaMiddleware(reduxSagaMonitorOptions);

  const middleware: Middleware[] = [
    apiMiddleware as Middleware,
    sagaMiddleware,
  ];

  if (process.env.NODE_ENV !== 'test') {
    middleware.push(createLoggerMiddleware({}) as Middleware);
  }

  const store = configureStore({
    reducer: rootReducer,
    middleware: (gDM) =>
      gDM({
        serializableCheck: false,
        thunk: {
          extraArgument: {
            http: new HttpClient(window.config),
          },
        },
      }).concat([...middleware]),
    preloadedState,
    devTools: shouldHaveDevTools(),
  });

  sagaMiddleware.run(rootSaga);

  // expose the Redux store to Cypress tests
  if (window.Cypress) {
    window.store = store;
  }
  return store;
};

export const store = configureAppStore();

export interface RootState {
  caseManager: CaseManagerSliceState;
  caseDetail: CaseDetailSliceState;
  settings: SettingsSliceState;
  appConfig: ConfigSliceState;
  metadata: MetadataSliceState;
  search: SearchSliceState;
  config: Window['config'];
  user: UserState;
  auth: AuthState & { readonly userId: string };
}

export default store;
