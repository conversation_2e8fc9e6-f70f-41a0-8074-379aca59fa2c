.search-page__accordion {
  .search-page__accordion-title {
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    & > .Sdk-MuiTypography-root {
      font-weight: bold !important;
      font-size: 12px;
    }
  }

  .search-page__accordion-details {
    padding: 10px 15px;
  }
}

// reset mui accordion styles
.Sdk-MuiAccordion-root {
  background-color: transparent;
  background-image: none;
  box-shadow: none;
  border: none;
  padding: 0;
  margin: 0;

  &::before {
    display: none;
  }

  .Sdk-MuiAccordion-heading {
    .Sdk-MuiAccordionSummary-root {
      min-height: 40px;
      padding: 0;

      .Sdk-MuiAccordionSummary-content {
        margin: 0;
      }
    }
  }

  .Sdk-MuiAccordionDetails-root {
    padding: 0;
  }
}

.Sdk-MuiAccordion-root.Mui-expanded {
  margin-top: 0;
}
