import {
  CaseSDO,
  InvestigateCase,
  TemporalDataObject,
} from '@shared-types/types';
import { configureAppStore } from '@store/index';
import {
  initialState as appConfigInitialState,
  ConfigSliceState,
} from '@store/modules/config/slice';
import { waitFor } from '@testing-library/dom';
import { GQLApi, SortBy } from '@utils/helpers';
import { baseGraphQLApiThrowError } from '@utils/helpers/gqlApi/baseGraphQLApi';
import { DateTime, Settings } from 'luxon';
import { afterEach } from 'node:test';
import { enqueueSnackbar } from 'notistack';
import { describe, expect, it, Mock, vi } from 'vitest';
import {
  mockCaseSearchResult,
  searchQueryNameSortedWithTwoStatuses,
  searchQueryTagSortedCaseDateAscPage2,
  softDeleteFileQuery,
} from './fixtures';
import {
  initialState as CaseManagerInitialState,
  CaseManagerSliceState,
  createNewCase,
  deleteCase,
  deleteFile,
  fileUploadedSuccessfully,
  searchCases,
  setCaseFilter,
  setLimit,
  setOffset,
  setSort,
} from './slice';

vi.mock('../../../utils/helpers/gqlApi/baseGraphQLApi', () => ({
  baseGraphQLApiThrowError: vi.fn(),
  baseGraphQLApi: vi.fn(),
  enqueueSnackbar: vi.fn(),
}));

const initialStateForMock: {
  caseManager: CaseManagerSliceState;
  appConfig: ConfigSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
  user: { user: { userId: string } };
} = {
  caseManager: {
    ...CaseManagerInitialState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'rootFolder123',
    },
    folderContentTemplateSchema: {
      status: 'idle',
      error: '',
      id: 'folderContentTemplateSchemaId123',
    },
  },
  appConfig: {
    ...appConfigInitialState,
    statusSchema: {
      status: 'idle',
      error: '',
      id: 'statusSchemaId123',
    },
    tagSchema: {
      status: 'idle',
      error: '',
      id: 'tagSchemaId123',
    },
    evidenceTypeSchema: {
      status: 'idle',
      error: undefined,
      id: '',
    },
    registryIds: {
      caseRegistryId: '',
      evidenceTypeRegistryId: '',
      statusRegistryId: '',
      tagRegistryId: '',
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
    },
    nodeEnv: 'dev',
  },
  auth: { sessionToken: 'sessionToken123' },
  user: { user: { userId: 'userId123' } },
};

const mockTdoDetails = {
  veritoneProgram: {
    programLiveImage: '',
  },
  veritoneFile: {
    fileName: 'Walking Tour 4-i-build10.mp4',
    fileType: 'video/mp4',
    createdByName: 'firstname lastname',
  },
  tags: [
    {
      value: 'veritone_track',
    },
  ],
  veritonePermissions: {
    acls: [
      {
        groupId: 'f534544-5368-4c6a-b0d6-095435b07a',
        permission: 'owner',
      },
    ],
    isPublic: false,
  },
  addToIndex: true,
  name: 'Walking Tour 4-i-build10.mp4',
} as TemporalDataObject['details'];

describe('caseManagerSlice', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should create new case', async () => {
    vi.spyOn(GQLApi.prototype, 'createSDO').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'sdoId123',
        data: undefined,
      })
    );
    vi.spyOn(GQLApi.prototype, 'createFolder').mockImplementationOnce(() =>
      Promise.resolve('folderId123')
    );
    vi.spyOn(
      GQLApi.prototype,
      'createFolderContentTemplate'
    ).mockImplementationOnce(() =>
      Promise.resolve('folderContentTemplateId123')
    );

    const mockedStore = configureAppStore(initialStateForMock);

    const testCase: InvestigateCase = {
      caseName: 'case name 1',
      caseId: '123',
      description: 'case description 123',
      caseDate: '2025-01-31T0:0:0.000Z',
      preconfiguredTagIds: ['South Los Angeles'],
      statusId: 'active',
      createdBy: 'userId123',
    };

    // dispatch the createNewCase thunk
    await mockedStore.dispatch(createNewCase(testCase));

    // get the updated state
    const newState = mockedStore.getState();

    expect(newState.caseManager.createCase.status).toBe('complete');
  });

  it('should delete case', async () => {
    const caseSdo: CaseSDO = {
      caseName: 'case name 1',
      caseId: '123',
      description: 'case description 123',
      caseDate: '2025-02-18T0:0:0.000Z',
      preconfiguredTagIds: ['South Los Angeles'],
      statusId: 'active',
      folderId: 'folder 1',
      createdBy: 'userId123',
      createdDateTime: '2025-02-18T17:20:51.123Z',
      modifiedBy: 'userId123',
      modifiedDateTime: '2025-02-18T17:20:51.123Z',
    };

    vi.spyOn(GQLApi.prototype, 'getSDO').mockImplementationOnce(() =>
      Promise.resolve({
        schemaId: 'sdoSchemaId123',
        id: 'sdoId123',
        data: caseSdo,
      })
    );
    const updateSDOMock = vi
      .fn()
      .mockImplementationOnce(() =>
        Promise.resolve({ id: 'sdoId123', data: {} })
      );

    vi.spyOn(GQLApi.prototype, 'updateSDO').mockImplementationOnce(
      updateSDOMock
    );

    const mockedStore = configureAppStore(initialStateForMock);

    // dispatch the deleteCase thunk
    await mockedStore.dispatch(deleteCase('sdoId123'));

    // get the updated state
    const newState = mockedStore.getState();

    expect(updateSDOMock.mock.calls[0][0].id).toBe('sdoId123');
    expect(updateSDOMock.mock.calls[0][0].data.toBeDeletedTime).toBeTruthy();
    expect(newState.caseManager.deleteCase.status).toBe('complete');
  });

  it('should search cases by id and sort by id with status filter', async () => {
    vi.spyOn(GQLApi.prototype, 'getRootFolder').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'rootFolderId123',
        name: 'cms root folder',
        ownerId: '',
      })
    );

    vi.mocked(baseGraphQLApiThrowError).mockImplementationOnce(() =>
      Promise.resolve(mockCaseSearchResult)
    );

    const searchCasesSpy = vi.spyOn(GQLApi.prototype, 'searchCases');

    const mockedStore = configureAppStore(initialStateForMock);

    // Change sort and limit
    mockedStore.dispatch(
      setSort({ sortBy: SortBy.CaseId, sortDirection: 'desc' })
    );
    mockedStore.dispatch(setLimit(10));

    // Change search filter
    mockedStore.dispatch(
      setCaseFilter({
        caseId: 'event name search text',
        statusId: 'statusId1',
      })
    );

    // dispatch the createNewCase thunk
    await mockedStore.dispatch(searchCases({}));

    // get the updated state
    const newState = mockedStore.getState();

    await waitFor(() => {
      expect(searchCasesSpy).toHaveBeenCalledOnce();
    });
    searchCasesSpy.mockClear();

    expect(baseGraphQLApiThrowError).toHaveBeenCalledWith(
      searchQueryNameSortedWithTwoStatuses
    );

    expect(newState.caseManager.cases.data.results).toStrictEqual(
      mockCaseSearchResult.searchMedia.jsondata.results
    );
  });

  it('should soft delete files', async () => {
    const deleteFileSpy = vi.spyOn(GQLApi.prototype, 'softDeleteFile');

    Settings.throwOnInvalid = true;

    vi.spyOn(DateTime, 'now').mockImplementationOnce(
      () => DateTime.fromISO('2025-02-18T17:32:40Z') as DateTime<true> // 'as' here is necessary because 'fromISO' may return a false 'IsValid' type, but now() always returns a true 'IsValid' type. Because we know the input during the test, the cast is safe.
    );

    vi.spyOn(GQLApi.prototype, 'getTdoDetails').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'tdoId123',
        name: 'Walking Tour 4-i-build10.mp4',
        description: 'desc',
        contentType: 'video/mp4',
        folders: [],
        details: mockTdoDetails,
      })
    );

    const mockedStore = configureAppStore(initialStateForMock);

    // dispatch the createNewCase thunk
    await mockedStore.dispatch(deleteFile({ tdoId: '1234567890' }));

    // get the updated state
    const newState = mockedStore.getState();

    await waitFor(() => {
      expect(deleteFileSpy).toHaveBeenCalledOnce();
    });
    deleteFileSpy.mockClear();

    expect(baseGraphQLApiThrowError).toHaveBeenCalledWith(softDeleteFileQuery);

    expect(newState.caseManager.deleteFile.status).toBe('complete');
  });

  it('should search cases by tag and sort by caseDate on the second page', async () => {
    vi.spyOn(GQLApi.prototype, 'getRootFolder').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'rootFolderId123',
        name: 'cms root folder',
        ownerId: '',
      })
    );

    vi.mocked(baseGraphQLApiThrowError).mockImplementationOnce(() =>
      Promise.resolve(mockCaseSearchResult)
    );

    const searchCasesSpy = vi.spyOn(GQLApi.prototype, 'searchCases');

    const mockedStore = configureAppStore(initialStateForMock);

    // Change sort and page, and limit
    mockedStore.dispatch(
      setSort({ sortBy: SortBy.CaseDate, sortDirection: 'asc' })
    );
    mockedStore.dispatch(setOffset(10));
    mockedStore.dispatch(setLimit(10));
    // Change search filter
    mockedStore.dispatch(
      setCaseFilter({
        tagIds: ['tagId123'],
      })
    );

    // dispatch the createNewCase thunk
    await mockedStore.dispatch(searchCases({}));

    // get the updated state
    const newState = mockedStore.getState();

    await waitFor(() => {
      expect(searchCasesSpy).toHaveBeenCalledOnce();
    });

    expect(baseGraphQLApiThrowError).toHaveBeenCalledWith(
      searchQueryTagSortedCaseDateAscPage2
    );

    expect(newState.caseManager.cases.data.results).toStrictEqual(
      mockCaseSearchResult.searchMedia.jsondata.results
    );
  });

  it('should delete case and show success snackbar', async () => {
    vi.spyOn(GQLApi.prototype, 'getSDO').mockImplementationOnce(() =>
      Promise.resolve({ schemaId: 'sdoSchemaId123', id: 'sdoId123', data: {} })
    );
    vi.spyOn(GQLApi.prototype, 'updateSDO').mockImplementationOnce(() =>
      Promise.resolve({ id: 'sdoId123', data: {} })
    );

    const mockedStore = configureAppStore(initialStateForMock);

    await mockedStore.dispatch(deleteCase('sdoId123'));

    const newState = mockedStore.getState();
    expect(newState.caseManager.deleteCase.status).toBe('complete');

    const mockedEnqueueSnackbar = enqueueSnackbar as unknown as Mock;

    expect(mockedEnqueueSnackbar).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({ variant: 'success' })
    );

    const [formattedMessage] = mockedEnqueueSnackbar.mock.calls[0];
    expect(formattedMessage.props.id).toBe('caseDeletedSuccessfully');
  });

  it('should fail to delete case and show error snackbar', async () => {
    vi.spyOn(GQLApi.prototype, 'getSDO').mockImplementationOnce(() =>
      Promise.reject(new Error('Failed to delete case'))
    );

    const mockedStore = configureAppStore(initialStateForMock);

    await mockedStore.dispatch(deleteCase('sdoId123'));

    const newState = mockedStore.getState();
    expect(newState.caseManager.deleteCase.status).toBe('failure');

    const mockedEnqueueSnackbar = enqueueSnackbar as unknown as Mock;

    expect(mockedEnqueueSnackbar).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({ variant: 'error' })
    );

    const [formattedMessage] = mockedEnqueueSnackbar.mock.calls[0];
    expect(formattedMessage.props.id).toBe('caseDeletionFailed');
  });

  it('should delete file and show success snackbar', async () => {
    vi.spyOn(GQLApi.prototype, 'getTdoDetails').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'mock-id',
        name: 'mock-name',
        description: 'mock-description',
        contentType: 'image/png',
        folders: [],
        details: {
          tags: [],
          veritoneFile: {
            fileType: 'image',
          },
        },
      })
    );

    const mockedStore = configureAppStore(initialStateForMock);

    await mockedStore.dispatch(deleteFile({ tdoId: '1234567890' }));

    const newState = mockedStore.getState();
    expect(newState.caseManager?.deleteFile.status).toBe('complete');

    const mockedEnqueueSnackbar = enqueueSnackbar as unknown as Mock;

    expect(mockedEnqueueSnackbar).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({ variant: 'success' })
    );

    const [formattedMessage] = mockedEnqueueSnackbar.mock.calls[0];
    expect(formattedMessage.props.id).toBe('fileDeletedSuccessfully');
  });

  it('displays file name on upload w/ successful file name fetch', async () => {
    vi.spyOn(GQLApi.prototype, 'fetchFileName').mockImplementationOnce(() =>
      Promise.resolve('File Name')
    );

    const mockedStore = configureAppStore(initialStateForMock);

    await mockedStore.dispatch(
      fileUploadedSuccessfully({ tdoId: '1234567890' })
    );
    const mockedEnqueueSnackbar = enqueueSnackbar as unknown as Mock;

    const [formattedMessage] = mockedEnqueueSnackbar.mock.calls[0];
    expect(formattedMessage.props.id).toBe('uploadFileSuccess');
    expect(formattedMessage.props.values.name).toBe('File Name');
  });

  it('displays file id on upload w/ failed file name fetch', async () => {
    vi.spyOn(GQLApi.prototype, 'fetchFileName').mockImplementationOnce(() =>
      Promise.reject(new Error('Failed to fetch file name'))
    );

    const mockedStore = configureAppStore(initialStateForMock);

    await mockedStore.dispatch(
      fileUploadedSuccessfully({ tdoId: '1234567890' })
    );
    const mockedEnqueueSnackbar = enqueueSnackbar as unknown as Mock;

    const [formattedMessage] = mockedEnqueueSnackbar.mock.calls[0];
    expect(formattedMessage.props.id).toBe('uploadFileSuccess');
    expect(formattedMessage.props.values.name).toBe('1234567890');
  });
});
