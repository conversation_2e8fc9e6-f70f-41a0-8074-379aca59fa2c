.empty-state__container {
    top: 50%;
    left: 50%;
    width: 100%;
    display: flex;
    position: absolute;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    transform: translate(-50%, -50%);

    & > p {
        margin: 0;
    }
}

.empty-table-and-tile_grouped {
    border: 1px solid var(--border-color);
    overflow: hidden;
    border-top: 0;
    overflow-y: auto;
    border-radius: 0 0 8px 8px;
}

.empty-table-and-tile-ungrouped {
    border: none;
    border-collapse: collapse;
}
