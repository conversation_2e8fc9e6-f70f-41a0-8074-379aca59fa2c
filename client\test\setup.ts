import '@testing-library/jest-dom/vitest';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeEach, vi } from 'vitest';

beforeEach(() => {
  vi.clearAllMocks();

  global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
});

afterEach(cleanup);

vi.mock('notistack', () => ({
  enqueueSnackbar: vi.fn(),
}));

window.aiware = {
  init: vi.fn(),
  mountPanel: vi.fn(),
  unmountPanel: vi.fn(),
  mountWidget: vi.fn(),
  unmountWidget: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  store: {
    getState: vi.fn(),
  },
};
