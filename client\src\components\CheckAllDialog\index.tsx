import './index.scss';
import Menu from '@components/Menu';
import { I18nTranslate } from '@i18n';
import { MenuItem } from '@mui/material';
import { ResultCategory } from '@store/modules/search/slice';

const CheckAllDialog = ({
  menuAnchorEl,
  onClose,
  onCheckAll,
  category,
}: Props) => {
  const intl = I18nTranslate.Intl();

  const handleCheckAll = (isChecked: boolean) => {
    onCheckAll(isChecked, category);
    onClose();
  };

  return (
    <Menu
      anchorEl={menuAnchorEl}
      open={Boolean(menuAnchorEl)}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      className="check-all-dialog"
    >
      <MenuItem onClick={() => handleCheckAll(true)}>
        {intl.formatMessage({ id: 'all' })}
      </MenuItem>
      <MenuItem onClick={() => handleCheckAll(false)}>
        {intl.formatMessage({ id: 'none' })}
      </MenuItem>
    </Menu>
  );
};

interface Props {
  menuAnchorEl: null | HTMLElement;
  onClose: () => void;
  onCheckAll: (isChecked: boolean, categoryId?: ResultCategory) => void;
  category?: ResultCategory;
}

export default CheckAllDialog;
