import { configureAppStore } from '@store/index';
import {
  initialState as caseManagerInitialState,
  CaseManagerSliceState,
} from '@store/modules/caseManager/slice';
import {
  initialState as settingsInitialState,
  SettingsSliceState,
  StatusTagRow,
  Visibility,
} from '@store/modules/settings/slice';
import { act, fireEvent, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GQLApi } from '@utils/helpers';
import { Provider } from 'react-redux';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import DeleteDialog from '.';
import { OptionsType } from '..';
import { render } from '../../../../test/render';

const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: settingsInitialState,
  caseManager: {
    ...caseManagerInitialState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'rootFolder123',
    },
    folderContentTemplateSchema: {
      status: 'idle',
      error: '',
      id: 'folderContentTemplateSchemaId123',
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

const mockSearchCasesByStatus: SettingsSliceState['searchCasesByStatus'] = {
  status: 'idle',
  data: {
    from: 0,
    limit: 0,
    results: [
      {
        caseId: 'case-001',
        statusId: 'open',
        caseDate: '2025-02-19T10:00:00.000Z',
        caseName: 'Test Case A',
        folderId: 'folder-001',
        createdBy: 'user-123',
        description: 'This is a test case A',
        createdDateTime: '2025-02-19T10:05:00.000Z',
        modifiedDateTime: '2025-02-19T10:10:00.000Z',
        preconfiguredTagIds: ['tag1', 'tag2'],
        id: 'uuid-001',
      },
      {
        caseId: 'case-002',
        statusId: 'closed',
        caseDate: '2025-02-18T14:30:00.000Z',
        caseName: 'Test Case B',
        folderId: 'folder-002',
        createdBy: 'user-456',
        description: 'This is a test case B',
        createdDateTime: '2025-02-18T14:35:00.000Z',
        modifiedDateTime: '2025-02-18T15:00:00.000Z',
        preconfiguredTagIds: ['tag3', 'tag4'],
        id: 'uuid-002',
      },
    ],
    to: 0,
    totalResults: 2,
  },
};

const mockStatusTagRows: StatusTagRow[] = [
  {
    id: '001',
    name: 'Pending',
    visibility: Visibility.Active,
  },
  {
    id: '002',
    name: 'Approved',
    visibility: Visibility.Active,
  },
  {
    id: '003',
    name: 'Rejected',
    visibility: Visibility.Inactive,
  },
];

describe('Settings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("shouldn't show radio button", () => {
    const store = configureAppStore(initialStateForMock);
    render(
      <Provider store={store}>
        <DeleteDialog
          open
          selectedTab={OptionsType.status}
          rows={[]}
          onClose={vi.fn()}
          handleDeleteRows={vi.fn()}
        />
      </Provider>
    );

    expect(
      screen.getByText(
        /are you sure you want to delete this statuslabel\? this action cannot be undone\./i
      )
    ).toBeInTheDocument();

    expect(screen.queryByRole('radiogroup')).not.toBeInTheDocument();

    expect(
      screen.getByRole('button', {
        name: /Delete/i,
      })
    ).toBeInTheDocument();
  });

  it('should show radio button', () => {
    const initialState = {
      ...initialStateForMock,
      settings: {
        ...initialStateForMock.settings,
        searchCasesByStatus: mockSearchCasesByStatus,
        rowSelection: { '001': true },
      },
    };
    const store = configureAppStore(initialState);

    vi.spyOn(GQLApi.prototype, 'getSDOSchemaId').mockImplementationOnce(() =>
      Promise.resolve('sdoSchemaId123')
    );
    vi.spyOn(GQLApi.prototype, 'createSDOs').mockImplementationOnce(() =>
      Promise.resolve({ data: [{ id: 'uuid-001' }] })
    );

    const handleDeleteRows = vi.fn();
    const singleRow = mockStatusTagRows[0];

    render(
      <Provider store={store}>
        <DeleteDialog
          open
          singleRow={singleRow}
          rows={mockStatusTagRows}
          onClose={vi.fn()}
          handleDeleteRows={handleDeleteRows}
          selectedTab={OptionsType.status}
        />
      </Provider>
    );

    expect(
      screen.getByText(
        /there are cases using this label, choose a newstatus to assign them too/i
      )
    ).toBeInTheDocument();

    expect(screen.getByRole('radiogroup')).toBeInTheDocument();

    expect(
      screen.getByRole('button', {
        name: /clear all/i,
      })
    ).toBeInTheDocument();

    act(() => {
      userEvent.click(
        screen.getByRole('radio', {
          name: /re\-assign & delete/i,
        })
      );
    });

    expect(screen.getByRole('combobox')).toBeInTheDocument(); // select
    expect(screen.getByText(/Approved/i)).toBeInTheDocument(); // the first item in the rest of the statuses list

    act(() => {
      fireEvent.click(
        screen.getByRole('button', {
          name: /re\-assign & delete/i,
        })
      );
    });
    waitFor(() => {
      expect(handleDeleteRows).toBeCalledWith([singleRow.id]);
    });
  });
});
