import { CaseTag } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function fetchTagsList({
  schemaId,
  gql,
}: {
  schemaId: string;
  gql: GQLApi;
}) {
  const latestTagsSdo = await gql.getLatestSDO(schemaId);

  if (
    typeof latestTagsSdo.data === 'object' &&
    latestTagsSdo.data !== null &&
    'tags' in latestTagsSdo.data
  ) {
    return {
      tags: latestTagsSdo.data.tags as CaseTag[],
      sdoId: latestTagsSdo.id,
    };
  } else {
    return { tags: [], sdoId: '' };
  }
}
