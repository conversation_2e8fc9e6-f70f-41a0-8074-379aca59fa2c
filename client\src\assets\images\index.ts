// TODO: Is there a better way to do duplicate imports? Not sure if we're actually using these?
/* eslint-disable import/no-duplicates */
import helpIcon from './help.svg';
import notificationIcon from './notification.svg';
import hamburgerIcon from './hamburger.svg';
import veritoneLogo from './app_logo.svg';
import exampleImg from './example_img.svg';
import iconAIWare from './aiw-admin-icon.svg';
import iconAIWareLarge from './aiware-logo-large.svg';
import iconOrg from './organizations.svg';
import iconApp from './app-admin-icon.svg';
import iconEngine from './engine-admin-icon.svg';
import setting from './setting.svg';
import emailIcon from './mail-icon.svg';
import notificationCenterIcon from './notification-center-icon.svg';
import notificationBannerIcon from './notification-banner-icon.svg';
import unCheckIcon from './un-check-icon.svg';
import checkedIcon from './checked-icon.svg';
import finder from './finder.svg';
import importerTool from './importerTool.svg';
import engineToolkit from './engineToolkit.svg';
import illuminate from './illuminate.svg';
import automate from './automate.svg';
import redact from './redact.svg';
import reprocess from './reprocess.svg';
import view from './view.svg';
import share from './share.svg';
import veritoneLogoActive from './app_logo.svg';
import notificationActive from './notification_active.svg';
import helpActive from './help_active.svg';
import cameraIcon from './camera_icon.svg';
import noStatusLabelsTagsFound from './no-status-labels-tags-found.svg';
import notification from './notification.svg';
import help from './help.svg';
import exampleImage from './example_img.svg';
import huawei from './huawei.svg';
import orgIcon from './organization.svg';
import appIcon from './app-admin-icon.svg';
import editIcon from './icon_edit.svg';
import resetIcon from './icon_reset.svg';
import filterIcon from './filter.svg';
import settingIcon from './settings.svg';
import searchIcon from './search.svg';
import applicationTabIcon from './application.svg';
import personalProfileTabIcon from './icon_profile.svg';
import organizationTabIcon from './organizations.svg';
import preferenceTabIcon from './preferences.svg';
import activityTabIcon from './activity.svg';
import monitorTabIcon from './performance_monitor.svg';
import cmsAppIcon from './cms.svg';
import discoveryAppIcon from './discovery.svg';
import collectionAppIcon from './collection.svg';
import libraryAppIcon from './library.svg';
import adminAppIcon from './aiw-admin-icon.svg';
import developAppIcon from './developer.svg';
import collapseIcon from './to_close.svg';
import closeIcon from './close.svg';
import openInNewIcon from './open_in_new.svg';
import openInNewIconBlue from './open_in_new_blue.svg';
import openFlow from './open-flow.svg';
import appleCompanyIcon from './company_apple.svg';
import ciscoCompanyIcon from './company_cisco.svg';
import settingProfileIcon from './setting_profile.svg';
import settingUserIcon from './setting_user.svg';
import settingOrgIcon from './organizations.svg';
import settingAppIcon from './icon_app.svg';
import settingEngineIcon from './engine-admin-icon.svg';
import settingEdgeIcon from './icon_edge.svg';
import settingConfigurationIcon from './icon_config.svg';
import noNotification from './no_notification.svg';
import offIcon from './temp/offline.svg';
import infoIcon from './/temp/info.svg';
import notiIcon from './temp/notification.svg';
import reminderIcon from './temp/reminder.svg';
import deleteIcon from './delete.svg';
import deleteFlow from './delete-flow.svg';
import arrowLeft from './arrow_left.svg';
import collectionIcon from './collection.svg';
import aiwareSmall from './aiware-logo-tm.svg';
import marketplace from './market_place.svg';
import defaultVeritoneImage from './default-veritone-image.svg';
import emptyApprovals from './empty-approvals.svg';
import emptyCasesState from './empty-cases-state.svg';
import emptyFiles from './empty-files.svg';
import emptyTdo from './empty-tdo.svg';
import emptyFolders from './empty-folders.svg';
import emptyOrgInvites from './empty-org-invites.svg';
import emptyOrgRequests from './empty-org-requests.svg';
import emptySources from './empty-sources.svg';
import emptySearch from './empty-search.svg';
import publicPackage from './public-package.svg';
import privatePackage from './private-package.svg';
import defaultEngineIcon from './default-engine-icon.svg';
import moreInfo from './more-info.svg';
import myFlows from './my-flows.svg';
import myFlowsBlue from './my-flows-blue.svg';
import menuOpenPanel from './menu_open_panel.svg';
import templates from './templates.svg';
import templatesBlue from './templates-blue.svg';
import expandMoreIcon from './expand-more-icon.svg';
import certified from './certified.svg';
import fullscreen from './fullscreen.svg';
import plusIcon from './plus.svg';
import exitFullscreen from './exit_fullscreen.svg';
import filterClose from './filter_close.svg';
import copyID from './copy-id.svg';
import flowWidgetVersionIcon from './flow_widget_version_icon.svg';
import deployedCheckIcon from './deployed_check_icon.svg';
import iconPause from './icon_pause.svg';
import iconPlay from './icon_play.svg';
import noFlowsFoundIcon from './no_flows_found_icon.svg';
import noSearchResultsImg from './noSearchResultsImg.svg';
import errorIconShield from './icon_error_shield.svg';
import emptyAppAvatar from './app_avatar_empty.svg';
import dashboardIcon from './dashboard.svg';
import helpCircle from './help_circle.svg';
import notificationOutlined from './notification_outlined.svg';
import veritoneDarkLogo from './app_logo.svg';
import emptyJobs from './empty-jobs.svg';
import processingCenterEmptyState from './processing-center-empty-state.svg';
import Mailbox from './mailbox-light.svg';
import desktopAiwareLogo from './aiware-desktop.svg';
import Falcon from './Falcon.svg';
import emptyPackages from './empty-packages.svg';
import aiwareLogo from './aiware-logo.svg';
import newFlowTemplate from './new-flow-template.svg';
import alwaysUpFlow from './always-up-flow.svg';
import errorState from './error-state.svg';
import sourceNullState from './<EMAIL>';
import emptyDagsStateImage from './empty-dags-state.svg';
import emptyFlowsStateImage from './empty-flows-state.svg';
import engineNullState from './engines-null.svg';
import applicationsNullState from './applications-null.svg';
import emptyContentTemplates from './empty-content-templates.svg';
import emptyAdaptersStateImage from './empty-adapters-state.svg';
import emptyJsonImage from './empty-json.svg';
import searchEmptyExplore from './search-empty-explore.svg';

export {
  emptyAdaptersStateImage,
  emptyDagsStateImage,
  emptyFlowsStateImage,
  desktopAiwareLogo,
  iconAIWareLarge,
  emptyJobs,
  helpIcon,
  notificationIcon,
  hamburgerIcon,
  veritoneLogo,
  exampleImg,
  iconAIWare,
  iconOrg,
  iconApp,
  iconEngine,
  setting,
  emailIcon,
  noStatusLabelsTagsFound,
  notificationCenterIcon,
  notificationBannerIcon,
  unCheckIcon,
  checkedIcon,
  finder,
  importerTool,
  engineToolkit,
  illuminate,
  automate,
  redact,
  share,
  view,
  reprocess,
  veritoneLogoActive,
  notificationActive,
  helpActive,
  cameraIcon,
  openInNewIcon,
  openInNewIconBlue,
  openFlow,
  cmsAppIcon,
  discoveryAppIcon,
  notification,
  help,
  exampleImage,
  huawei,
  orgIcon,
  appIcon,
  editIcon,
  resetIcon,
  filterIcon,
  settingIcon,
  searchIcon,
  applicationTabIcon,
  personalProfileTabIcon,
  organizationTabIcon,
  preferenceTabIcon,
  activityTabIcon,
  monitorTabIcon,
  collectionAppIcon,
  libraryAppIcon,
  adminAppIcon,
  developAppIcon,
  collapseIcon,
  closeIcon,
  appleCompanyIcon,
  ciscoCompanyIcon,
  settingProfileIcon,
  settingUserIcon,
  settingOrgIcon,
  settingAppIcon,
  settingEngineIcon,
  settingEdgeIcon,
  settingConfigurationIcon,
  noNotification,
  offIcon,
  infoIcon,
  notiIcon,
  reminderIcon,
  deleteIcon,
  deleteFlow,
  arrowLeft,
  collectionIcon,
  aiwareSmall,
  marketplace,
  defaultVeritoneImage,
  emptyApprovals,
  emptyCasesState,
  emptyFiles,
  emptyTdo,
  emptyFolders,
  emptyOrgInvites,
  emptyOrgRequests,
  emptySources,
  emptySearch,
  emptyContentTemplates,
  defaultEngineIcon,
  moreInfo,
  myFlows,
  myFlowsBlue,
  menuOpenPanel,
  templates,
  expandMoreIcon,
  certified,
  templatesBlue,
  fullscreen,
  plusIcon,
  exitFullscreen,
  filterClose,
  copyID,
  flowWidgetVersionIcon,
  deployedCheckIcon,
  iconPause,
  iconPlay,
  noFlowsFoundIcon,
  noSearchResultsImg,
  errorIconShield,
  emptyAppAvatar,
  dashboardIcon,
  helpCircle,
  notificationOutlined,
  veritoneDarkLogo,
  processingCenterEmptyState,
  Mailbox,
  Falcon,
  emptyPackages,
  aiwareLogo,
  newFlowTemplate,
  alwaysUpFlow,
  publicPackage,
  privatePackage,
  errorState,
  sourceNullState,
  engineNullState,
  applicationsNullState,
  emptyJsonImage,
  searchEmptyExplore,
};
