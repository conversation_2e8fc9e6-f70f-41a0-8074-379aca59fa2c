import { I18nTranslate } from '@i18n';
import { enqueueSnackbar } from 'notistack';

interface UploadFileParams {
  selectedFolderId?: string;
  selectedCaseId?: string;
  // TODO: IS registryId supposed to be required?
  // What should happen if it's not defined?
  registryId?: string;
  rowIdTableContextMenu?: string;
  intl: ReturnType<typeof I18nTranslate.Intl>;
}

export function uploadFile({
  selectedFolderId,
  selectedCaseId,
  registryId,
  intl,
}: UploadFileParams) {
  if (window.aiware) {
    window.aiware.mountPanel({
      panelId: 'DATA_CENTER_IMPORTER',
      microFrontend: {
        name: 'DATA_CENTER_IMPORTER',
        config: {
          title: '',
          fileLimit: 10,
          titleSubText: '',
          name: intl.formatMessage({ id: 'fileUploader' }),
          panelTitle: intl.formatMessage({ id: 'uploadFiles' }),
          locationFolderTitle: intl.formatMessage({ id: 'chooseACase' }),
          locationFolderSubText: intl.formatMessage({
            id: 'selectStoredLocation',
          }),
          locationFolderInputLabel: intl.formatMessage({ id: 'case' }),
          hidePermissionsManagement: true,
          hideNewFolderButton: true,
          registryId,
          activeFolder: {
            id: selectedFolderId,
            name: selectedCaseId,
          },
        },
      },
      panelConfig: {
        dimmed: 0,
        zIndex: 1000,
        marginTop: 0,
        size: 'large',
        marginStart: 0,
        type: 'APP_BAR_PANEL_TEMPLATE',
      },
    });
  } else {
    enqueueSnackbar(I18nTranslate.TranslateMessage('unableToUploadFile'), {
      variant: 'error',
    });
  }
}
