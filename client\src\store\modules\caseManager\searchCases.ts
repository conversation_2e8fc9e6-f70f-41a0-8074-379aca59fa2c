import { GQLApi, SortBy } from '@utils/helpers';

export async function searchCases({
  caseIdSearchText,
  tagIds,
  statusIds,
  limit,
  offset,
  sortBy = SortBy.CaseDate,
  sortDirection = 'desc',
  folderContentTemplateSchemaId,
  gql,
  caseStatuses,
}: {
  caseIdSearchText?: string;
  tagIds?: string[];
  statusIds?: string[];
  limit: number;
  offset: number;
  sortBy: SortBy;
  sortDirection: 'desc' | 'asc';
  folderContentTemplateSchemaId: string;
  gql: GQLApi;
  caseStatuses?: string[];
}) {
  const result = await gql.searchCases({
    limit,
    offset,
    tagIds,
    caseIdSearchText,
    statusIds,
    sortBy,
    sortDirection,
    schemaId: folderContentTemplateSchemaId,
    caseStatuses,
  });

  return result.searchMedia.jsondata;
}
