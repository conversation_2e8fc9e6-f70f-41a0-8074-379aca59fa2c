import { GQLApi } from '@utils/helpers';
import { filter } from 'lodash';

interface Folder {
  id: string;
  name: string;
  createdDateTime: string;
}

function isFolder(item: unknown): item is Folder {
  return (
    item !== null &&
    typeof item === 'object' &&
    'id' in item &&
    'name' in item &&
    'createdDateTime' in item
  );
}

export async function checkCasesExists(
  caseIds: string[],
  gql: GQLApi
): Promise<{
  validCases: string[];
  inValidCases: string[];
}> {
  if (caseIds.length > 0) {
    const result = await gql.getFolders(caseIds);

    if (result) {
      return {
        validCases: filter(Object.values(result.data), isFolder).map(
          (folder) => folder.id
        ),
        inValidCases: Object.values(
          ('errors' in result.data && result.data.errors) || {}
        ).map((deletedCase) => deletedCase.data.objectId),
      };
    }
  }
  return {
    validCases: [],
    inValidCases: [],
  };
}
