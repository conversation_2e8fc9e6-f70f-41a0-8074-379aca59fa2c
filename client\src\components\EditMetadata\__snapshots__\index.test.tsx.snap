// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`edit metadata > should render correctly 1`] = `
<DocumentFragment>
  <form
    class="edit-metadata-form"
  >
    <div
      class="edit-metadata-container"
    >
      <div
        class="header"
      >
        <p>
          Edit Metadata
        </p>
        <button
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium close-btn css-53g0n7-MuiButtonBase-root-MuiIconButton-root"
          data-testid="close-btn"
          id=":r0:"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CloseIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>
      <p
        class="warning"
      >
        Any changes to asset metadatas are permanent and can not be undone.
      </p>
      <div
        class="main-content"
      >
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File Name
          </label>
          <div
            class="metadata-content"
          >
            <div
              class="edit-metadata-input"
            >
              <div
                class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root css-cmpglg-MuiFormControl-root-MuiTextField-root"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl metadata-input normal-input css-1blp12k-MuiInputBase-root-MuiOutlinedInput-root"
                >
                  <input
                    aria-invalid="false"
                    class="MuiInputBase-input MuiOutlinedInput-input css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input"
                    data-testid="fileName-textfield"
                    id=":r1:"
                    name="fileName"
                    rows="1"
                    type="text"
                    value="bloomberg.mp4"
                  />
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline css-1ll44ll-MuiOutlinedInput-notchedOutline"
                  >
                    <legend
                      class="css-w4cd9x"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="metadata-field metadata-text-area"
        >
          <label
            class="metadata-label text-area-label"
          >
            Description
          </label>
          <div
            class="metadata-content"
          >
            <div
              class="edit-metadata-input"
            >
              <div
                class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root css-cmpglg-MuiFormControl-root-MuiTextField-root"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-multiline metadata-input text-area-input css-zsp0bh-MuiInputBase-root-MuiOutlinedInput-root"
                >
                  <textarea
                    aria-invalid="false"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-w4nesw-MuiInputBase-input-MuiOutlinedInput-input"
                    data-testid="description-textfield"
                    id=":r2:"
                    name="description"
                    rows="3"
                    style="height: 0px; overflow: hidden;"
                  >
                    description123
                  </textarea>
                  <textarea
                    aria-hidden="true"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-w4nesw-MuiInputBase-input-MuiOutlinedInput-input"
                    readonly=""
                    style="visibility: hidden; position: absolute; overflow: hidden; height: 0px; top: 0px; left: 0px; transform: translateZ(0); padding-top: 0px; padding-bottom: 0px; width: 100%;"
                    tabindex="-1"
                  />
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline css-1ll44ll-MuiOutlinedInput-notchedOutline"
                  >
                    <legend
                      class="css-w4cd9x"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
              <svg
                aria-hidden="true"
                class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium text-area-icon css-1umw9bq-MuiSvgIcon-root"
                data-testid="ArrowDropUpIcon"
                focusable="false"
                viewBox="0 0 24 24"
              >
                <path
                  d="m7 14 5-5 5 5z"
                />
              </svg>
            </div>
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Summary
          </label>
          <div
            class="metadata-content truncate"
          >
            summary123
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Upload Date
          </label>
          <div
            class="metadata-content text-only"
          >
            05/16/2025
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File Size
          </label>
          <div
            class="metadata-content text-only"
          >
            2.5 MB
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File Format
          </label>
          <div
            class="metadata-content text-only"
          >
            MP4
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Content Type
          </label>
          <div
            class="metadata-content text-only"
          >
            Video
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File ID
          </label>
          <div
            class="metadata-content text-only"
          >
            tdoId123
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Duration
          </label>
          <div
            class="metadata-content text-only"
          >
            00:02:30
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Created By
          </label>
          <div
            class="metadata-content text-only"
          >
            Test User
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Asset Status
          </label>
          <div
            class="metadata-content text-only"
          >
            Processed
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Evidence Type
          </label>
          <div
            class="metadata-content"
          >
            <div
              class="edit-metadata-select"
            >
              <div
                class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth metadata-select-input MuiSelect-root css-ovbmre-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
              >
                <div
                  aria-expanded="false"
                  aria-haspopup="listbox"
                  aria-labelledby="mui-component-select-evidenceType"
                  class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                  id="mui-component-select-evidenceType"
                  role="combobox"
                  tabindex="0"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </div>
                <input
                  aria-hidden="true"
                  aria-invalid="false"
                  class="MuiSelect-nativeInput css-j0riat-MuiSelect-nativeInput"
                  name="evidenceType"
                  tabindex="-1"
                  value=""
                />
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-lohd6h-MuiSvgIcon-root-MuiSelect-icon"
                  data-testid="ArrowDropDownIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M7 10l5 5 5-5z"
                  />
                </svg>
                <fieldset
                  aria-hidden="true"
                  class="MuiOutlinedInput-notchedOutline css-1ll44ll-MuiOutlinedInput-notchedOutline"
                >
                  <legend
                    class="css-w4cd9x"
                  >
                    <span
                      aria-hidden="true"
                      class="notranslate"
                    >
                      ​
                    </span>
                  </legend>
                </fieldset>
              </div>
            </div>
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Case ID
          </label>
          <div
            class="metadata-content text-only"
          >
            caseId123
          </div>
        </div>
      </div>
      <div
        class="footer"
      >
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary cancel css-1uent87-MuiButtonBase-root-MuiButton-root"
          id=":r4:"
          tabindex="0"
          type="button"
        >
          Cancel
        </button>
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary submit css-74d805-MuiButtonBase-root-MuiButton-root"
          data-testid="save-changes-btn"
          disabled=""
          id=":r5:"
          tabindex="-1"
          type="submit"
        >
          Save Changes
        </button>
      </div>
    </div>
  </form>
</DocumentFragment>
`;

exports[`edit metadata > should render without duration or file size 1`] = `
<DocumentFragment>
  <form
    class="edit-metadata-form"
  >
    <div
      class="edit-metadata-container"
    >
      <div
        class="header"
      >
        <p>
          Edit Metadata
        </p>
        <button
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium close-btn css-53g0n7-MuiButtonBase-root-MuiIconButton-root"
          data-testid="close-btn"
          id=":r7:"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CloseIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>
      <p
        class="warning"
      >
        Any changes to asset metadatas are permanent and can not be undone.
      </p>
      <div
        class="main-content"
      >
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File Name
          </label>
          <div
            class="metadata-content"
          >
            <div
              class="edit-metadata-input"
            >
              <div
                class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root css-cmpglg-MuiFormControl-root-MuiTextField-root"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl metadata-input normal-input css-1blp12k-MuiInputBase-root-MuiOutlinedInput-root"
                >
                  <input
                    aria-invalid="false"
                    class="MuiInputBase-input MuiOutlinedInput-input css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input"
                    id=":r8:"
                    name="fileName"
                    rows="1"
                    type="text"
                    value="bloomberg.mp4"
                  />
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline css-1ll44ll-MuiOutlinedInput-notchedOutline"
                  >
                    <legend
                      class="css-w4cd9x"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="metadata-field metadata-text-area"
        >
          <label
            class="metadata-label text-area-label"
          >
            Description
          </label>
          <div
            class="metadata-content"
          >
            <div
              class="edit-metadata-input"
            >
              <div
                class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root css-cmpglg-MuiFormControl-root-MuiTextField-root"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-multiline metadata-input text-area-input css-zsp0bh-MuiInputBase-root-MuiOutlinedInput-root"
                >
                  <textarea
                    aria-invalid="false"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-w4nesw-MuiInputBase-input-MuiOutlinedInput-input"
                    id=":r9:"
                    name="description"
                    rows="3"
                    style="height: 0px; overflow: hidden;"
                  >
                    description123
                  </textarea>
                  <textarea
                    aria-hidden="true"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputMultiline css-w4nesw-MuiInputBase-input-MuiOutlinedInput-input"
                    readonly=""
                    style="visibility: hidden; position: absolute; overflow: hidden; height: 0px; top: 0px; left: 0px; transform: translateZ(0); padding-top: 0px; padding-bottom: 0px; width: 100%;"
                    tabindex="-1"
                  />
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline css-1ll44ll-MuiOutlinedInput-notchedOutline"
                  >
                    <legend
                      class="css-w4cd9x"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
              <svg
                aria-hidden="true"
                class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium text-area-icon css-1umw9bq-MuiSvgIcon-root"
                data-testid="ArrowDropUpIcon"
                focusable="false"
                viewBox="0 0 24 24"
              >
                <path
                  d="m7 14 5-5 5 5z"
                />
              </svg>
            </div>
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Summary
          </label>
          <div
            class="metadata-content truncate"
          >
            summary123
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Upload Date
          </label>
          <div
            class="metadata-content text-only"
          >
            05/16/2025
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File Size
          </label>
          <div
            class="metadata-content text-only"
          >
            2.5 MB
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File Format
          </label>
          <div
            class="metadata-content text-only"
          >
            mp4
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Content Type
          </label>
          <div
            class="metadata-content text-only"
          >
            video
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            File ID
          </label>
          <div
            class="metadata-content text-only"
          >
            tdoId123
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Duration
          </label>
          <div
            class="metadata-content text-only"
          >
            00:02:30
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Created By
          </label>
          <div
            class="metadata-content text-only"
          >
            Test User
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Asset Status
          </label>
          <div
            class="metadata-content text-only"
          >
            Processed
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Evidence Type
          </label>
          <div
            class="metadata-content"
          >
            <div
              class="edit-metadata-select"
            >
              <div
                class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth metadata-select-input MuiSelect-root css-ovbmre-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root"
              >
                <div
                  aria-expanded="false"
                  aria-haspopup="listbox"
                  aria-labelledby="mui-component-select-evidenceType"
                  class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input"
                  id="mui-component-select-evidenceType"
                  role="combobox"
                  tabindex="0"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </div>
                <input
                  aria-hidden="true"
                  aria-invalid="false"
                  class="MuiSelect-nativeInput css-j0riat-MuiSelect-nativeInput"
                  name="evidenceType"
                  tabindex="-1"
                  value=""
                />
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-lohd6h-MuiSvgIcon-root-MuiSelect-icon"
                  data-testid="ArrowDropDownIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M7 10l5 5 5-5z"
                  />
                </svg>
                <fieldset
                  aria-hidden="true"
                  class="MuiOutlinedInput-notchedOutline css-1ll44ll-MuiOutlinedInput-notchedOutline"
                >
                  <legend
                    class="css-w4cd9x"
                  >
                    <span
                      aria-hidden="true"
                      class="notranslate"
                    >
                      ​
                    </span>
                  </legend>
                </fieldset>
              </div>
            </div>
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Case Name
          </label>
          <div
            class="metadata-content text-only"
          >
            caseName123
          </div>
        </div>
        <div
          class="metadata-field"
        >
          <label
            class="metadata-label"
          >
            Case ID
          </label>
          <div
            class="metadata-content text-only"
          >
            caseId123
          </div>
        </div>
      </div>
      <div
        class="footer"
      >
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary cancel css-1uent87-MuiButtonBase-root-MuiButton-root"
          id=":rb:"
          tabindex="0"
          type="button"
        >
          Cancel
        </button>
        <button
          class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary submit css-74d805-MuiButtonBase-root-MuiButton-root"
          disabled=""
          id=":rc:"
          tabindex="-1"
          type="submit"
        >
          Save Changes
        </button>
      </div>
    </div>
  </form>
</DocumentFragment>
`;
