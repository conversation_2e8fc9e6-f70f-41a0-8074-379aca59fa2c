export enum SettingDatatTestSelector {
  TabSettings = 'nav-tab-settings',
  AddNewButton = 'add-button',
  DialogRole = 'dialog',
  InputStatus = 'status-name-input',
  InputTag = 'tag-name-input',
  ConfirmBtn = 'confirm-button',
  ColorPicker = 'color-picker',
  Notification = '#notistack-snackbar',
  EditButton = 'edit-button',
  NameReorder = 'reorder-table-name-edit',
  DragButton = 'reorder-table-drag-handle',
  VisibilityDropdown = 'reorder-table-visibility-edit',
  DeleteBtn = 'reorder-table__remove-button',
  CheckBox = 'row-check-box',
  ActiveButton = 'active-radio',
  InactiveButton = 'inactive-radio',
  AddStatusBtnText = 'Add Status',
  SettingContent = 'settings-content',
  DialogColorPicker = '.settings__dialog-color-picker',
  FirstRow = 'tbody tr:first-child',
  SaturationPointer = '.react-colorful__saturation-pointer',
  HexPointer = 'color-picker-hex-input',
  ColorSwatch = 'color-picker-swatch',
  CheckIcon = 'CheckIcon',
  StatusDialogTitle = 'status-dialog-title',
  CancelBtn = 'cancel-button',
  ChangeColorBtn = 'change-color-button',
  ChangeVisibilityBtn = 'change-visibility-button',
  DeleteStatusBtn = 'delete-status-button',
}

export enum SettingColor {
  YellowColor = 'rgb(250, 204, 21);',
  YellowColorCode = '#FACC15',
  RedColorRGB = 'rgb(255, 87, 51)',
  RedColorCode = '#FF5733',
}

export const StatusTestData = {
  StatusName: 'Status1',
};

export enum SettingsType {
  Status = 'status',
  Tag = 'tag',
}

export enum ElementText {
  ErrorMessage = 'Invalid character only "-" and "_" are allowed.',
  SaveChangesBtnText = 'Save Changes',
  AddedStatusMessage = 'Status added successfully',
  SavedStatusMessage = 'Statuses saved successfully',
}

export const enum SettingsGraphql {
  FetchSettingStatuses = '\n    query getLatestSDO {\n      structuredDataObjects(\n        schemaId: \"1ef079a4-2f66-4946-955d-ce3665af0c9d\"\n        limit:1\n        offset:0\n        orderBy: [{\n          field: modifiedDateTime,\n          direction: desc\n        }]) {\n        records {\n          id\n          data\n        }\n      }\n    }',
  DeleteQuery = `\n      mutation createStructuredData($schemaId: ID!, $id: ID, $data: JSONData) {\n        createStructuredData(input: {schemaId: $schemaId, id: $id, data: $data }) {\n          id\n          data\n        }\n      }`,
  FetchSettingTag = `\n    query getLatestSDO {\n      structuredDataObjects(\n        schemaId: \"a30fa449-aeb7-4945-95de-b47130f1d9ed\"\n        limit:1\n        offset:0\n        orderBy: [{\n          field: modifiedDateTime,\n          direction: desc\n        }]) {\n        records {\n          id\n          data\n        }\n      }\n    }`,
}
