[{"description": "", "elements": [{"description": "", "id": "health-check;health-check-case-management-screen", "keyword": "<PERSON><PERSON><PERSON>", "line": 4, "name": "Health Check Case Management screen", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": **********}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "The user verify Case Management screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/health-check/health-check.ts:20"}, "result": {"status": "passed", "duration": 12153000000}}], "tags": [{"name": "@e2e", "line": 3}, {"name": "@health-check", "line": 3}], "type": "scenario"}, {"description": "", "id": "health-check;health-check-settings-screen", "keyword": "<PERSON><PERSON><PERSON>", "line": 8, "name": "Health Check Settings screen", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 543000000}}, {"arguments": [], "keyword": "Given ", "line": 9, "name": "The user verify Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/health-check/health-check.ts:29"}, "result": {"status": "passed", "duration": 24690000000}}], "tags": [{"name": "@e2e", "line": 7}, {"name": "@health-check", "line": 7}], "type": "scenario"}, {"description": "", "id": "health-check;health-check-search-screen", "keyword": "<PERSON><PERSON><PERSON>", "line": 12, "name": "Health Check Search screen", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 575000000}}, {"arguments": [], "keyword": "Given ", "line": 13, "name": "The user verify Search screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/health-check/health-check.ts:47"}, "result": {"status": "passed", "duration": 10642000000}}], "tags": [{"name": "@e2e", "line": 11}, {"name": "@health-check", "line": 11}], "type": "scenario"}], "id": "health-check", "line": 1, "keyword": "Feature", "name": "Health Check", "tags": [], "uri": "cypress\\e2e\\features\\health-check.feature"}]