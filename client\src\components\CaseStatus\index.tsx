import './index.scss';
import { I18nTranslate } from '@i18n';
import { useSelector } from 'react-redux';
import { CaseStatus } from '../../types/types';
import { Checkmark } from '../../assets/icons';
import { MouseEvent, useMemo, useRef, useState, useLayoutEffect } from 'react';
import { Divider, Menu, MenuItem } from '@mui/material';
import { selectFetchedStatuses } from '../../store/modules/settings/slice';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { useAppDispatch } from '../../store/hooks';
import {
  setStatusMenuSelectedId,
  selectStatusMenuSelectedId,
} from '../../store/modules/caseManager/slice.ts';

const ITEM_HEIGHT = 40;

const CaseStatusMenu = ({
  currentStatusId,
  onSaveStatus,
  onClick,
  currentRowId,
}: Props) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const caseStatuses: CaseStatus[] = useSelector(selectFetchedStatuses);
  const selectedStatusMenuId = useSelector(selectStatusMenuSelectedId);
  const dispatch = useAppDispatch();
  const buttonElementRef = useRef<HTMLButtonElement>(null);

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    onClick?.();
    dispatch(setStatusMenuSelectedId(currentRowId || ''));
    setAnchorEl(event.currentTarget);
  };

  const handleClose = (event: MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(null);
    dispatch(setStatusMenuSelectedId(''));
  };

  const handleDeleteStatus = (event: MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    onClick?.();
    onSaveStatus('', currentRowId);
  };

  const currentStatus = useMemo(
    () => caseStatuses.find((caseStatus) => caseStatus.id === currentStatusId),
    [currentStatusId, caseStatuses]
  );

  useLayoutEffect(() => {
    if (buttonElementRef.current) {
      setAnchorEl(buttonElementRef.current);
    }
  }, []);

  return (
    <div className="case-status__container" data-testid="case-status">
      <button
        onClick={handleClick}
        style={{
          borderColor: currentStatus?.color,
          borderStyle: currentStatus ? 'solid' : 'dashed',
        }}
        data-testid="case-status-button"
        ref={buttonElementRef}
      >
        {currentStatus?.label ||
          I18nTranslate.TranslateMessage('selectAStatus')}
      </button>
      {selectedStatusMenuId && currentRowId === selectedStatusMenuId && (
        <Menu
          anchorEl={anchorEl}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: -10,
            horizontal: 'center',
          }}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          className="case-status-menu"
          slotProps={{
            paper: {
              style: {
                // Max 10 items, plus padding of the menu
                maxHeight: ITEM_HEIGHT * 10 + 8,
              },
            },
          }}
          data-testid="case-status-menu"
        >
          {currentStatus?.label && (
            <MenuItem
              onClick={(event) => {
                handleDeleteStatus(event);
                handleClose(event);
              }}
              data-testid={`case-status-item-delete`}
            >
              <span className="check-icon" />
              <RemoveCircleOutlineIcon className="status-label-delete-icon" />
              <span className="status-label">
                {I18nTranslate.TranslateMessage('delete')}
              </span>
            </MenuItem>
          )}
          {currentStatus?.label && <Divider />}
          {caseStatuses
            .filter((caseStatus) => caseStatus.active)
            .map((caseStatus) => (
              <MenuItem
                key={`CaseStatus-${Math.floor(Math.random() * 1000)}-${caseStatus.id}`}
                onClick={(event) => {
                  if (caseStatus.id !== currentStatusId) {
                    onSaveStatus(caseStatus.id, currentRowId);
                  }
                  handleClose(event);
                }}
                data-testid={`case-status-item-${caseStatus.id}`}
              >
                <span
                  className="check-icon"
                  data-testid={`check-icon-${caseStatus.id}`}
                >
                  {currentStatusId === caseStatus.id && <Checkmark />}
                </span>
                <span
                  className="color-icon"
                  style={{ backgroundColor: caseStatus.color }}
                />
                <span className="status-label">{caseStatus.label}</span>
              </MenuItem>
            ))}
        </Menu>
      )}
    </div>
  );
};

interface Props {
  currentStatusId: string | undefined;
  onSaveStatus: (statusId: string, rowId: string | undefined) => void;
  onClick?: () => void;
  currentRowId?: string;
}

export default CaseStatusMenu;
