import { vi, expect, describe, beforeEach, afterEach, it } from 'vitest';
import {
  EXPIRATION_MS,
  savePendingDeleteCaseToLocalStorage,
  updatePendingDeleteCaseToLocalStorage,
} from '.';

describe('savePendingDeleteCaseToLocalStorage', () => {
  beforeEach(() => {
    // tell vitest to use mocked time
    vi.useFakeTimers();
    localStorage.clear();
  });

  afterEach(() => {
    // restoring date after each test run
    vi.useRealTimers();
    localStorage.clear();
  });

  it('save correctly', () => {
    // set time for test
    const date = new Date(2025, 2, 19, 9, 30);
    vi.setSystemTime(date);
    const ids = savePendingDeleteCaseToLocalStorage(['id1']);
    expect(ids).toEqual([
      { value: 'id1', expiry: date.getTime() + EXPIRATION_MS },
    ]);
  });

  it('save multiple values correctly', () => {
    // set time for test
    const date = new Date(2025, 2, 19, 9, 30);
    vi.setSystemTime(date);
    const ids = savePendingDeleteCaseToLocalStorage(['id1']);
    expect(ids).toEqual([
      { value: 'id1', expiry: date.getTime() + EXPIRATION_MS },
    ]);

    const date2 = new Date(2025, 2, 19, 9, 31);
    vi.setSystemTime(date2);
    const ids2 = savePendingDeleteCaseToLocalStorage(['id2']);

    expect(ids2).toEqual([
      { value: 'id1', expiry: date.getTime() + EXPIRATION_MS },
      { value: 'id2', expiry: date2.getTime() + EXPIRATION_MS },
    ]);
  });

  it('save old item is removed correctly', () => {
    // set time for test
    const date = new Date(2025, 2, 19, 9, 30);
    vi.setSystemTime(date);
    const ids = savePendingDeleteCaseToLocalStorage(['id1']);
    expect(ids).toEqual([
      { value: 'id1', expiry: date.getTime() + EXPIRATION_MS },
    ]);

    const min = EXPIRATION_MS / (60 * 1000);
    const date2 = new Date(2025, 2, 19, 9, 30 + min);
    vi.setSystemTime(date2);
    const ids2 = savePendingDeleteCaseToLocalStorage(['id2']);
    expect(ids2).toEqual([
      { value: 'id2', expiry: date2.getTime() + EXPIRATION_MS },
    ]);
  });
});

describe('updatePendingDeleteCaseToLocalStorage', () => {
  beforeEach(() => {
    // tell vitest to use mocked time
    vi.useFakeTimers();
    localStorage.clear();
  });

  afterEach(() => {
    // restoring date after each test run
    vi.useRealTimers();
    localStorage.clear();
  });

  it('update correctly', () => {
    // set time for test
    const date = new Date(2025, 2, 19, 9, 30);
    vi.setSystemTime(date);
    const ids = savePendingDeleteCaseToLocalStorage(['id1']);
    expect(ids).toEqual([
      { value: 'id1', expiry: date.getTime() + EXPIRATION_MS },
    ]);
    const min = EXPIRATION_MS / (60 * 1000);
    const date2 = new Date(2025, 2, 19, 9, 30 + min);
    vi.setSystemTime(date2);
    const ids2 = updatePendingDeleteCaseToLocalStorage();
    expect(ids2).toEqual([]);
  });
});
