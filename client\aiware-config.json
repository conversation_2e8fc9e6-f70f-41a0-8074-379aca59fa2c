[{"environmentName": {"default": "on-prem"}}, "automateControllerUrl", "controllerNodeRedImage", "sessionTimeout", {"apiRoot": {"service": "core-admin-server", "postfix": "/v1/admin"}}, {"loginUrl": {"service": "desktop-app", "postfix": "/ui/auth/login"}}, {"cmsAppUrl": {"service": "cms", "postfix": ""}}, {"adminAppUrl": {"service": "admin-app", "postfix": ""}}, {"automateUrl": {"service": "automate-app", "postfix": ""}}, {"baseUrl": {"service": "core-graphql-server", "postfix": "/v3/graphql"}}, {"switchAppUrl": {"service": "core-admin-server", "postfix": "/api/admin/switch-app"}}, {"hubUrl": {"default": "https://hub.aiware.com/hub/v1"}}, {"sentryDSN": {"default": "https://<EMAIL>/4505172433043456"}}, "signupUrl"]