import { constant } from 'lodash';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { setDarkMode, detectThemeChange, initTheme } from '@theme';

describe('Initialize Theme', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('sets dark mode', () => {
    setDarkMode(true);
    expect(document.querySelector?.('body')?.classList).toContain('dark');
    expect(document.querySelector?.('body')?.classList).not.toContain('light');
  });

  it('sets light mode', () => {
    setDarkMode(false);
    expect(document.querySelector?.('body')?.classList).toContain('light');
    expect(document.querySelector?.('body')?.classList).not.toContain('dark');
  });

  it('handles undefined body', () => {
    const originalQuerySelector = document.querySelector;
    document.querySelector = vi.fn(() => undefined);

    expect(() => setDarkMode(true)).not.toThrow();
    expect(() => setDarkMode(false)).not.toThrow();

    document.querySelector = originalQuerySelector;
  });

  it('detects theme change and uses addEventListener', () => {
    const mockMatchMedia = vi.fn().mockImplementation((query) => ({
      matches: query === '(prefers-color-scheme: dark)',
      addEventListener: vi.fn((_event, callback) =>
        callback({ matches: true })
      ),
      addListener: vi.fn(),
    }));
    window.matchMedia = mockMatchMedia;

    detectThemeChange();

    expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    expect(document.querySelector?.('body')?.classList).toContain('dark');
  });

  it('detects theme change and uses addListener when addEventListener is not available', () => {
    const mockMatchMedia = vi.fn().mockImplementation((query) => ({
      matches: query === '(prefers-color-scheme: dark)',
      addListener: vi.fn((callback) => callback({ matches: false })),
    }));
    window.matchMedia = mockMatchMedia;

    detectThemeChange();

    expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    expect(document.querySelector?.('body')?.classList).toContain('light');
  });

  it('initializes theme to dark', () => {
    vi.spyOn(window.localStorage.__proto__, 'getItem');
    window.localStorage.__proto__.getItem = vi.fn(constant('Dark'));
    setDarkMode(true);
    initTheme();
    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('initializes theme to light', () => {
    vi.spyOn(window.localStorage.__proto__, 'getItem');
    window.localStorage.__proto__.getItem = vi.fn(constant('Light'));
    setDarkMode(false);
    initTheme();
    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('initializes theme to unknown', () => {
    vi.spyOn(window.localStorage.__proto__, 'getItem');
    window.localStorage.__proto__.getItem = vi.fn(constant('Unknown'));
    setDarkMode(false);
    initTheme();
    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });
});
