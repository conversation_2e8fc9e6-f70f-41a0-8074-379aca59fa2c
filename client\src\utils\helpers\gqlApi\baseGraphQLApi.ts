import { isEmpty } from 'lodash';

export interface GQLResponse<T> {
  data: T;
  // TODO: Validate the type of errors
  errors?: {
    data?: {
      requestId?: string;
    };
    message?: string;
  }[];
}

/**
 * Base Graphql api for common usage.
 *
 * @param gqlEndpoint
 * @param token
 * @param query
 * @param variables
 * @param operationName
 * @param veritoneAppId veritone application id
 * @param extraHeaders
 */
export async function baseGraphQLApi<T>({
  gqlEndpoint,
  token,
  query,
  variables,
  operationName,
  veritoneAppId,
  extraHeaders,
  abortSignal,
}: {
  gqlEndpoint: string;
  token?: string;
  query: string;
  variables?: Record<string, unknown>;
  operationName?: unknown;
  veritoneAppId?: string;
  extraHeaders?: T;
  abortSignal?: AbortSignal;
}): Promise<GQLResponse<T>> {
  const response = await fetch(gqlEndpoint, {
    method: 'post',
    body: JSON.stringify({
      query,
      variables,
      operationName,
    }),
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...extraHeaders,
      ...(veritoneAppId && { 'x-veritone-application': veritoneAppId }),
    },
    signal: abortSignal,
  });

  // TODO: What can we do to improve this?
  // Should we be awaiting or just returning response.json ?
  const resp = (await response.json()) as GQLResponse<T>;
  return resp;
}

/**
 * Base Graphql api throws error when there is graphql errors in response.
 * @param gqlEndpoint
 * @param token
 * @param query
 * @param variables
 * @param operationName
 * @param veritoneAppId veritone application id
 */
export async function baseGraphQLApiThrowError<T = unknown>({
  gqlEndpoint,
  token,
  query,
  variables,
  operationName,
  veritoneAppId,
  abortSignal,
}: {
  gqlEndpoint: string;
  token: string;
  query: string;
  variables?: Record<string, unknown>;
  operationName?: string;
  veritoneAppId?: string;
  abortSignal?: AbortSignal;
}): Promise<T> {
  const resp = await baseGraphQLApi<T>({
    query,
    variables,
    operationName,
    gqlEndpoint,
    token,
    veritoneAppId,
    abortSignal,
  });
  if (!isEmpty(resp.errors)) {
    let message = (resp?.errors || [])[0]?.message || operationName || 'Error';

    if (resp.errors && resp.errors.length > 0) {
      message = resp.errors[0].message || operationName || 'Error';
      // Add the requestId to the message if available
      const requestId = resp.errors[0]?.data?.requestId;
      message += requestId ? ':' + resp.errors[0]?.data?.requestId : '';
    }
    throw new Error(message);
  }
  return resp.data;
}
