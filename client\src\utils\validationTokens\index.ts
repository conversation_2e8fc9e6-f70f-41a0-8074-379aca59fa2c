import {
  CognitionItem,
  CognitionList,
} from '@components/CognitionAutocomplete';
import {
  FaceDetectionTerm,
  MathOperators,
  ObjectDetectionTerm,
  validateFaceCognitionTerms,
  validateObjectCognitionTerms,
} from '@store/modules/search/slice';
import { v4 as uuid } from 'uuid';

const isValidOperator = ({
  prev,
  next,
}: {
  prev?: CognitionItem;
  next?: CognitionItem;
}) => {
  if (!prev || !next) {
    return false;
  }

  if (
    prev.type === 'operator' ||
    (prev.value as MathOperators) === MathOperators.OPEN_PARENTHESIS
  ) {
    return false;
  }

  if (next.type === 'value' && !next.isValid) {
    return false;
  }

  if (
    next.type === 'operator' ||
    (next.value as MathOperators) === MathOperators.CLOSE_PARENTHESIS
  ) {
    return false;
  }

  return true;
};

function cleanFlatTokens(tokens: CognitionList): CognitionList {
  const result: CognitionList = [];

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];

    if (token.type === 'value') {
      if (token.isValid) {
        result.push(token);
      }
    } else if (token.type === 'operator') {
      const prev = result[result.length - 1];
      const next = tokens[i + 1];

      if (isValidOperator({ prev, next })) {
        result.push(token);
      }
    } else if (token.type === 'parenthesis') {
      result.push(token);
    }
  }

  return result;
}

// clean string that doesn't query entity/library/object
export function cleanValidTokens(tokens: CognitionList): CognitionList {
  const stack: CognitionList[] = [[]];

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];

    if ((token.value as MathOperators) === MathOperators.OPEN_PARENTHESIS) {
      stack.push([]);
    } else if (
      (token.value as MathOperators) === MathOperators.CLOSE_PARENTHESIS
    ) {
      const innerExpression = stack.pop();
      if (!innerExpression) {
        continue;
      }

      const cleanedExpression = cleanFlatTokens(innerExpression);
      const prevStack = stack[stack.length - 1];

      if (cleanedExpression.length === 0) {
        continue;
      } else if (
        cleanedExpression.length === 1 &&
        cleanedExpression[0].type === 'value'
      ) {
        prevStack.push(cleanedExpression[0]);
      } else {
        prevStack.push({
          id: `open-parenthesis-${uuid()}`,
          type: 'parenthesis',
          cognitionType: 'operator',
          value: MathOperators.OPEN_PARENTHESIS,
        });
        prevStack.push(...cleanedExpression);
        prevStack.push({
          id: `close-parenthesis-${uuid()}`,
          type: 'parenthesis',
          cognitionType: 'operator',
          value: MathOperators.CLOSE_PARENTHESIS,
        });
      }
    } else {
      stack[stack.length - 1].push(token);
    }
  }

  return cleanFlatTokens(stack[0]);
}

// maybe don't need cleanRedundantParentheses function
// because check isRedundantParentheses before adding new parenthesis

// export const cleanRedundantParentheses = (tokens: CognitionList) => {
//   const openStack: number[] = [];

//   for (let i = 0; i < tokens.length; i++) {
//     const token = tokens[i];

//     if (token.type === 'parenthesis') {
//       const value = token.value as MathOperators;

//       if (value === MathOperators.OPEN_PARENTHESIS) {
//         openStack.push(i);
//       } else if (value === MathOperators.CLOSE_PARENTHESIS) {
//         if (openStack.length > 0) {
//           const openIndex = openStack.pop();
//           if (openIndex === undefined) {
//             continue;
//           }

//           const innerExpression = tokens.slice(openIndex + 1, i);

//           const isRedundant =
//             innerExpression.length === 1 ||
//             ((innerExpression[0].value as MathOperators) ===
//               MathOperators.OPEN_PARENTHESIS &&
//               (innerExpression[innerExpression.length - 1]
//                 .value as MathOperators) === MathOperators.CLOSE_PARENTHESIS);

//           if (isRedundant) {
//             tokens.splice(openIndex, 1);
//             tokens.splice(i - 1, 1);
//             i = i - 2;
//           }
//         }
//       }
//     }
//   }

//   if (
//     (tokens[0]?.value as MathOperators) === MathOperators.OPEN_PARENTHESIS &&
//     (tokens[tokens.length - 1]?.value as MathOperators) ===
//       MathOperators.CLOSE_PARENTHESIS
//   ) {
//     tokens.splice(0, 1);
//     tokens.splice(tokens.length - 1, 1);
//   }

//   return tokens;
// };

const checkRedundant = (tokens: CognitionList) =>
  tokens.length > 2 &&
  tokens[0].type === 'parenthesis' &&
  tokens[tokens.length - 1].type === 'parenthesis';

export const isRedundantParentheses = (tokens: CognitionList) => {
  const openStack: number[] = [];
  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];

    if (token.type === 'parenthesis') {
      const value = token.value as MathOperators;

      if (value === MathOperators.OPEN_PARENTHESIS) {
        openStack.push(i);
      } else if (value === MathOperators.CLOSE_PARENTHESIS) {
        if (openStack.length > 0) {
          const openIndex = openStack.pop();
          if (openIndex === undefined) {
            continue;
          }

          const innerExpression = tokens.slice(openIndex + 1, i);
          if (checkRedundant(innerExpression)) {
            return true;
          }
        }
      }
    }
  }

  return false;
};

export const formFaceDetectionsToSearchParams = (
  detections: CognitionItem[]
): FaceDetectionTerm[] => {
  const faceDetectionsTerms = detections
    ?.map((term) => {
      switch (term.cognitionType) {
        case 'entity':
          return {
            type: 'entity',
            entityId: term.id,
          };
        case 'library':
          return {
            type: 'library',
            libraryId: term.id,
          };
        case 'string':
          return {
            type: 'string',
            stringQuery: term.value,
          };
        case 'operator':
          return {
            type: 'operator',
            operator: term.value,
          };
        default:
          return null;
      }
    })
    .filter((term) => term !== null);

  return faceDetectionsTerms as FaceDetectionTerm[];
};

export const formObjectDetectionsToSearchParams = (
  detections: CognitionItem[]
): ObjectDetectionTerm[] => {
  const objectDescriptorsTerms = detections
    ?.map((term) => {
      switch (term.cognitionType) {
        case 'foundString':
          return {
            type: 'foundString',
            foundString: term.id || '',
          };
        case 'foundFullTextString':
          return {
            type: 'foundFullTextString',
            foundFullTextString: term.value,
          };
        case 'operator':
          return {
            type: 'operator',
            operator: term.value,
          };
        default:
          return null;
      }
    })
    .filter((term) => term !== null);

  return objectDescriptorsTerms as ObjectDetectionTerm[];
};

export const faceDetectionsValidation = (detections: CognitionItem[]) => {
  if (!detections || detections.length === 0) {
    return { isValid: true }; // No face detections, validation passes
  }
  const faceDetectionsTerms = formFaceDetectionsToSearchParams(detections);

  try {
    validateFaceCognitionTerms(faceDetectionsTerms);
  } catch (error) {
    const message = (error as Error).message;
    return { isValid: false, reason: message }; // Return the validation error message
  }

  return { isValid: true }; // Validation passes
};

export const objectDetectionsValidation = (detections: CognitionItem[]) => {
  if (!detections || detections.length === 0) {
    return { isValid: true }; // No face detections, validation passes
  }
  const objectDetectionsTerms = formObjectDetectionsToSearchParams(detections);

  try {
    validateObjectCognitionTerms(objectDetectionsTerms);
  } catch (error) {
    const message = (error as Error).message;
    return { isValid: false, reason: message }; // Return the validation error message
  }

  return { isValid: true }; // Validation passes
};
