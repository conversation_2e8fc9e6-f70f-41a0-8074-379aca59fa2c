Feature: Status Settings

  Background: The user is on Settings screen
    Given The user logins successfully
    Given The user deletes status if exists
      | statusName     |
      | Status1        |
      | Status2        |
      | Status3        |
      | Status4        |
      | Status5        |
      | Status6        |
      | Status7        |
      | Status8        |
      | Status9        |
      | Status10       |
      | StatusReorder1 |
      | StatusReorder2 |
      | UpdatedStatus  |
      | multipleEdit   |
    And The user is on Settings screen

  @e2e @status-settings
  Scenario: Add status
    When The user clicks on the Add button
    Then The user sees Add New "status" modal
    When The user inputs "status" name "Status 1"
    Then An error appears showing that the name is not valid
    When The user inputs "status" name "Status1"
    And The user selects color "rgb(250, 204, 21)"
    And The user confirms by clicking the Add button
    Then The "1st" row of the "status" list should be updated with name "Status1"
    And A snack notification 'Status added successfully' confirming this has succeeded

  @e2e @status-settings
  Scenario: Add status in edit mode
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user clicks on the Add button
    Then The user sees Add New "status" modal
    When The user inputs "status" name "Status 2"
    Then An error appears showing that the name is not valid
    When The user inputs "status" name "Status2"
    And The user selects color "rgb(250, 204, 21)"
    And The user confirms by clicking the Add button
    When The user sees the new "status" status "Active" for "Status2" in edit mode
    And The user clicks Save Changes button
    Then The "1st" row of the "status" list should be updated with name "Status2"
    And A snack notification 'Statuses saved successfully' confirming this has succeeded

  @e2e @status-settings
  Scenario: Status set individual visibility
    Given The user creates a default status name "Status3"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the visibility next to any status in the list and a dropdown appears
    And The user selects "Inactive" visibility
    Then The user sees the new "status" status "Inactive" for "Status3" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The affected "status" for "Status3" should have updated visibility "Inactive"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the visibility next to any status in the list and a dropdown appears
    And The user selects "Active" visibility
    Then The user sees the new "status" status "Active" for "Status3" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The affected "status" for "Status3" should have updated visibility "Active"

  @e2e @status-settings
  Scenario: Status set individual color
    Given The user creates a default status name "Status4"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user clicks the color icon next to any status in the list
    When The user selects a new color from the color scale
    And The user inputs the HEX value manually "#FF0000"
    And The user clicks away from the dialog to close
    Then The user sees the new "status" status "Active" for "Status4" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The affected statuses should have updated colors "#FF0000"

  @e2e @status-settings
  Scenario: Status change status name
    Given The user creates a default status name "Status5"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user edits the new name "UpdatedStatus" of any status in the list
    Then The user sees the new "status" status "Active" for "UpdatedStatus" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The affected statuses for "status" have updated names "UpdatedStatus"

  @e2e @status-settings
  Scenario: Status individual delete
    Given The user creates a default status name "Status6"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the delete icon next to any status in the list
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user can click Cancel to discard the edits, restoring the row to its original state
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the delete icon next to any status in the list
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The deleted row "Status6" are permanently removed from the list

  @e2e @status-settings
  Scenario: Status Bulk Set Color
    Given The user creates a default status name "Status7"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 1 statuses using checkboxes
    When The user clicks the Set Color button
    When The user selects a new color from the color scale
    And The user inputs the HEX value manually "#FF0000"
    And The user clicks away from the dialog to close
    When The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The affected statuses should have updated colors "#FF0000"

  @e2e @status-settings
  Scenario: Status Bulk Set Visibility
    Given The user creates a default status name "Status8"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 1 statuses using checkboxes
    When The user clicks the Set Visibility button
    And The user selects Inactive visibility
    When The user confirms by clicking Save Changes button
    When The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The affected "status" for "Status8" should have updated visibility "Inactive"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 2 statuses using checkboxes
    When The user clicks the Set Visibility button
    And The user selects Active visibility
    When The user confirms by clicking Save Changes button
    When The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The affected "status" for "Status8" should have updated visibility "Active"

  @e2e @status-settings
  Scenario: Status Bulk Delete
    Given The user creates a default status name "Status9"
    Given The user creates a default status name "Status10"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 1 statuses using checkboxes
    When The user clicks the Delete button
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user can click Cancel to discard the edits, restoring the row to its original state
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 2 statuses using checkboxes
    When The user clicks the Delete button
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The deleted row "Status9" are permanently removed from the list
    And The deleted row "Status10" are permanently removed from the list

  @e2e @status-settings
  Scenario: Status reorder
    When The user clicks on the Add button
    Then The user sees Add New "status" modal
    When The user inputs "status" name "StatusReorder1"
    And The user confirms by clicking the Add button
    Then The "1st" row of the "status" list should be updated with name "StatusReorder1"
    And A snack notification 'Status added successfully' confirming this has succeeded
    When The user clicks on the Add button
    Then The user sees Add New "status" modal
    When The user inputs "status" name "StatusReorder2"
    And The user confirms by clicking the Add button
    Then The "1st" row of the "status" list should be updated with name "StatusReorder2"
    And A snack notification 'Status added successfully' confirming this has succeeded
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user drags the "1st" row to "3rd" row in the list
    When The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    And The "1st" row of the "status" list should be updated with name "StatusReorder1"
    And The "3rd" row of the "status" list should be updated with name "StatusReorder2"

  @e2e @status-settings @multi-user
  Scenario: Two users editing statuses
    Given The user logins as "user1"
    And The user is on Settings screen
    When The user creates a default status name "multipleEdit"
    Then A snack notification "Status added successfully" confirming this has succeeded
    When The user changes the visibility to "Inactive"
    Then The user sees the new "status" status "Inactive" for "multipleEdit" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    Then The affected "status" for "multipleEdit" should have updated visibility "Inactive"
    When The user logins as "user2"
    And The user is on Settings screen
    Then The affected "status" for "multipleEdit" should have updated visibility "Inactive"
    When The user changes the visibility to "Active"
    Then The user sees the new "status" status "Active" for "multipleEdit" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Statuses saved successfully' confirming this has succeeded
    Then The affected "status" for "multipleEdit" should have updated visibility "Active"

  @e2e @status-settings @keyboard-event
  Scenario: Keyboard navigation with TAB and arrow keys in status table
    When The user clicks Edit button
    And The user presses "Tab" key in settings table
    And The user presses "Enter" key 1 time(s)
    Then The "1st" row is selected
    And The user presses "ArrowDown" key 2 time(s)
    And The user presses "Enter" key 1 time(s)
    Then The "1st" row is selected
    And The "3rd" row is selected