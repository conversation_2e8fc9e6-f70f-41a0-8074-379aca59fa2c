import createSvgIcon from './lib/createSvgIcon';
export const CaseDetailNullState = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="190"
    height="190"
    viewBox="0 0 190 190"
    fill="none"
  >
    <circle cx="95" cy="95" r="95" fill="url(#paint0_angular_3742_40395)" />
    <path
      d="M94.0072 142.041C122.129 142.041 144.927 119.243 144.927 91.0207C144.927 62.7986 122.028 40 94.0072 40C65.8855 40 43.0869 62.7986 43.0869 91.0207C43.0869 119.243 65.8855 142.041 94.0072 142.041Z"
      fill="#EAEEF9"
    />
    <path
      d="M151.392 74.8555C153.666 74.8555 155.51 73.0119 155.51 70.7377C155.51 68.4635 153.666 66.6199 151.392 66.6199C149.118 66.6199 147.274 68.4635 147.274 70.7377C147.274 73.0119 149.118 74.8555 151.392 74.8555Z"
      fill="#EAEEF9"
    />
    <path
      d="M157.418 58.786C158.971 58.786 160.23 57.5269 160.23 55.9738C160.23 54.4207 158.971 53.1616 157.418 53.1616C155.865 53.1616 154.605 54.4207 154.605 55.9738C154.605 57.5269 155.865 58.786 157.418 58.786Z"
      fill="#EAEEF9"
    />
    <path
      d="M45.3971 57.576C46.9502 57.576 48.2093 56.3169 48.2093 54.7638C48.2093 53.2107 46.9502 51.9517 45.3971 51.9517C43.844 51.9517 42.585 53.2107 42.585 54.7638C42.585 56.3169 43.844 57.576 45.3971 57.576Z"
      fill="#F1F3F9"
    />
    <path
      d="M28.2226 111.811C31.107 111.811 33.4452 109.473 33.4452 106.588C33.4452 103.704 31.107 101.366 28.2226 101.366C25.3382 101.366 23 103.704 23 106.588C23 109.473 25.3382 111.811 28.2226 111.811Z"
      fill="#EAEEF9"
    />
    <path
      d="M96.7 153.6C124.7 153.6 147.4 130.9 147.4 102.8C147.4 74.7 124.6 52 96.7 52C68.7 52 46 74.7 46 102.8C46 130.9 68.7 153.6 96.7 153.6Z"
      fill="#EAEEF9"
    />
    <path
      d="M143.7 69.5999C145.964 69.5999 147.8 67.7643 147.8 65.4999C147.8 63.2355 145.964 61.3999 143.7 61.3999C141.435 61.3999 139.6 63.2355 139.6 65.4999C139.6 67.7643 141.435 69.5999 143.7 69.5999Z"
      fill="#F1F3F9"
    />
    <path
      d="M149.7 53.6C151.247 53.6 152.5 52.3464 152.5 50.8C152.5 49.2536 151.247 48 149.7 48C148.154 48 146.9 49.2536 146.9 50.8C146.9 52.3464 148.154 53.6 149.7 53.6Z"
      fill="#EAEEF9"
    />
    <path
      d="M41.8 76.6C43.3464 76.6 44.6 75.3464 44.6 73.8C44.6 72.2536 43.3464 71 41.8 71C40.2536 71 39 72.2536 39 73.8C39 75.3464 40.2536 76.6 41.8 76.6Z"
      fill="#EAEEF9"
    />
    <path
      d="M57.2 154.4C60.0719 154.4 62.4 152.072 62.4 149.2C62.4 146.328 60.0719 144 57.2 144C54.3281 144 52 146.328 52 149.2C52 152.072 54.3281 154.4 57.2 154.4Z"
      fill="#EAEEF9"
    />
    <g filter="url(#filter0_d_3742_40395)">
      <path
        d="M160 116.405C160 129.326 149.55 139.775 136.517 139.775C136.292 139.775 134.382 139.775 120.337 139.775C110.562 139.775 94.9438 139.775 70 139.775H58.0899C42.4719 140.112 30 127.641 30 112.584C30 97.4159 42.5843 84.8316 58.4269 85.6181C72.0224 43.1463 134.27 49.1013 139.55 93.0338C151.236 94.4945 160 104.382 160 116.405Z"
        fill="url(#paint1_linear_3742_40395)"
      />
    </g>
    <path
      d="M136.517 139.775C149.439 139.775 160 129.326 160 116.405C160 103.483 149.439 93.0339 136.517 93.0339C123.596 93.0339 113.034 103.483 113.034 116.405C113.034 129.326 123.596 139.775 136.517 139.775Z"
      fill="url(#paint2_linear_3742_40395)"
    />
    <path
      d="M98.202 140C121.236 140 140 121.348 140 98.3147C140 75.2811 121.236 56.6294 98.202 56.6294C75.1683 56.6294 56.4043 75.2811 56.4043 98.3147C56.4043 121.348 75.056 140 98.202 140Z"
      fill="url(#paint3_linear_3742_40395)"
    />
    <path
      d="M86.6176 99.5102L88.5724 97.551C89.1425 96.9796 89.1425 96 88.5724 95.4286C88.0023 94.8571 87.0249 94.8571 86.4548 95.4286L84.5 97.3878L82.5452 95.4286C81.9751 94.8571 80.9977 94.8571 80.4276 95.4286C79.8575 96 79.8575 96.9796 80.4276 97.551L82.3824 99.5102L80.4276 101.469C79.8575 102.041 79.8575 103.02 80.4276 103.592C80.7534 103.918 81.0792 104 81.4864 104C81.8937 104 82.2195 103.837 82.5452 103.592L84.5 101.633L86.4548 103.592C86.7805 103.918 87.1063 104 87.5136 104C87.9208 104 88.2466 103.837 88.5724 103.592C89.1425 103.02 89.1425 102.041 88.5724 101.469L86.6176 99.5102Z"
      fill="#989FB0"
    />
    <path
      d="M107.353 99.5102L109.525 97.551C110.158 96.9796 110.158 96 109.525 95.4286C108.891 94.8571 107.805 94.8571 107.172 95.4286L105 97.3878L102.828 95.4286C102.195 94.8571 101.109 94.8571 100.475 95.4286C99.8416 96 99.8416 96.9796 100.475 97.551L102.647 99.5102L100.475 101.469C99.8416 102.041 99.8416 103.02 100.475 103.592C100.837 103.918 101.199 104 101.652 104C102.104 104 102.466 103.837 102.828 103.592L105 101.633L107.172 103.592C107.534 103.918 107.896 104 108.348 104C108.801 104 109.163 103.837 109.525 103.592C110.158 103.02 110.158 102.041 109.525 101.469L107.353 99.5102Z"
      fill="#989FB0"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M50.7363 60.0521C50.9707 59.13 51.9082 58.5725 52.8303 58.8068L59.3505 60.4642C60.2726 60.6986 60.8301 61.6361 60.5957 62.5582C60.3614 63.4804 59.4238 64.0379 58.5017 63.8035L51.9815 62.1461C51.0594 61.9117 50.5019 60.9742 50.7363 60.0521Z"
      fill="#989FB0"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M56.2762 41.7153C57.0634 41.1809 58.1347 41.3859 58.6691 42.1731L66.4635 53.6561C66.9978 54.4434 66.7929 55.5147 66.0056 56.0491C65.2184 56.5834 64.1471 56.3784 63.6127 55.5912L55.8183 44.1082C55.284 43.321 55.4889 42.2496 56.2762 41.7153Z"
      fill="#989FB0"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M70.7351 36.5363C71.6684 36.3515 72.5748 36.9584 72.7595 37.8917L75.4543 51.5061C75.639 52.4394 75.0322 53.3458 74.0988 53.5306C73.1655 53.7153 72.2591 53.1084 72.0744 52.1751L69.3796 38.5607C69.1949 37.6274 69.8018 36.721 70.7351 36.5363Z"
      fill="#989FB0"
    />
    <rect x="84" y="113" width="23" height="3" fill="#989FB0" />
    <path
      d="M98 114H102V119C102 120.105 101.105 121 100 121C98.8954 121 98 120.105 98 119V114Z"
      fill="#989FB0"
    />
    <path
      d="M150.594 70.0647C150.168 71.8614 149.528 73.5889 148.746 75.1783C146.686 78.9098 143.63 81.9503 139.793 83.8161C135.813 85.82 131.123 86.511 126.362 85.6127C115.276 83.3323 108.099 72.8288 110.444 61.9798C112.789 51.1308 123.52 44.1515 134.676 46.4318C138.656 47.2611 142.138 49.1268 145.051 51.8218C149.883 56.6589 151.944 63.5 150.594 70.0647Z"
      fill="url(#paint4_linear_3742_40395)"
    />
    <path
      d="M127.508 56.1442L127.508 56.1442L127.509 56.1522L128.321 66.2291C128.39 67.4288 129.315 68.5 130.5 68.5C131.685 68.5 132.61 67.4288 132.679 66.2291L133.491 56.1522L133.491 56.1522L133.492 56.1431C133.614 54.1862 132.32 52.5 130.5 52.5C128.674 52.5 127.386 54.2551 127.508 56.1442Z"
      fill="white"
      stroke="white"
    />
    <path
      d="M130.5 72.5C128.835 72.5 127.5 73.835 127.5 75.5C127.5 77.165 128.835 78.5 130.5 78.5C132.165 78.5 133.5 77.165 133.5 75.5C133.5 73.835 132.165 72.5 130.5 72.5Z"
      fill="white"
      stroke="white"
    />
    <defs>
      <filter
        id="filter0_d_3742_40395"
        x="8"
        y="45.731"
        width="174"
        height="127.051"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="11" />
        <feGaussianBlur stdDeviation="11" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_3742_40395"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_3742_40395"
          result="shape"
        />
      </filter>
      <radialGradient
        id="paint0_angular_3742_40395"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(95 65) rotate(90) scale(125 72.2749)"
      >
        <stop stopColor="#FDFEFF" stopOpacity="0.55" />
        <stop offset="0.5" stopColor="#F3F7FF" />
      </radialGradient>
      <linearGradient
        id="paint1_linear_3742_40395"
        x1="94.9999"
        y1="91.0509"
        x2="94.9576"
        y2="140.677"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="white" />
        <stop offset="0.9964" stopColor="#ECF0F5" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_3742_40395"
        x1="110.535"
        y1="88.0023"
        x2="131.353"
        y2="110.77"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#BCCBE1" />
        <stop offset="0.9942" stopColor="white" stopOpacity="0" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_3742_40395"
        x1="52.4904"
        y1="79.1864"
        x2="85.3007"
        y2="92.0262"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#E2E8F0" />
        <stop offset="0.9942" stopColor="white" stopOpacity="0" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_3742_40395"
        x1="109.981"
        y1="66.0043"
        x2="151.007"
        y2="66.0043"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#B0BACC" />
        <stop offset="1" stopColor="#969EAE" />
      </linearGradient>
    </defs>
  </svg>,
  'CaseDetailNullState'
);
