import { CaseTag } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function saveTagList({
  tags,
  sdoId,
  tagSchemaId,
  userId,
  gql,
}: {
  tags: CaseTag[];
  sdoId?: string;
  tagSchemaId: string;
  userId: string;
  gql: GQLApi;
}) {
  if (sdoId && sdoId !== '') {
    const latestTagSdo = await gql.getLatestSDO(tagSchemaId);
    if (sdoId !== latestTagSdo.id) {
      console.warn('On saving tags, the tags have been modified.');
      throw new Error(`concurrentModificationError_${latestTagSdo.id}`);
    }
  }

  const data = {
    tags,
    createdBy: userId,
  };

  const newSdoId = await gql.createSDO({
    schemaId: tagSchemaId,
    data,
  });

  return newSdoId;
}
