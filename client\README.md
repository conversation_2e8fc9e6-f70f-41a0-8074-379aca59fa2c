# Investigate Client

[![Lines of Code](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=ncloc&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Maintainability Rating](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=sqale_rating&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Reliability Rating](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=reliability_rating&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Security Rating](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=security_rating&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=alert_status&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Technical Debt](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=sqale_index&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Vulnerabilities](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=vulnerabilities&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=coverage&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=code_smells&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=duplicated_lines_density&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=aiware-apps&metric=bugs&token=7d766c19032706ddb69217fbc9dd466846d8a39f)](https://sonarcloud.io/dashboard?id=aiware-apps)
[![](https://data.jsdelivr.com/v1/package/npm/aiware-js/badge)](https://www.jsdelivr.com/package/npm/aiware-js)

AI that Helps Your People Solve Investigations Faster With A Central Evidence Hub

### How to start:
Make sure `./config.json` has the correct configuration before running.
```
yarn 
yarn start:dev
```

### How to check eslint:
```
yarn lint
yarn lint:fix // Attempts to fix eslint issues
```

### How to test:
```
yarn test // Automatically runs in watch mode
yarn test:covereage // To get coverage report
```

### How to run End-to-End tests:
Create a file called cypress.env.json in the root of the project with the following content:
```
{
  "username": "your-username",
  "password": "your-password"
}
```

#### Local
Investigate should be running locally on port 4200
```
yarn cypress:runLocal
```
You can run e2e tests interactively by running:
```
yarn cypress:openLocal
```

#### Against stage.us-1
```
yarn cypress:runStage
```

### How to format code:
```
yarn format
```

### How to check for outdated dependencies:
```
yarn outdated
```

### How to check for unused or undefined dependencies:
```
yarn depcheck
```

© Veritone, 2025
