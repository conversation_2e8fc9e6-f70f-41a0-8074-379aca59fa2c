.grouped-list-header {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--background-secondary);
  margin-bottom: 11px;

  &.has-scrollbar {
    padding-right: 10px;
  }

  table {
    table-layout: fixed;
    overflow: hidden;
  }

  thead {
    box-shadow: none;
  }

  .Sdk-MuiTableCell-root {
    border: none;
  }

  .Sdk-MuiTableCell-root:first-child {
    width: 100px;
    padding: 10px 0 10px 16px;
  }

  .Sdk-MuiTableCell-root:nth-child(2) {
    padding-left: 56px;
  }

  .grouped-list-header__actions-wrapper {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .grouped-list-header__checkbox-label {
    display: flex;
    align-items: center;
    color: var(--text-primary);
    min-width: fit-content;
    padding: 6px;
    border-radius: 4px;

    &.empty-result-disabled {
      opacity: 0.6;
    }
  }
}
