import { ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';
import { BasicUserInfo, OwnerInfo } from '@shared-types/types';
import HttpClient from '@store/dependencies/httpClient';
import { GQLApi } from '@utils/helpers';
import { setUserInfo } from '@store/modules/caseManager/slice';

export const getOwner = async ({
  gql,
  dispatch,
  data,
}: {
  data: OwnerInfo;
  userInfo?: Record<string, BasicUserInfo['basicUserInfo']>;
  gql: GQLApi;
  dispatch: ThunkDispatch<unknown, { http: HttpClient }, UnknownAction>;
}) => {
  // Get casOwner priority: userName from store - email from store - userName from api -
  // email from api - createdByName from caseDetail - userId from caseDetail - N/A
  const { createdBy: userId, createdByName: userName } = data;

  const basicUser = await gql.getBasicUserInfo({ id: userId });
  if (basicUser) {
    dispatch(setUserInfo(basicUser));
    return getNameOrEmail(basicUser);
  }

  return userName || `User: ${userId.slice(0, 8)} ... ${userId.slice(-6)}`;
};

export const getNameOrEmail = (user: BasicUserInfo['basicUserInfo']) => {
  const fullName = `${user.firstName} ${user.lastName}`.trim();
  return fullName || user.email;
};
