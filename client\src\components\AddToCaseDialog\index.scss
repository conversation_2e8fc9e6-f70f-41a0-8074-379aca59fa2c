.add-to-case-dialog {
  .Sdk-MuiPaper-root {
    padding: 30px;
    display: grid;
    gap: 30px;
    max-width: 420px;
    max-height: 290px;
    border-radius: 10px;
    margin: 0;
    overflow: hidden;
  }

  &__header {
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__title {
    font-weight: bold;
    font-size: 18px;
  }

  &__sub_title {
    font-weight: 400;
    font-size: 16px;
  }

  &__menu-item {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .Sdk-MuiDialogContent-root {
    .loader-wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .Sdk-MuiListItemButton-root {
      display: flex;
      gap: 24px;
      font-size: 16px;
      font-weight: 400;
      text-overflow: ellipsis;
    }

    .Sdk-MuiInputBase-input {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .Sdk-MuiSvgIcon-root {
      path {
        fill: var(--text-primary);
      }
    }

    width: 100%;
    padding: 0;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .Sdk-MuiDialogActions-root {
    height: 40px;
    gap: 5px;
    display: flex;
    justify-content: flex-end;
    padding: 0;
    font-size: 14px;
    font-weight: bold;

    .cancel-button {
      color: var(--text-primary);
      padding: 10px 25px;
    }

    .confirm-button {
      padding: 10px 25px;
      border-radius: 3px;
      color: white;
      margin: 0;
      background: var(--button-dark-blue);
    }

    .Mui-disabled {
      color: var(--button-text-disabled);
      background-color: var(--button-background-disabled);
    }
  }
}
