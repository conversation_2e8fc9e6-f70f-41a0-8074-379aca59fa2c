import CustomNotification from '@components/CustomSnackbar';
import {
  SnackbarProvider as NotiSnackbarProvider,
  SnackbarProviderProps,
} from 'notistack';
import { PropsWithChildren } from 'react';

// TODO: Add i18n
// TODO: Customize snackbar with subtitle
const SnackbarProvider = ({
  children,
  ...props
}: PropsWithChildren & SnackbarProviderProps) => (
  <NotiSnackbarProvider
    Components={{ customSnackbar: CustomNotification }}
    {...props}
  >
    {children}
  </NotiSnackbarProvider>
);

export default SnackbarProvider;
