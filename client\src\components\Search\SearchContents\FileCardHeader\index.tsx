import './index.scss';
import { Blur, ExpandMore } from '@assets/icons';
import { BlurSwitch } from '@components/Search/Switch';
import { I18nTranslate } from '@i18n';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { Checkbox, IconButton } from '@mui/material';
import { selectCategories } from '@store/modules/search/slice';
import { SearchView } from '@utils/local-storage';
import cn from 'classnames';
import { noop } from 'lodash';
import { useSelector } from 'react-redux';

const FileCardHeader = ({
  hasExpanseCol = false,
  isCheckAll,
  isIndeterminate,
  isEmptyResult,
  onExpandAll = noop,
  onCheckAll,
  onBlurSwitch,
  onSetMenuAnchorEl = noop,
  blur,
  searchView,
}: Props) => {
  const intl = I18nTranslate.Intl();
  const categories = useSelector(selectCategories);
  const isExpandedAll = categories.every(({ expanded }) => expanded);

  return (
    <div
      className={cn('file-card-header', {
        'grouped-view-header': !searchView
          ? SearchView.Grouped
          : searchView === SearchView.Grouped,
      })}
    >
      <div className="file-card-header__actions-wrapper">
        {hasExpanseCol && (
          <ExpandMore
            cursor="pointer"
            onClick={() => onExpandAll(!isExpandedAll)}
            style={{
              transform: isExpandedAll ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
            }}
          />
        )}
        <IconButton
          className={cn('file-card-header__checkbox-label', {
            'empty-result-disabled': isEmptyResult,
          })}
          onClick={(e) => onSetMenuAnchorEl(e.currentTarget)}
          disabled={isEmptyResult}
        >
          <Checkbox
            checked={isCheckAll}
            indeterminate={isIndeterminate}
            onClick={(e) => e.stopPropagation()}
            onChange={(e) => onCheckAll(e.target.checked)}
          />
          <ArrowDropDownIcon />
        </IconButton>
      </div>
      <div className="file-card-header__blur-images-wrapper">
        <span className="file-card-header__blur-label">
          {intl.formatMessage({ id: 'blurImages' })}
        </span>
        <BlurSwitch
          checked={blur}
          icon={<Blur />}
          checkedIcon={<Blur />}
          onChange={onBlurSwitch}
          data-testid="blur-switch"
        />
      </div>
    </div>
  );
};

interface Props {
  blur: boolean;
  searchView?: SearchView;
  hasExpanseCol?: boolean;
  isCheckAll: boolean;
  isIndeterminate: boolean;
  isEmptyResult: boolean;
  onBlurSwitch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onExpandAll?: (isExpanded: boolean) => void;
  onCheckAll: (isChecked: boolean) => void;
  onSetMenuAnchorEl?: (anchorEl: null | HTMLElement) => void;
}

export default FileCardHeader;
