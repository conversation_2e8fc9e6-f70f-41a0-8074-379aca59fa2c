import createSvgIcon from './lib/createSvgIcon';

export const Applications = createSvgIcon(
  <svg
    width="24"
    height="24"
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="20" cy="20" r="20" fill="#F2F7FE" />
    <path
      d="M13.6153 27.2307C13.1711 27.2307 12.7908 27.0725 12.4745 26.7562C12.1582 26.4399 12 26.0596 12 25.6154C12 25.1711 12.1582 24.7909 12.4745 24.4745C12.7908 24.1582 13.1711 24 13.6153 24C14.0596 24 14.4399 24.1582 14.7562 24.4745C15.0725 24.7909 15.2307 25.1711 15.2307 25.6154C15.2307 26.0596 15.0725 26.4399 14.7562 26.7562C14.4399 27.0725 14.0596 27.2307 13.6153 27.2307ZM19.6154 27.2307C19.1711 27.2307 18.7908 27.0725 18.4745 26.7562C18.1582 26.4399 18 26.0596 18 25.6154C18 25.1711 18.1582 24.7909 18.4745 24.4745C18.7908 24.1582 19.1711 24 19.6154 24C20.0596 24 20.4398 24.1582 20.7562 24.4745C21.0725 24.7909 21.2307 25.1711 21.2307 25.6154C21.2307 26.0596 21.0725 26.4399 20.7562 26.7562C20.4398 27.0725 20.0596 27.2307 19.6154 27.2307ZM25.6154 27.2307C25.1711 27.2307 24.7909 27.0725 24.4745 26.7562C24.1582 26.4399 24 26.0596 24 25.6154C24 25.1711 24.1582 24.7909 24.4745 24.4745C24.7909 24.1582 25.1711 24 25.6154 24C26.0596 24 26.4398 24.1582 26.7562 24.4745C27.0725 24.7909 27.2307 25.1711 27.2307 25.6154C27.2307 26.0596 27.0725 26.4399 26.7562 26.7562C26.4398 27.0725 26.0596 27.2307 25.6154 27.2307ZM13.6153 21.2307C13.1711 21.2307 12.7908 21.0725 12.4745 20.7562C12.1582 20.4399 12 20.0596 12 19.6154C12 19.1711 12.1582 18.7909 12.4745 18.4745C12.7908 18.1582 13.1711 18 13.6153 18C14.0596 18 14.4399 18.1582 14.7562 18.4745C15.0725 18.7909 15.2307 19.1711 15.2307 19.6154C15.2307 20.0596 15.0725 20.4399 14.7562 20.7562C14.4399 21.0725 14.0596 21.2307 13.6153 21.2307ZM19.6154 21.2307C19.1711 21.2307 18.7908 21.0725 18.4745 20.7562C18.1582 20.4399 18 20.0596 18 19.6154C18 19.1711 18.1582 18.7909 18.4745 18.4745C18.7908 18.1582 19.1711 18 19.6154 18C20.0596 18 20.4398 18.1582 20.7562 18.4745C21.0725 18.7909 21.2307 19.1711 21.2307 19.6154C21.2307 20.0596 21.0725 20.4399 20.7562 20.7562C20.4398 21.0725 20.0596 21.2307 19.6154 21.2307ZM25.6154 21.2307C25.1711 21.2307 24.7909 21.0725 24.4745 20.7562C24.1582 20.4399 24 20.0596 24 19.6154C24 19.1711 24.1582 18.7909 24.4745 18.4745C24.7909 18.1582 25.1711 18 25.6154 18C26.0596 18 26.4398 18.1582 26.7562 18.4745C27.0725 18.7909 27.2307 19.1711 27.2307 19.6154C27.2307 20.0596 27.0725 20.4399 26.7562 20.7562C26.4398 21.0725 26.0596 21.2307 25.6154 21.2307ZM13.6153 15.2307C13.1711 15.2307 12.7908 15.0725 12.4745 14.7562C12.1582 14.4398 12 14.0596 12 13.6154C12 13.1711 12.1582 12.7909 12.4745 12.4745C12.7908 12.1582 13.1711 12 13.6153 12C14.0596 12 14.4399 12.1582 14.7562 12.4745C15.0725 12.7909 15.2307 13.1711 15.2307 13.6154C15.2307 14.0596 15.0725 14.4398 14.7562 14.7562C14.4399 15.0725 14.0596 15.2307 13.6153 15.2307ZM19.6154 15.2307C19.1711 15.2307 18.7908 15.0725 18.4745 14.7562C18.1582 14.4398 18 14.0596 18 13.6154C18 13.1711 18.1582 12.7909 18.4745 12.4745C18.7908 12.1582 19.1711 12 19.6154 12C20.0596 12 20.4398 12.1582 20.7562 12.4745C21.0725 12.7909 21.2307 13.1711 21.2307 13.6154C21.2307 14.0596 21.0725 14.4398 20.7562 14.7562C20.4398 15.0725 20.0596 15.2307 19.6154 15.2307ZM25.6154 15.2307C25.1711 15.2307 24.7909 15.0725 24.4745 14.7562C24.1582 14.4398 24 14.0596 24 13.6154C24 13.1711 24.1582 12.7909 24.4745 12.4745C24.7909 12.1582 25.1711 12 25.6154 12C26.0596 12 26.4398 12.1582 26.7562 12.4745C27.0725 12.7909 27.2307 13.1711 27.2307 13.6154C27.2307 14.0596 27.0725 14.4398 26.7562 14.7562C26.4398 15.0725 26.0596 15.2307 25.6154 15.2307Z"
      fill="#0D47A1"
    />
  </svg>,
  'Applications'
);
