import './index.scss';
import { ExpandMore } from '@assets/icons';
import {
  OrderBy,
  SearchResult,
} from '@components/Search/SearchContents/SearchTable';
import { Column } from '@components/Table';
import SortLabel from '@components/Table/SortLabel';
import { I18nTranslate } from '@i18n';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import {
  Checkbox,
  IconButton,
  Table,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';
import { FILES_SORT_FIELD } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  selectCategories,
  selectSort,
  setSortBy,
  setSortDirection,
} from '@store/modules/search/slice';
import cn from 'classnames';
import { useSelector } from 'react-redux';

const GroupedListHeader = ({
  isCheckAll,
  isIndeterminate,
  isEmptyResult,
  onExpandAll,
  onCheckAll,
  onSetMenuAnchorEl,
  hasScrollBar = false,
}: Props) => {
  const intl = I18nTranslate.Intl();
  const categories = useSelector(selectCategories);
  const filesSort = useSelector(selectSort);
  const isExpandedAll = categories.every(({ expanded }) => expanded);

  const isValidOrderBy = (value: string): value is OrderBy =>
    Object.values(OrderBy).includes(value as OrderBy);
  const dispatch = useAppDispatch();

  const handleSort = (key: string) => {
    if (filesSort.type === key) {
      dispatch(setSortDirection(filesSort.order === 'asc' ? 'desc' : 'asc'));
    } else if (isValidOrderBy(key) && filesSort.type !== key) {
      dispatch(setSortBy(key as FILES_SORT_FIELD));
    }
  };

  const columns: Column<SearchResult>[] = [
    {
      header: intl.formatMessage({ id: 'fileName' }),
      field: 'fileName',
      isSortable: true,
      width: '35%',
    },
    {
      header: intl.formatMessage({ id: 'caseId' }),
      field: 'caseId',
      isSortable: false,
      width: '16%',
    },
    {
      header: intl.formatMessage({ id: 'dateUploaded' }),
      field: 'createdTime',
      isSortable: true,
      width: '16%',
    },
    // Leave this commented section here.  Retention Date will be part of future phase.
    // {
    //   header: intl.formatMessage({ id: 'retentionDate' }),
    //   isSortable: true,
    //   width: '14%',
    // },
    {
      header: intl.formatMessage({ id: 'description' }),
      field: 'description',
      width: '20%',
      isSortable: false,
    },
  ];
  return (
    <div
      className={cn('grouped-list-header', { 'has-scrollbar': hasScrollBar })}
    >
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>
              <div className="grouped-list-header__actions-wrapper">
                <ExpandMore
                  cursor="pointer"
                  onClick={() => onExpandAll(!isExpandedAll)}
                  style={{
                    transform: isExpandedAll
                      ? 'rotate(180deg)'
                      : 'rotate(0deg)',
                    transition:
                      'transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
                  }}
                />
                <IconButton
                  className={cn('grouped-list-header__checkbox-label', {
                    'empty-result-disabled': isEmptyResult,
                  })}
                  onClick={(e) => onSetMenuAnchorEl(e.currentTarget)}
                  disabled={isEmptyResult}
                >
                  <Checkbox
                    checked={isCheckAll}
                    indeterminate={isIndeterminate}
                    onClick={(e) => e.stopPropagation()}
                    onChange={(e) => onCheckAll(e.target.checked)}
                    data-testid="grouped-list-header__checkbox"
                  />
                  <ArrowDropDownIcon />
                </IconButton>
              </div>
            </TableCell>
            {columns.map(({ field, header, isSortable, width }) => (
              <SortLabel
                direction={filesSort.order}
                orderBy={filesSort.type}
                field={String(field)}
                header={header}
                handleSort={handleSort}
                isSortable={isSortable}
                key={String(field)}
                id={String(field)}
                width={width}
              />
            ))}
            <TableCell />
          </TableRow>
        </TableHead>
      </Table>
    </div>
  );
};

interface Props {
  isCheckAll: boolean;
  isIndeterminate: boolean;
  isEmptyResult: boolean;
  onExpandAll: (isExpanded: boolean) => void;
  onCheckAll: (isChecked: boolean) => void;
  onSetMenuAnchorEl: (anchorEl: null | HTMLElement) => void;
  hasScrollBar?: boolean;
}

export default GroupedListHeader;
