import './index.scss';
import cn from 'classnames';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useFormContext } from 'react-hook-form';
import MetadataInput from './Input';
import MetadataSelect from './Select';

export enum ValueType {
  Input = 'input',
  Textarea = 'textarea',
  ViewOnly = 'viewOnly',
  Dropdown = 'dropdown',
  Truncate = 'truncate',
}

interface Props {
  label: string;
  name: string;
  value?: string | number;
  valueType: ValueType;
}

dayjs.extend(utc);

const MetadataField = ({ label, name, value, valueType }: Props) => {
  const {
    formState: { errors },
  } = useFormContext();

  const isError = valueType === ValueType.Input && name in errors;
  const renderValue = () => {
    if (name === 'uploadDate') {
      return dayjs(value).format('MM/DD/YYYY');
    }
    // calculate duration based on seconds
    if (name === 'duration' && typeof value === 'number') {
      return dayjs.utc(value * 1000).format('HH:mm:ss');
    }
    if (name === 'fileSize' && typeof value === 'number') {
      const fileSizeInMB = value / 1000000;
      return `${fileSizeInMB.toFixed(2)} MB`;
    }
    if (!value) {
      return '--';
    }
    return value;
  };

  return (
    <div
      key={label}
      className={cn('metadata-field', {
        'metadata-text-area': valueType === ValueType.Textarea,
        'metadata-err-field': isError,
      })}
    >
      <label
        className={cn('metadata-label', {
          'text-area-label': valueType === ValueType.Textarea,
          'err-label': isError,
        })}
      >
        {label}
      </label>
      {(valueType === ValueType.Input || valueType === ValueType.Textarea) && (
        <div className="metadata-content">
          <MetadataInput name={name} valueType={valueType} />
        </div>
      )}
      {valueType === ValueType.ViewOnly && (
        <div className="metadata-content text-only">{renderValue()}</div>
      )}
      {valueType === ValueType.Truncate && (
        <div className="metadata-content truncate">{renderValue()}</div>
      )}
      {valueType === ValueType.Dropdown && (
        <div className="metadata-content">
          <MetadataSelect name={name} />
        </div>
      )}
    </div>
  );
};

export default MetadataField;
