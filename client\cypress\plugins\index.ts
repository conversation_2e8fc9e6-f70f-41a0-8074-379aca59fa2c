// This file is used to load the environment specific configuration
import { envData } from '../fixtures/envData';

interface EnvironmentConfig {
  ENVIRONMENT?: string;
  apiRoot?: string;
  visitUrl?: string;
}

interface InputConfig {
  env: EnvironmentConfig;
  baseUrl: string;
}

export const addEnvToConfig = (
  _on: Cypress.PluginEvents,
  config: InputConfig
) => {
  const env = config.env.ENVIRONMENT;
  if (env && envData[env]) {
    config.baseUrl = envData[env].baseUrl;
    config.env.apiRoot = envData[env].apiRoot;
    config.env.visitUrl = envData[env].baseUrl;
  } else {
    if (!config.env.apiRoot) {
      // use config.json
      config.env.apiRoot = 'https://api.stage.us-1.veritone.com';
      config.env.visitUrl = 'https://local.veritone.com:4200';
    }
  }
  return config;
};
