<svg width="190" height="190" viewBox="0 0 190 190" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="95" cy="95" r="95" fill="url(#paint0_angular_65265_23195)"/>
<path d="M93.217 154.151C125.778 154.151 152.176 127.753 152.176 95.0755C152.176 62.3979 125.662 36 93.217 36C60.6557 36 34.2578 62.3979 34.2578 95.0755C34.2578 127.753 60.6557 154.151 93.217 154.151Z" fill="#EAEEF9"/>
<path d="M159.661 76.3581C162.294 76.3581 164.429 74.2234 164.429 71.5902C164.429 68.9569 162.294 66.8223 159.661 66.8223C157.028 66.8223 154.893 68.9569 154.893 71.5902C154.893 74.2234 157.028 76.3581 159.661 76.3581Z" fill="#EAEEF9"/>
<path d="M166.638 57.7515C168.437 57.7515 169.895 56.2937 169.895 54.4954C169.895 52.6971 168.437 51.2393 166.638 51.2393C164.84 51.2393 163.382 52.6971 163.382 54.4954C163.382 56.2937 164.84 57.7515 166.638 57.7515Z" fill="#EAEEF9"/>
<path d="M36.9329 56.3506C38.7312 56.3506 40.189 54.8928 40.189 53.0945C40.189 51.2962 38.7312 49.8384 36.9329 49.8384C35.1346 49.8384 33.6768 51.2962 33.6768 53.0945C33.6768 54.8928 35.1346 56.3506 36.9329 56.3506Z" fill="#EAEEF9"/>
<path d="M17.0471 119.148C20.3868 119.148 23.0942 116.441 23.0942 113.101C23.0942 109.761 20.3868 107.054 17.0471 107.054C13.7074 107.054 11 109.761 11 113.101C11 116.441 13.7074 119.148 17.0471 119.148Z" fill="#EAEEF9"/>
<g filter="url(#filter0_d_65265_23195)">
<path d="M141.215 150.858H42.5791C40.6577 150.858 39.0811 149.282 39.0811 147.36V123.268C39.0811 121.346 40.6577 119.77 42.5791 119.77H141.215C143.137 119.77 144.713 121.346 144.713 123.268V147.36C144.713 149.282 143.137 150.858 141.215 150.858Z" fill="url(#paint1_linear_65265_23195)"/>
</g>
<path d="M124.858 131.446H69.9232C68.7408 131.446 67.8047 130.728 67.8047 129.82C67.8047 128.913 68.7408 128.194 69.9232 128.194H124.858C126.04 128.194 126.977 128.913 126.977 129.82C126.977 130.728 126.04 131.446 124.858 131.446Z" fill="#D6DCE8"/>
<path d="M105.15 141.349H69.9232C68.7408 141.349 67.8047 140.631 67.8047 139.724C67.8047 138.816 68.7408 138.098 69.9232 138.098H105.15C106.333 138.098 107.269 138.816 107.269 139.724C107.269 140.631 106.333 141.349 105.15 141.349Z" fill="#D6DCE8"/>
<path d="M53.6649 144.059C58.5083 144.059 62.4347 140.133 62.4347 135.289C62.4347 130.446 58.5083 126.52 53.6649 126.52C48.8214 126.52 44.895 130.446 44.895 135.289C44.895 140.133 48.8214 144.059 53.6649 144.059Z" fill="#989FB0"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M57.1281 133.29L51.61 138.759L50.2021 137.338L55.7203 131.869L57.1281 133.29Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M51.61 131.869L57.1281 137.338L55.7203 138.759L50.2021 133.29L51.61 131.869Z" fill="white"/>
<g filter="url(#filter1_d_65265_23195)">
<path d="M144.53 71.5091H45.8936C43.9721 71.5091 42.3955 69.9325 42.3955 68.011V43.9185C42.3955 41.997 43.9721 40.4204 45.8936 40.4204H144.53C146.451 40.4204 148.028 41.997 148.028 43.9185V68.011C148.028 69.9325 146.451 71.5091 144.53 71.5091Z" fill="url(#paint2_linear_65265_23195)"/>
</g>
<path d="M128.172 52.097H73.2377C72.0552 52.097 71.1191 51.3785 71.1191 50.4711C71.1191 49.5636 72.0552 48.8452 73.2377 48.8452H128.172C129.355 48.8452 130.291 49.5636 130.291 50.4711C130.291 51.3785 129.355 52.097 128.172 52.097Z" fill="#D6DCE8"/>
<path d="M108.465 62.0003H73.2377C72.0552 62.0003 71.1191 61.2819 71.1191 60.3744C71.1191 59.4669 72.0552 58.7485 73.2377 58.7485H108.465C109.647 58.7485 110.583 59.4669 110.583 60.3744C110.583 61.2819 109.647 62.0003 108.465 62.0003Z" fill="#D6DCE8"/>
<path d="M56.9788 64.7096C61.8223 64.7096 65.7487 60.7832 65.7487 55.9398C65.7487 51.0963 61.8223 47.1699 56.9788 47.1699C52.1354 47.1699 48.209 51.0963 48.209 55.9398C48.209 60.7832 52.1354 64.7096 56.9788 64.7096Z" fill="#989FB0"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M60.4421 53.9401L54.924 59.4089L53.5161 57.9884L59.0342 52.5195L60.4421 53.9401Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M54.924 52.5195L60.4421 57.9884L59.0342 59.4089L53.5161 53.9401L54.924 52.5195Z" fill="white"/>
<g filter="url(#filter2_d_65265_23195)">
<path d="M129.391 110.997H30.7056C28.7841 110.997 27.2075 109.42 27.2075 107.499V83.4063C27.2075 81.4848 28.7841 79.9082 30.7056 79.9082H129.342C131.263 79.9082 132.84 81.4848 132.84 83.4063V107.499C132.889 109.47 131.313 110.997 129.391 110.997Z" fill="url(#paint3_linear_65265_23195)"/>
</g>
<path d="M41.8407 104.247C46.6841 104.247 50.6105 100.321 50.6105 95.4774C50.6105 90.6339 46.6841 86.7075 41.8407 86.7075C36.9972 86.7075 33.0708 90.6339 33.0708 95.4774C33.0708 100.321 36.9972 104.247 41.8407 104.247Z" fill="#989FB0"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46.686 93.4254L40.4608 99.6507L36.9946 96.1845L38.4088 94.7703L40.4608 96.8222L45.2718 92.0112L46.686 93.4254Z" fill="white"/>
<path d="M113.034 91.5852H58.099C56.9166 91.5852 55.9805 90.8668 55.9805 89.9594C55.9805 89.0519 56.9166 88.3335 58.099 88.3335H113.034C114.216 88.3335 115.152 89.0519 115.152 89.9594C115.152 90.8668 114.167 91.5852 113.034 91.5852Z" fill="#D6DCE8"/>
<path d="M93.3263 101.537H58.099C56.9166 101.537 55.9805 100.819 55.9805 99.9115C55.9805 99.0041 56.9166 98.2856 58.099 98.2856H93.3263C94.5087 98.2856 95.4448 99.0041 95.4448 99.9115C95.3956 100.819 94.4594 101.537 93.3263 101.537Z" fill="#D6DCE8"/>
<defs>
<filter id="filter0_d_65265_23195" x="17.0811" y="108.77" width="149.632" height="75.0889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="11"/>
<feGaussianBlur stdDeviation="11"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_65265_23195"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_65265_23195" result="shape"/>
</filter>
<filter id="filter1_d_65265_23195" x="20.3955" y="29.4204" width="149.632" height="75.0889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="11"/>
<feGaussianBlur stdDeviation="11"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_65265_23195"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_65265_23195" result="shape"/>
</filter>
<filter id="filter2_d_65265_23195" x="5.20752" y="68.9082" width="149.634" height="75.0889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="11"/>
<feGaussianBlur stdDeviation="11"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.397708 0 0 0 0 0.47749 0 0 0 0 0.575 0 0 0 0.27 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_65265_23195"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_65265_23195" result="shape"/>
</filter>
<radialGradient id="paint0_angular_65265_23195" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(95 65) rotate(90) scale(125 72.2749)">
<stop stop-color="#FDFEFF" stop-opacity="0.55"/>
<stop offset="0.5" stop-color="#F3F7FF"/>
</radialGradient>
<linearGradient id="paint1_linear_65265_23195" x1="91.8628" y1="119.05" x2="91.8628" y2="151.193" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFEFF"/>
<stop offset="0.9964" stop-color="#ECF0F5"/>
</linearGradient>
<linearGradient id="paint2_linear_65265_23195" x1="95.1773" y1="39.7013" x2="95.1773" y2="71.8442" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFEFF"/>
<stop offset="0.9964" stop-color="#ECF0F5"/>
</linearGradient>
<linearGradient id="paint3_linear_65265_23195" x1="79.9899" y1="79.1891" x2="79.9899" y2="111.332" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFEFF"/>
<stop offset="0.9964" stop-color="#ECF0F5"/>
</linearGradient>
</defs>
</svg>
