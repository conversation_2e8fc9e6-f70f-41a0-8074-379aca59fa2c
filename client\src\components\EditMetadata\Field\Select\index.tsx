import './index.scss';
import { MenuItem, Select as MuiSelect, PopoverOrigin } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';

const evidenceTypes = [
  '911 Call Recording',
  'Arrest Report',
  'Body Worn Camera',
  'Booking Photo',
  'Citizen Submitted Video',
  'Crime Scene Photo',
  'In Car Video',
  'Interview Audio Recording',
  'Interview Room Recording',
  'Mobile Device Extraction',
  'Security Camera Video',
];

const sources = [
  'Axon',
  'Milestone',
  'Motorola',
  'Cellebrite',
  'GrayKey',
  'Upload',
];

const cameraTypes = ['Doorbell Camera', 'Mobile Phone', 'Security Camera'];

const deviceTypes = [
  'Desktop',
  'Laptop',
  'Phone',
  'Tablet',
  'Other (e.g. iPod Touch)',
];

const menuItemsMap: Record<string, string[]> = {
  sourceName: sources,
  evidenceType: evidenceTypes,
  cameraType: cameraTypes,
  deviceType: deviceTypes,
};

const MetadataSelect = ({ name }: Props) => {
  const { control } = useFormContext();
  const isSource = name === 'sourceName';

  const menuItems = menuItemsMap?.[name] ?? [];
  const sourceMenuPosition: MenuPosition = {
    anchorOrigin: { vertical: 'top', horizontal: 'center' },
    transformOrigin: { vertical: 'bottom', horizontal: 'center' },
  };
  const defaultMenuPosition: MenuPosition = {
    anchorOrigin: { vertical: 'bottom', horizontal: 'center' },
    transformOrigin: { vertical: 'top', horizontal: 'center' },
  };

  return (
    <div className="edit-metadata-select">
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <MuiSelect
            {...field}
            fullWidth
            className="metadata-select-input"
            MenuProps={{
              ...(isSource ? sourceMenuPosition : defaultMenuPosition),
              MenuListProps: {
                className: 'metadata-select-menu-list',
              },
            }}
          >
            {menuItems.map((item) => (
              <MenuItem key={item} value={item}>
                {item}
              </MenuItem>
            ))}
          </MuiSelect>
        )}
      />
    </div>
  );
};

interface Props {
  name: string;
}

interface MenuPosition {
  anchorOrigin: PopoverOrigin;
  transformOrigin: PopoverOrigin;
}

export default MetadataSelect;
