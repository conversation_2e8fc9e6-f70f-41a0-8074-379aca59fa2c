import CustomNotification, {
  CustomActionProp,
} from '@components/CustomSnackbar';
import '@fontsource/nunito';
import { I18nProvider } from '@i18n';
import App from '@pages/App';
import { store } from '@store/index';
import { initTheme, ThemeProvider } from '@theme';
import { SnackbarProvider } from 'notistack';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import 'rsuite/dist/rsuite-no-reset.min.css';
import initAiware from './initAiware';
import './main.dark.scss';
import './main.scss';
import './sentry';
import './theme/color.scss';

const locale = localStorage.getItem('language') ?? navigator.language;

declare module 'notistack' {
  interface VariantOverrides {
    customSnackbar: {
      loading?: boolean;
      hasClose?: boolean;
      customAction?: CustomActionProp;
      longTextWrap?: boolean;
    };
  }
}

initAiware()
  .then(() => {
    initTheme();
    store.dispatch({ type: 'app/booting' });

    createRoot(document.getElementById('root')!).render(
      <StrictMode>
        <ThemeProvider>
          <I18nProvider locale={locale}>
            <Provider store={store}>
              <SnackbarProvider
                Components={{ customSnackbar: CustomNotification }}
              >
                <App />
              </SnackbarProvider>
            </Provider>
          </I18nProvider>
        </ThemeProvider>
      </StrictMode>
    );
  })
  .catch((e) => {
    console.error('Error initializing app', e);
  });
