import { FileTypes } from '@assets/icons';
import { I18nTranslate } from '@i18n';
import { SvgIconProps, SvgIconTypeMap } from '@mui/material';
import { OverridableComponent } from '@mui/material/OverridableComponent';
import db from 'mime-db';
import { ReactElement } from 'react';

const fileTypeMapper = {
  docs: new Set<string>(),
  image: new Set<string>(),
  audio: new Set<string>(),
  video: new Set<string>(),
};

for (const type of Object.keys(db)) {
  if (type.includes('image/')) {
    fileTypeMapper.image.add(type);
  } else if (type.includes('audio/')) {
    fileTypeMapper.audio.add(type);
  } else if (type.includes('video/')) {
    fileTypeMapper.video.add(type);
  } else {
    fileTypeMapper.docs.add(type);
  }
}

export enum FILE_TYPE {
  video = 'video',
  audio = 'audio',
  image = 'image',
  docs = 'docs',
  unknown = 'unknown',
}

export function convertFileType(fileType?: string): FILE_TYPE {
  if (!fileType) {
    return FILE_TYPE.unknown;
  }
  if (fileTypeMapper.video.has(fileType)) {
    return FILE_TYPE.video;
  }
  if (fileTypeMapper.image.has(fileType)) {
    return FILE_TYPE.image;
  }
  if (fileTypeMapper.docs.has(fileType)) {
    return FILE_TYPE.docs;
  }
  if (fileTypeMapper.audio.has(fileType)) {
    return FILE_TYPE.audio;
  }
  return FILE_TYPE.unknown;
}

export const FILE_TYPE_ICON_MAP: Record<
  FILE_TYPE,
  | React.NamedExoticComponent<
      Omit<SvgIconProps, 'ref'> & React.RefAttributes<SVGSVGElement>
    >
  | (OverridableComponent<SvgIconTypeMap<{ customProp?: string }, 'svg'>> & {
      muiName: string;
    })
  | (() => ReactElement)
> = {
  video: FileTypes.VideoCamera,
  audio: FileTypes.Audio,
  docs: FileTypes.File,
  image: FileTypes.Image,
  unknown: () => <div>{I18nTranslate.TranslateMessage('defaultEmpty')}</div>,
};
