export const DELETE_CASE_KEY = 'deletedCaseSdoIds';
const DELETE_FILE_KEY = 'deletedFileTdoIds';
export const PENDING_FILE_KEY = 'pendingFileTdoIds';

export const EXPIRATION_MS = 1000 * 60 * 5;

export interface ExpiringItem<T> {
  value: T;
  expiry: number;
}

export function updatePendingMoveFileToLocalStorage() {
  return saveToLocalStorageWithExpiration({
    key: PENDING_FILE_KEY,
    values: [],
    expirationMs: EXPIRATION_MS,
  });
}

export function savePendingMoveFileToLocalStorage(
  fileId: string,
  oldFolderId: string,
  newFolderId: string
) {
  return saveToLocalStorageWithExpiration({
    key: PENDING_FILE_KEY,
    values: [{ fileId, oldFolderId, newFolderId }],
    expirationMs: EXPIRATION_MS,
  });
}

export function updatePendingDeleteCaseToLocalStorage() {
  return saveToLocalStorageWithExpiration({
    key: DELETE_CASE_KEY,
    values: [],
    expirationMs: EXPIRATION_MS,
  });
}

export function savePendingDeleteCaseToLocalStorage(ids: string[]) {
  return saveToLocalStorageWithExpiration({
    key: DELETE_CASE_KEY,
    values: ids,
    expirationMs: EXPIRATION_MS,
  });
}

export function updatePendingDeleteFileToLocalStorage() {
  return saveToLocalStorageWithExpiration({
    key: DELETE_FILE_KEY,
    values: [],
    expirationMs: EXPIRATION_MS,
  });
}

export function savePendingDeleteFileToLocalStorage(ids: string[]) {
  return saveToLocalStorageWithExpiration({
    key: DELETE_FILE_KEY,
    values: ids,
    expirationMs: EXPIRATION_MS,
  });
}

export function saveToLocalStorageWithExpiration<T>({
  key,
  values,
  expirationMs,
}: {
  key: string;
  values: T[];
  expirationMs: number;
}) {
  const itemsToSave: ExpiringItem<T>[] = [];
  const str = localStorage.getItem(key);
  let items: unknown = [];
  if (str) {
    try {
      items = JSON.parse(str);
    } catch {
      console.error('failed to parse local storage data: ', str);
    }
  }

  const isExpiringItem = (item: unknown): item is ExpiringItem<T> =>
    typeof item === 'object' &&
    item !== null &&
    'expiry' in item &&
    typeof item.expiry === 'number' &&
    'value' in item;

  const itemArr = Array.isArray(items) ? items : [];
  const now = new Date();
  // Filter out expired items
  for (const item of itemArr) {
    if (isExpiringItem(item) && now.getTime() < item.expiry) {
      itemsToSave.push(item);
    }
  }

  // Add the new items
  for (const value of values) {
    const item: ExpiringItem<T> = {
      value: value,
      expiry: now.getTime() + expirationMs,
    };
    itemsToSave.push(item);
  }

  // Update localStorage
  localStorage.setItem(key, JSON.stringify(itemsToSave));
  return itemsToSave;
}

export function localStorageHasItemsForKey<T>(key: string) {
  const str = localStorage.getItem(key);
  let items: unknown = [];
  if (str) {
    try {
      items = JSON.parse(str);
    } catch {
      console.error('failed to parse local storage data: ', str);
    }
  }

  const isExpiringItem = (item: unknown): item is ExpiringItem<T> =>
    typeof item === 'object' &&
    item !== null &&
    'expiry' in item &&
    typeof item.expiry === 'number' &&
    'value' in item;

  const itemArr = Array.isArray(items) ? items : [];
  const now = new Date();
  // Filter out expired items
  const nonExpiredItems: ExpiringItem<T>[] = [];
  for (const item of itemArr) {
    if (isExpiringItem(item) && now.getTime() < item.expiry) {
      nonExpiredItems.push(item);
    }
  }

  return nonExpiredItems.length > 0;
}

export function getLocalStorageItemsForKey<T>(key: string): ExpiringItem<T>[] {
  const str = localStorage.getItem(key);
  let items: unknown = [];
  if (str) {
    try {
      items = JSON.parse(str);
    } catch {
      console.error('failed to parse local storage data: ', str);
    }
  }

  const isExpiringItem = (item: unknown): item is ExpiringItem<T> =>
    typeof item === 'object' &&
    item !== null &&
    'expiry' in item &&
    typeof item.expiry === 'number' &&
    'value' in item;

  const itemArr = Array.isArray(items) ? items : [];
  const now = new Date();
  return itemArr
    .filter((item) => isExpiringItem(item))
    .filter((item) => now.getTime() < item.expiry);
}

export function removeLocalStorageItems<T>(key: string, value: T[]) {
  const str = localStorage.getItem(key);
  let items: unknown = [];
  if (str) {
    try {
      items = JSON.parse(str);
    } catch {
      console.error('failed to parse local storage data: ', str);
    }
  }

  const isExpiringItem = (item: unknown): item is ExpiringItem<T> =>
    typeof item === 'object' &&
    item !== null &&
    'expiry' in item &&
    typeof item.expiry === 'number' &&
    'value' in item;

  const itemArr = Array.isArray(items) ? items : [];
  const now = new Date();
  // Filter out expired items
  const nonExpiredItems = itemArr.filter(
    (item) =>
      isExpiringItem(item) &&
      now.getTime() < item.expiry &&
      value.includes(item.value)
  );

  // Update localStorage
  localStorage.setItem(key, JSON.stringify(nonExpiredItems));
}
