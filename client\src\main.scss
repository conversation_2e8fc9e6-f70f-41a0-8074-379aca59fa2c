@font-face {
  font-family: Mundial;
  src: url('assets/fonts/mundial/MundialRegular.otf') format('opentype');
}

* {
  box-sizing: border-box;
  font-family: 'Nunito', sans-serif !important;
}

/* ===== Scrollbar CSS ===== */
/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) transparent;
}

/* Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 5px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: var(--scrollbar-thumb);
}

body {
  margin: 0;
  height: 100%;
  overflow: hidden;
  overscroll-behavior: none;
  background: var(--background-primary);
}

#root {

  display: flex;
  flex-direction: column;
  height: calc(100vh - 55px); // 55px is the height of the app bar

  hr {
    border-top: 1px solid var(--hr);
  }
  
  .Sdk-MuiTableContainer-root {
    background-color: var(--background-secondary);
    border-radius: 8px;
  }
  
  h2 {
    font-size: 1rem;
  }
  
  p {
    font-size: 0.875rem;
  }

  .Sdk-MuiAutocomplete-root {
    .Sdk-MuiAccordionSummary-expandIconWrapper {
      transform: none;
    }
  }

  .Sdk-MuiAutocomplete-endAdornment,
  .Sdk-MuiInputAdornment-root {
    color: var(--text-primary);

    .Sdk-MuiSvgIcon-root {
      color: var(--text-primary);
    }
  }

  .rs-input-group {
    border: solid 1px rgba(0, 0, 0, 0.23);
  }

  .rs-input-group.rs-input-group-inside .rs-input-group-addon {
    padding: 8px 12px;
  }

  .rs-input {
    padding: 5px 11px;
  }

  .Sdk-MuiAutocomplete-popper {
    .Sdk-MuiAutocomplete-option {
      padding: 5px 10px;
      font-size: 12px;
      font-weight: 400;

      &.Mui-focusVisible {
        background-color: var(--background-secondary);
      }
    }
  }

  .Sdk-MuiAutocomplete-root {
    input {
      font-size: 12px;
      font-weight: 400;
    }
  }

  .Sdk-MuiInputBase-root {
    input {
      padding: 6.5px 14px;
    }
  }

  .Sdk-MuiInputBase-input {
    font-size: 12px;
    font-weight: 400;
  }

  .Sdk-MuiTypography-root {
    color: var(--text-primary);
    font-weight: 400;
  }

  .Sdk-MuiTabs-indicator {
    background: transparent;
  }
}

#veritone-sdk-help-button {
  & > div > button > div {
    background: var(--button-nav-bar);
  }
}

#aiware-app-bar {
  & > div {
    border-bottom: none;
  }

  h1 {
    font-weight: 700;
    text-transform: capitalize;
    font-family: 'Mundial', sans-serif !important;
  }
}

#aiware-panel-backdrop {
  z-index: 100 !important;
}

// Force hide app switcher dialog only
div[role="presentation"].tss-208dqn-dialog,
div[class*="application-dialog"]:not([class*="DATA_CENTER_IMPORTER"]) {
  display: none !important;
}
 
// Hide the app switcher backdrop only
div[class*="Sdk-MuiBackdrop-root"][class*="application-dialog"]:not([class*="DATA_CENTER_IMPORTER"]) {
  display: none !important;
}