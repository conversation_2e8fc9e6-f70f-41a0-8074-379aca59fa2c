import { getContentType } from '@store/modules/metadata/getFileMetadata';

export const getThumbnailUrl = (
  fileType: string,
  programLiveImage: string
): string => {
  const mainFileType = getContentType(fileType);
  switch (mainFileType) {
    case 'audio':
      return '/audio-null.svg';
    case 'document':
      return '/doc-null.svg';
    case 'image':
      return programLiveImage || '/image-null.svg';
    default:
      return programLiveImage || '/video-null.svg';
  }
};
