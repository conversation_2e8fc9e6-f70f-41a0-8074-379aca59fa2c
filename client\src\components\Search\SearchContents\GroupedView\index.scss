.grouped-view {
  height: 100%;
  display: flex;
  flex-direction: column;

  .accordion-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 11px;
    overflow: auto;
  }

  .accordion-header__checkbox-label {
    display: flex;
    align-items: center;
    color: var(--text-primary);
    min-width: fit-content;
    padding: 6px;
    border-radius: 4px;

    &.empty-result-disabled {
      opacity: 0.6;
    }
  }

  .accordion-header__content-wrapper {
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .accordion-header__category-label {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    padding-left: 10px;
  }

  .accordion-header__category-icon {
    width: 24px;
    height: 24px;
  }

  .accordion-header__category-name {
    font-weight: bold;
  }

  .empty-state-container {
    position: relative;
    top: calc(50% - 8px);
  }

  .Sdk-MuiAccordionSummary-expandIconWrapper {
    margin: 0 6px 0 16px;
  }

  .Sdk-MuiAccordionSummary-root {
    flex-direction: row-reverse;
    border-radius: 8px 8px 0 0;
    border: 1px solid var(--border-color);
  }

  .Sdk-MuiAccordion-root.Mui-expanded {
    margin-bottom: 0;
  }
}

.grouped-view__list-view {
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 8px 8px !important;
  max-height: 50vh;

  .table-body {
    max-height: 50vh;
  }
}
