const SORT_CATEGORY_KEY = 'investigate-sort-category';

export enum SortCategory {
  RecentlyUploaded = 'Recently Uploaded',
  None = 'None', // TODO: update more category when figma design is ready
}

export const isSortCategory = (sort: string): sort is SortCategory =>
  Object.values(SortCategory).includes(sort as SortCategory);

export const getSortCategoryLocalStorage = () => {
  const sort = localStorage.getItem(SORT_CATEGORY_KEY);

  if (sort && isSortCategory(sort)) {
    return sort;
  }

  return SortCategory.RecentlyUploaded;
};

export const setSortCategoryLocalStorage = (sort: SortCategory) => {
  localStorage.setItem(SORT_CATEGORY_KEY, sort);
};
