import './index.scss';
import { noStatusLabelsTagsFound } from '@assets/images';
import { I18nTranslate } from '@i18n';
import AddIcon from '@mui/icons-material/Add';
import { Button } from '@mui/material';

const renderOptionTitle = (status: Props['option']) =>
  ({
    caseStatus: I18nTranslate.TranslateMessage('statuses'),
    caseTags: I18nTranslate.TranslateMessage('tags'),
  })[status];

const EmptyState = ({ option, onClick }: Props) => {
  const intl = I18nTranslate.Intl();
  return (
    <div className="settings-empty-state__container">
      <div className="settings-empty-state__content">
        <img
          // TODO: declare a real type for the image import
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          src={noStatusLabelsTagsFound}
          alt={intl.formatMessage({ id: 'emptyState' })}
          draggable={false}
        />
        <h2>
          {I18nTranslate.TranslateMessage('no')}
          {I18nTranslate.TranslateMessage('space')}
          {renderOptionTitle(option)}
          {I18nTranslate.TranslateMessage('space')}
          {I18nTranslate.TranslateMessage('found')}
        </h2>
        <Button
          variant="outlined"
          color="primary"
          className="settings-empty-state__button"
          startIcon={<AddIcon />}
          onClick={onClick}
        >
          {option === 'caseStatus'
            ? I18nTranslate.TranslateMessage('addStatus')
            : I18nTranslate.TranslateMessage('addTag')}
        </Button>
      </div>
    </div>
  );
};

interface Props {
  option: 'caseStatus' | 'caseTags';
  onClick: () => void;
}

export default EmptyState;
