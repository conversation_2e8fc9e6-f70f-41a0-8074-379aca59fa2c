import createSvgIcon from '../createSvgIcon';
export const Fingerprint = createSvgIcon(
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.8102 4.47C17.7302 4.47 17.6502 4.45 17.5802 4.41C15.6602 3.42 14.0002 3 12.0102 3C10.0302 3 8.15023 3.47 6.44023 4.41C6.20023 4.54 5.90023 4.45 5.76023 4.21C5.63023 3.97 5.72023 3.66 5.96023 3.53C7.82023 2.52 9.86023 2 12.0102 2C14.1402 2 16.0002 2.47 18.0402 3.52C18.2902 3.65 18.3802 3.95 18.2502 4.19C18.1602 4.37 17.9902 4.47 17.8102 4.47ZM3.50023 9.72C3.40023 9.72 3.30023 9.69 3.21023 9.63C2.98023 9.47 2.93023 9.16 3.09023 8.93C4.08023 7.53 5.34023 6.43 6.84023 5.66C9.98023 4.04 14.0002 4.03 17.1502 5.65C18.6502 6.42 19.9102 7.51 20.9002 8.9C21.0602 9.12 21.0102 9.44 20.7802 9.6C20.5502 9.76 20.2402 9.71 20.0802 9.48C19.1802 8.22 18.0402 7.23 16.6902 6.54C13.8202 5.07 10.1502 5.07 7.29023 6.55C5.93023 7.25 4.79023 8.25 3.89023 9.51C3.81023 9.65 3.66023 9.72 3.50023 9.72ZM9.75023 21.79C9.62023 21.79 9.49023 21.74 9.40023 21.64C8.53023 20.77 8.06023 20.21 7.39023 19C6.70023 17.77 6.34023 16.27 6.34023 14.66C6.34023 11.69 8.88023 9.27 12.0002 9.27C15.1202 9.27 17.6602 11.69 17.6602 14.66C17.6602 14.94 17.4402 15.16 17.1602 15.16C16.8802 15.16 16.6602 14.94 16.6602 14.66C16.6602 12.24 14.5702 10.27 12.0002 10.27C9.43023 10.27 7.34023 12.24 7.34023 14.66C7.34023 16.1 7.66023 17.43 8.27023 18.51C8.91023 19.66 9.35023 20.15 10.1202 20.93C10.3102 21.13 10.3102 21.44 10.1202 21.64C10.0102 21.74 9.88023 21.79 9.75023 21.79ZM16.9202 19.94C15.7302 19.94 14.6802 19.64 13.8202 19.05C12.3302 18.04 11.4402 16.4 11.4402 14.66C11.4402 14.38 11.6602 14.16 11.9402 14.16C12.2202 14.16 12.4402 14.38 12.4402 14.66C12.4402 16.07 13.1602 17.4 14.3802 18.22C15.0902 18.7 15.9202 18.93 16.9202 18.93C17.1602 18.93 17.5602 18.9 17.9602 18.83C18.2302 18.78 18.4902 18.96 18.5402 19.24C18.5902 19.51 18.4102 19.77 18.1302 19.82C17.5602 19.93 17.0602 19.94 16.9202 19.94ZM14.9102 22C14.8702 22 14.8202 21.99 14.7802 21.98C13.1902 21.54 12.1502 20.95 11.0602 19.88C9.66023 18.49 8.89023 16.64 8.89023 14.66C8.89023 13.04 10.2702 11.72 11.9702 11.72C13.6702 11.72 15.0502 13.04 15.0502 14.66C15.0502 15.73 15.9802 16.6 17.1302 16.6C18.2802 16.6 19.2102 15.73 19.2102 14.66C19.2102 10.89 15.9602 7.83 11.9602 7.83C9.12023 7.83 6.52023 9.41 5.35023 11.86C4.96023 12.67 4.76023 13.62 4.76023 14.66C4.76023 15.44 4.83023 16.67 5.43023 18.27C5.53023 18.53 5.40023 18.82 5.14023 18.91C4.88023 19.01 4.59023 18.87 4.50023 18.62C4.01023 17.31 3.77023 16.01 3.77023 14.66C3.77023 13.46 4.00023 12.37 4.45023 11.42C5.78023 8.63 8.73023 6.82 11.9602 6.82C16.5102 6.82 20.2102 10.33 20.2102 14.65C20.2102 16.27 18.8302 17.59 17.1302 17.59C15.4302 17.59 14.0502 16.27 14.0502 14.65C14.0502 13.58 13.1202 12.71 11.9702 12.71C10.8202 12.71 9.89023 13.58 9.89023 14.65C9.89023 16.36 10.5502 17.96 11.7602 19.16C12.7102 20.1 13.6202 20.62 15.0302 21.01C15.3002 21.08 15.4502 21.36 15.3802 21.62C15.3302 21.85 15.1202 22 14.9102 22Z"
      strokeWidth="0.3"
    />
  </svg>,
  'Fingerprint'
);
