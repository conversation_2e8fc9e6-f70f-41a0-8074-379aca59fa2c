import { VideoCamera } from '@assets/icons';
import { ResultCategory } from '@store/modules/search/slice';
import { SearchResult } from './SearchTable';

const resultCategories: ResultCategory[] = [
  'transcription',
  'faceRecognition',
  'objectDetection',
  'vehicleRecognition',
  'licensePlateRecognition',
  // 'sceneClassification',
  'textRecognition',
  'metadata',
];

const mockSearchResults: SearchResult[] = Array.from(
  { length: 200 },
  (_, index) => {
    const fileTypes = ['txt', 'video', 'image', 'audio', 'document'];
    const fileType = fileTypes[index % fileTypes.length];

    return {
      id: `file_${index + 1}`,
      fileName: ` File_${index + 1} super long name.txt super long name.txt long name.txt`,
      fileType,
      fileIcon: <VideoCamera />,
      fileDuration: 123456,
      duration: 22.5,
      parentTreeObjectIds: [`parent_${index % 5}`],
      createdByName: `User_${index % 3}`,
      createdTime: new Date(2024, 0, index + 1).toISOString(),
      updatedTime: new Date(2024, 1, index + 1).toISOString(),
      caseId: index < 5 ? undefined : `case_${index + 1}_1234567890`,
      retentionDate: new Date(2025, 0, index + 1).toISOString(),
      description: `Description for file ${index + 1} super long description super long description super long description`,
      thumbnailUrl: `https://picsum.photos/200/300?random=${index}`,
    };
  }
);

// Shuffle and assign each SearchResult to a random category
const mockGroupedResults = resultCategories.reduce<
  Record<
    ResultCategory,
    {
      searchResults: SearchResult[];
      pagination: {
        from: number;
        to: number;
        limit: number;
        totalResults: number;
      };
      loading: boolean;
    }
  >
>((acc, category) => {
  acc[category] = {
    searchResults: [],
    pagination: {
      from: 0,
      to: 0,
      limit: 10,
      totalResults: 0,
    },
    loading: false,
  };
  return acc;
}, {});

// Distribute search results randomly into categories
mockSearchResults.forEach((result) => {
  const randomCategory =
    resultCategories[Math.floor(Math.random() * resultCategories.length)];

  mockGroupedResults[randomCategory].searchResults.push(result);
});

// Update pagination
for (const category of resultCategories) {
  const results = mockGroupedResults[category].searchResults;
  mockGroupedResults[category].pagination.totalResults = results.length;
  mockGroupedResults[category].pagination.to = Math.min(10, results.length);
  mockGroupedResults[category].searchResults = results.slice(0, 10);
}

export default mockGroupedResults;
