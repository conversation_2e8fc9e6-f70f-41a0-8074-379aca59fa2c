.select {
  height: 30px;
  background: var(--background-primary);
  line-height: normal;
}

.select-menu {
  .Sdk-MuiPaper-root {
    & > ul {
      padding: 5px 0;

      & > li {
        padding: 5px 12px;

        &:hover(:not(.Mui-selected)) {
          background: var(--background-primary);
        }
      }
    }
  }
}

.filter-select {
  &__clear-icon {
    width: 15px;
    height: 15px;
    opacity: 0.6;
    color: var(--button-inner);
    padding: 2px;
    border-radius: 50%;
    background-color: var(--chip-background-grey);
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}
