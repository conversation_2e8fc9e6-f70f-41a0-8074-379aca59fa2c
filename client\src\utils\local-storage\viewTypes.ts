const VIEW_TYPE_KEY = 'investigate-view-type';

export enum ViewType {
  LIST = 'list',
  GRID = 'grid',
}

const isViewType = (view: string): view is ViewType =>
  Object.values(ViewType).includes(view as ViewType);

export const getViewTypeLocalStorage = () => {
  const viewType = localStorage.getItem(VIEW_TYPE_KEY);

  if (viewType && isViewType(viewType)) {
    return viewType;
  }

  return ViewType.LIST;
};

export const setViewTypeLocalStorage = (viewType: ViewType) => {
  localStorage.setItem(VIEW_TYPE_KEY, viewType);
};
