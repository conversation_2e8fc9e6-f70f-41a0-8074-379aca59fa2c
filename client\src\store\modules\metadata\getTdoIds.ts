import { GQLApi } from '@utils/helpers';

export async function getTdoIds(folderId: string, gql: GQLApi) {
  const response = await gql.searchMedia({
    folderId,
    offset: 0,
    sortBy: 'createdTime',
    sortDirection: 'desc',
  });
  if (!response) {
    throw new Error('could not fetch tdo ids');
  }

  const tdoIds = response.searchMedia.jsondata.results.map(
    ({ recording }) => recording.recordingId
  );
  // TODO Handle error

  return tdoIds;
}
