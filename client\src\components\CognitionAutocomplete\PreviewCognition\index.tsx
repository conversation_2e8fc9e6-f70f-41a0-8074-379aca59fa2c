import { MathOperators } from '@store/modules/search/slice';
import cn from 'classnames';
import { useEffect, useMemo, useRef, useState } from 'react';
import { v4 as uuid } from 'uuid';
import {
  AliasParenthesisMap,
  CognitionItem,
  CognitionList,
  Parenthesis,
} from '..';
import './index.scss';
import { enqueueSnackbar } from 'notistack';
import { I18nTranslate } from '@i18n';
import { isRedundantParentheses } from '@utils/validationTokens';

interface Props {
  fieldValues: CognitionList;
  updateFieldValues: (fieldValues: CognitionList) => void;
  aliasParenthesisMap: AliasParenthesisMap;
  setAliasParenthesisMap: (map: AliasParenthesisMap) => void;
  error?: string;
}

const PreviewCognition = ({
  fieldValues,
  updateFieldValues,
  aliasParenthesisMap,
  setAliasParenthesisMap,
  error,
}: Props) => {
  const intl = I18nTranslate.Intl();
  const previewRef = useRef<HTMLDivElement | null>(null);
  const openRef = useRef<HTMLDivElement | null>(null);
  const closeRef = useRef<HTMLDivElement | null>(null);

  const [currentTagId, setCurrentTagId] = useState<string | null>(null);

  const isShowOpen = useMemo(() => {
    if (!currentTagId) {
      return false;
    }

    const aliasParenthesis = aliasParenthesisMap[currentTagId];
    if (
      aliasParenthesis &&
      aliasParenthesis.parenthesis === Parenthesis.CLOSE_PARENTHESIS
    ) {
      return false;
    }

    return true;
  }, [currentTagId, aliasParenthesisMap]);

  const isShowClose = useMemo(() => {
    if (!currentTagId) {
      return false;
    }

    const aliasParenthesis = aliasParenthesisMap[currentTagId];
    if (
      aliasParenthesis &&
      aliasParenthesis.parenthesis === Parenthesis.OPEN_PARENTHESIS
    ) {
      return false;
    }

    if (fieldValues[0]?.id === currentTagId) {
      return false;
    }

    return true;
  }, [currentTagId, aliasParenthesisMap, fieldValues]);

  const handleReset = () => {
    setCurrentTagId(null);
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        (openRef.current && !openRef.current.contains(event.target as Node)) ||
        (closeRef.current && !closeRef.current.contains(event.target as Node))
      ) {
        handleReset();
      }
    }

    document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [openRef.current]);

  const handleClickValue = ({
    value,
    e,
  }: {
    value: CognitionItem;
    e: React.MouseEvent<HTMLSpanElement>;
  }) => {
    e.stopPropagation();

    const { id, type, valueId } = value;

    if (type === 'operator') {
      return;
    }

    if (type === 'parenthesis') {
      if (!valueId) {
        return;
      }

      const aliasParenthesis = aliasParenthesisMap[valueId];
      if (!aliasParenthesis) {
        return;
      }

      const index = fieldValues.findIndex((item) => item.id === id);
      if (index === -1) {
        return;
      }

      const updateValues = [...fieldValues];
      updateValues.splice(index, 1);
      updateFieldValues(updateValues);

      const newMap = { ...aliasParenthesisMap };

      if (aliasParenthesis.total === 1) {
        delete newMap[valueId];
      } else if (aliasParenthesis.total > 1) {
        newMap[valueId] = {
          ...aliasParenthesis,
          total: aliasParenthesis.total - 1,
        };
      }

      setAliasParenthesisMap(newMap);
      return;
    }

    setCurrentTagId(id);
  };

  const handleChooseParenthesis = ({
    id,
    option,
  }: {
    id: string;
    option: Parenthesis;
  }) => {
    const updateValues = [...fieldValues];
    const tagIndex = updateValues.findIndex((item) => item.id === id);

    if (tagIndex === -1) {
      return;
    }

    if (option === Parenthesis.OPEN_PARENTHESIS) {
      updateValues.splice(tagIndex, 0, {
        id: `open-parenthesis-${uuid()}`,
        type: 'parenthesis',
        cognitionType: 'operator',
        value: MathOperators.OPEN_PARENTHESIS,
        valueId: id,
      });
    } else if (option === Parenthesis.CLOSE_PARENTHESIS) {
      updateValues.splice(tagIndex + 1, 0, {
        id: `close-parenthesis-${uuid()}`,
        type: 'parenthesis',
        cognitionType: 'operator',
        value: MathOperators.CLOSE_PARENTHESIS,
        valueId: id,
      });
    }

    if (isRedundantParentheses(updateValues)) {
      handleReset();
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'redundantParenthesis',
          defaultMessage: 'Redundant parentheses!',
        }),
        variant: 'warning',
      });
      return;
    }

    updateFieldValues(updateValues);
    const newMap = { ...aliasParenthesisMap };
    newMap[id] = {
      parenthesis:
        option === Parenthesis.OPEN_PARENTHESIS
          ? Parenthesis.OPEN_PARENTHESIS
          : Parenthesis.CLOSE_PARENTHESIS,
      total: (newMap[id]?.total || 0) + 1,
    };
    setAliasParenthesisMap(newMap);
    handleReset();
  };

  return (
    <>
      {!!fieldValues.length && (
        <div ref={previewRef} className="cognition-preview">
          {fieldValues.map((value) => {
            const { id, alias, value: label, type } = value;

            return (
              <span key={id}>
                {isShowOpen && currentTagId === id && (
                  <span
                    ref={openRef}
                    className="cognition-preview__parenthesis-option open"
                    onClick={() =>
                      handleChooseParenthesis({
                        id: id,
                        option: Parenthesis.OPEN_PARENTHESIS,
                      })
                    }
                  >
                    {Parenthesis.OPEN_PARENTHESIS}
                  </span>
                )}
                <span
                  onClick={(e) => handleClickValue({ value, e })}
                  className={cn('cognition-preview__item', {
                    'cognition-preview__alias': alias,
                    'cognition-preview__parenthesis': type === 'parenthesis',
                  })}
                  data-testid={`cognition-preview-item-${id}`}
                >
                  {alias || label}
                </span>
                {isShowClose && currentTagId === id && (
                  <span
                    ref={closeRef}
                    className="cognition-preview__parenthesis-option close"
                    onClick={() =>
                      handleChooseParenthesis({
                        id: id,
                        option: Parenthesis.CLOSE_PARENTHESIS,
                      })
                    }
                  >
                    {Parenthesis.CLOSE_PARENTHESIS}
                  </span>
                )}
              </span>
            );
          })}
        </div>
      )}
      {error && <span className="cognition-warning-message">{error}</span>}
    </>
  );
};

export default PreviewCognition;
