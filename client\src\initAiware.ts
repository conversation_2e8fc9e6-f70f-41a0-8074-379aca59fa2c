const initAiware = () =>
  new Promise<void>((resolve, reject) => {
    console.log('aiware-js loading...', window.aiware);

    const {
      aiwareJSPath,
      aiwareJSVersion,
      apiRoot,
      veritoneAppId,
      graphQLEndpoint,
    } = window?.config ?? {};
    if (
      !aiwareJSPath ||
      !aiwareJSVersion ||
      !apiRoot ||
      !veritoneAppId ||
      !graphQLEndpoint
    ) {
      console.error(
        'aiwareJSPath, aiwareJSVersion, apiRoot, graphQLEndpoint, and veritoneAppId are all required in the config.'
      );
      return;
    }

    document
      .getElementsByTagName('head')[0]
      .insertAdjacentHTML(
        'beforeend',
        `<link rel="stylesheet" type="text/css" href="${aiwareJSPath}${aiwareJSVersion}/index.esm.min.css">`
      );

    const aiwareJSScript = document.createElement('script');
    aiwareJSScript.setAttribute('type', 'module');
    aiwareJSScript.setAttribute(
      'src',
      `${aiwareJSPath}${aiwareJSVersion}/index.esm.min.js`
    );
    aiwareJSScript.setAttribute('nonce', 'NGINX_CSP_NONCE');
    aiwareJSScript.onload = () => {
      // It takes about 260ms for the window.aiware object to be available 🙄
      const checkIfLoaded = () =>
        setTimeout(() => {
          if (window.aiware) {
            console.log('aiware-js loaded');
            window.aiware.init({
              baseUrl: `${apiRoot}/${graphQLEndpoint}`,
              applicationId: veritoneAppId,
              // knowledgeBaseUrl: '<your-knowledge-base-url>' // TODO: Grab from config
            });
            resolve();
          } else {
            checkIfLoaded();
          }
        }, 50);
      checkIfLoaded();
    };
    aiwareJSScript.onerror = reject;
    document.head.appendChild(aiwareJSScript);
  });

export default initAiware;
