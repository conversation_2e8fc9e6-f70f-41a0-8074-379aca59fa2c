.case-manager-table {
  display: flex;
  flex: 2;

  .Sdk-MuiTableCell-root:first-child, .table-cell:first-child {
    padding-left: 22px;
    padding-right: 0;
  }

  .Sdk-MuiTableCell-root:nth-child(2), .table-cell:nth-child(2) {
    padding-left: 10px;
  }
}

td {
  .case-table-cell {
    position: relative;

    &::before {
      content: '&nbsp;';
      visibility: hidden;
    }

    & > span {
      position: absolute;
      left: 0;
      right: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .retention-date {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 0;
  }
}
