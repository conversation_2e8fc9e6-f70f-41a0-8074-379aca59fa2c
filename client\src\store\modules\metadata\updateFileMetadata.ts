import {
  EvidenceTypeMetadata,
  FileMetadata,
  Metadata,
} from '@shared-types/metadata';
import { GQLApi } from '@utils/helpers';

export async function updateFileMetadata(
  tdoId: string,
  updatedMetadata: Metadata,
  evidenceTypeSchemaId: string,
  gql: GQLApi
) {
  const fileMetadata: Pick<
    FileMetadata,
    'veritoneFile' | 'description' | 'aiCognitionEngineOutput' | 'sourceName'
  > = {
    veritoneFile: {
      ...updatedMetadata.veritoneFile,
    },
    description: updatedMetadata.description,
    aiCognitionEngineOutput: updatedMetadata.aiCognitionEngineOutput, // probably move
    sourceName: updatedMetadata.sourceName,
  };

  // const genericInvestigateMetadata: GenericInvestigateMetadata = {
  //   sourceId: updatedMetadata.sourceId,
  //   contentType: updatedMetadata.contentType,
  //   assetStatus: updatedMetadata.assetStatus,
  // };

  const evidenceTypeMetadata: EvidenceTypeMetadata = {
    evidenceType: updatedMetadata.evidenceType,
    ...(updatedMetadata.badgeId && { badgeId: updatedMetadata.badgeId }),
    ...(updatedMetadata.cadId && { cadId: updatedMetadata.cadId }),
    ...(updatedMetadata.callerPhoneNumber && {
      callerPhoneNumber: updatedMetadata.callerPhoneNumber,
    }),
    ...(updatedMetadata.cameraFacingDirection && {
      cameraFacingDirection: updatedMetadata.cameraFacingDirection,
    }),
    ...(updatedMetadata.cameraPhysicalAddress && {
      cameraPhysicalAddress: updatedMetadata.cameraPhysicalAddress,
    }),
    ...(updatedMetadata.cameraPhysicalLocation && {
      cameraPhysicalLocation: updatedMetadata.cameraPhysicalLocation,
    }),
    ...(updatedMetadata.cameraType && {
      cameraType: updatedMetadata.cameraType,
    }),
    ...(updatedMetadata.citizenName && {
      citizenName: updatedMetadata.citizenName,
    }),
    ...(updatedMetadata.dateOfBirth && {
      dateOfBirth: updatedMetadata.dateOfBirth,
    }),
    ...(updatedMetadata.deviceId && { deviceId: updatedMetadata.deviceId }),
    ...(updatedMetadata.deviceModel && {
      deviceModel: updatedMetadata.deviceModel,
    }),
    ...(updatedMetadata.deviceName && {
      deviceName: updatedMetadata.deviceName,
    }),
    ...(updatedMetadata.deviceRegisteredOwner && {
      deviceRegisteredOwner: updatedMetadata.deviceRegisteredOwner,
    }),
    ...(updatedMetadata.deviceType && {
      deviceType: updatedMetadata.deviceType,
    }),
    ...(updatedMetadata.evidenceTechnician && {
      evidenceTechnician: updatedMetadata.evidenceTechnician,
    }),
    ...(updatedMetadata.firstName && { firstName: updatedMetadata.firstName }),
    ...(updatedMetadata.interviewRoom && {
      interviewRoom: updatedMetadata.interviewRoom,
    }),
    ...(updatedMetadata.interviewee && {
      interviewee: updatedMetadata.interviewee,
    }),
    ...(updatedMetadata.interviewer && {
      interviewer: updatedMetadata.interviewer,
    }),
    ...(updatedMetadata.lastName && { lastName: updatedMetadata.lastName }),
    ...(updatedMetadata.locationTimeline && {
      locationTimeline: updatedMetadata.locationTimeline,
    }),
    ...(updatedMetadata.officerName && {
      officerName: updatedMetadata.officerName,
    }),
    ...(updatedMetadata.reportNumber && {
      reportNumber: updatedMetadata.reportNumber,
    }),
  };

  const evidenceTypeResponse = await gql.updateFileMetadata({
    tdoId,
    fileMetadata,
    evidenceTypeMetadata,
    evidenceTypeSchemaId,
  });
  if (!evidenceTypeResponse) {
    throw new Error('no evidence type response');
  }

  return updatedMetadata;
}
