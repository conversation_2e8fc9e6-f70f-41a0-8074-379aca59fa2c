import { <PERSON><PERSON><PERSON><PERSON> } from '@utils/helpers';
import { getSummary } from './getSummary';
import { AssetStatus, Metadata } from '@shared-types/metadata';

export async function getFileMetadata(
  fileIds: string[],
  evidenceTypeSchemaId: string,
  gql: GQLApi
) {
  const rawFileMetadata = await gql.getFileMetadata(fileIds);
  if (!rawFileMetadata) {
    throw new Error('no file metadata');
  }
  const userIds = [
    ...new Set(
      rawFileMetadata.temporalDataObjects.records
        .map((tdo) => tdo.createdBy)
        .filter((id): id is string => !!id)
    ),
  ];
  const usersResponse =
    userIds.length > 0 ? await gql.getUsersInfo({ userIds }) : null;
  const usersList = usersResponse?.data.users?.records ?? [];

  const usersMap = new Map(usersList.map((user) => [user.id, user]));

  return Promise.all(
    rawFileMetadata.temporalDataObjects.records.map(async (tdo) => {
      const user = tdo.createdBy ? usersMap.get(tdo.createdBy) : undefined;
      const contentTemplate = tdo.assets.records.find(
        (asset) => asset.jsondata.schemaId === evidenceTypeSchemaId
      );
      const evidenceTypeJson: unknown = JSON.parse(
        contentTemplate?.transform ?? '{}'
      );
      if (typeof evidenceTypeJson !== 'object') {
        throw new Error('Invalid evidence type metadata');
      }
      const fileCase = tdo.folders.find(
        (folder) => folder.contentTemplates.length > 0
      )?.contentTemplates?.[0]?.sdo?.data;
      const summary = await getSummary(tdo.id, gql); // TODO: Optimize

      const fileMetadata: Metadata = {
        /* File Metadata */
        aiwareTdoId: tdo.id,
        description: tdo.description,
        uploadedDate: tdo.createdDateTime,
        fileSize: tdo.details.veritoneFile.size,
        fileFormat: tdo.name?.match(/\.([0-9a-z]+)(?:[\?#]|$)/i)?.[1] ?? '',
        duration: tdo.primaryAsset?.jsondata?.mediaDuration ?? 0,
        fileName:
          tdo.details.veritoneFile.fileName ??
          tdo.details.veritoneFile.filename,
        veritoneFile: tdo.details.veritoneFile,
        caseId: fileCase?.caseId ?? '',
        caseName: fileCase?.caseName ?? '',
        aiCognitionEngineOutput: [],
        summary,
        sourceName: '',
        creator: user ? `${user.firstName} ${user.lastName}` : '',

        /* Generic Investigate Metadata */
        sourceId: '',
        contentType: getContentType(tdo.details.veritoneFile.mimetype),
        assetStatus: getFileStatus(tdo.jobs.records),

        /* Evidence Type Metadata */
        evidenceType: '',
        ...evidenceTypeJson,
      };
      return fileMetadata;
    })
  );
}

export const getContentType = (mimeType?: string) => {
  if (!mimeType) {
    return 'document';
  }
  if (mimeType.startsWith('video/')) {
    return 'video';
  }

  if (mimeType.startsWith('audio/')) {
    return 'audio';
  }

  if (mimeType.startsWith('image/')) {
    return 'image';
  }

  return 'document';
};

interface Job {
  id: string;
  status:
    | 'pending'
    | 'queued'
    | 'running'
    | 'complete'
    | 'failed'
    | 'cancelled'
    | 'aborted';
}

const getFileStatus = (jobs: Job[]): AssetStatus => {
  if (jobs.length === 0) {
    return null;
  }

  const statuses = jobs.map((job) => job.status);

  if (statuses.includes('failed')) {
    return 'error';
  }

  if (
    statuses.some((status) => ['pending', 'queued', 'running'].includes(status))
  ) {
    return 'processing';
  }

  if (statuses.every((status) => status === 'complete')) {
    return 'processed';
  }

  return 'processing';
};
