import { I18nTranslate } from '@i18n';
import { PayloadAction } from '@reduxjs/toolkit';
import { CaseSearchResults, CaseStatus, CaseTag } from '@shared-types/types';
import { createAppSlice } from '@store/createAppSlice';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/index';
import { searchCases } from '@store/modules/caseManager/searchCases';
import { setCaseFilter } from '@store/modules/caseManager/slice';
import {
  selectStatusSchema,
  selectTagSchema,
} from '@store/modules/config/slice';
import { ApiStatus } from '@store/types';
import { getApiAuthToken } from '@store/utils';
import { GQLApi, SortBy } from '@utils/helpers';
import { isEqual } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { fetchStatusList } from './fetchStatuses';
import { fetchTagsList } from './fetchTags';
import { getNewRows } from './getNewRows';
import { reassignStatus } from './reassignStatus';
import { saveStatusList } from './saveStatuses';
import { saveTagList } from './saveTags';

export enum Visibility {
  Active = 'Active',
  Inactive = 'Inactive',
}

export interface StatusTagRow {
  id: string;
  name: string;
  color?: string;
  visibility: Visibility;
  error?: boolean;
  selected?: boolean;
  isDeleted?: boolean;
}

export interface SettingsSliceState {
  saveStatuses: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  saveTags: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  // TODO: Rename, might want to combine with saveStatuses for most up-to-date statuses
  fetchedStatuses: {
    status: ApiStatus;
    error?: string;
    statuses: CaseStatus[];
    sdoId: string;
  };
  fetchedTags: {
    status: ApiStatus;
    error?: string;
    tags: CaseTag[];
    sdoId: string;
  };
  searchCasesByStatus: {
    status: ApiStatus;
    error?: string;
    data: CaseSearchResults['searchMedia']['jsondata'];
  };
  reassignStatuses: {
    deletedStatusIds: string[];
    reassignStatusId: string;
    cases: CaseSearchResults['searchMedia']['jsondata']['results'];
  }[];
  rowSelection: { [key: string]: boolean };
  editingRows: StatusTagRow[];
}

export const initialState: SettingsSliceState = {
  saveStatuses: {
    status: 'idle',
    error: '',
    id: '',
  },
  saveTags: {
    status: 'idle',
    error: '',
    id: '',
  },
  fetchedStatuses: {
    status: 'idle',
    error: '',
    statuses: [],
    sdoId: '',
  },
  fetchedTags: {
    status: 'idle',
    error: '',
    tags: [],
    sdoId: '',
  },
  searchCasesByStatus: {
    status: 'idle',
    error: '',
    data: {
      results: [],
      totalResults: 0,
      limit: 0,
      from: 0,
      to: 0,
    },
  },
  reassignStatuses: [],
  rowSelection: {},
  editingRows: [],
};

export const settingsSlice = createAppSlice({
  name: 'settings',
  initialState,
  reducers: (create) => {
    const createThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();
    return {
      saveStatuses: createThunk(
        async (
          payload: {
            statuses: CaseStatus[];
            sdoId?: string;
            isAddNew?: boolean;
          },
          thunkAPI
        ) => {
          const { getState, dispatch } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const statusSchemaId = selectStatusSchema(state).id;
          if (!statusSchemaId) {
            throw new Error('no status schema id found');
          }

          const reassignStatuses = state.settings.reassignStatuses;
          if (reassignStatuses.length) {
            for (const { reassignStatusId, cases } of reassignStatuses) {
              await reassignStatus({
                cases,
                statusId: reassignStatusId,
                dataRegistryId: config.registryIds.caseRegistryId,
                gql,
              });
            }

            // clear search cases filter by status if it is deleted
            const deletedStatusIds = new Set(
              reassignStatuses.flatMap((rs) => rs.deletedStatusIds)
            );
            const filterStatusId = state.caseManager.caseFilter.statusId;
            if (filterStatusId && deletedStatusIds.has(filterStatusId)) {
              dispatch(
                setCaseFilter({
                  ...state.caseManager.caseFilter,
                  statusId: undefined,
                })
              );
            }
          }

          const sodId = await saveStatusList({
            statuses: payload.statuses,
            statusSchemaId,
            userId: state.user.user.userId,
            gql,
            sdoId: payload.sdoId,
          });
          return sodId;
        },
        {
          pending: (state) => {
            state.saveStatuses.status = 'loading';
            state.saveStatuses.id = '';
          },
          fulfilled: (state, action) => {
            const { isAddNew } = action.meta.arg;
            state.saveStatuses.status = 'complete';
            state.saveStatuses.id = action.payload.id;
            state.fetchedStatuses.statuses = action.payload.data.statuses;
            state.fetchedStatuses.sdoId = action.payload.id;
            state.rowSelection = {};
            state.reassignStatuses = [];

            const message = I18nTranslate.TranslateMessage(
              isAddNew ? 'addStatusSuccess' : 'saveStatusesSuccess'
            );
            enqueueSnackbar(message, { variant: 'success' });
          },
          rejected: (state, action) => {
            const { isAddNew } = action.meta.arg;
            state.saveStatuses.status = 'failure';
            state.saveStatuses.id = '';

            if (
              action.error.message &&
              action.error.message.startsWith('concurrentModificationError')
            ) {
              // set the latest sdoId into the state for the frontend concurrency check
              state.fetchedStatuses.sdoId = action.error.message.split('_')[1];
              state.saveStatuses.status = 'concurrentModificationError';
            } else {
              console.error('failed to create statuses', action.error);
              const message = I18nTranslate.TranslateMessage(
                isAddNew ? 'addStatusFailure' : 'saveStatusesFailure'
              );
              enqueueSnackbar(message, { variant: 'error' });
            }
          },
        }
      ),
      fetchStatuses: createThunk(
        async (_: { isPolling?: boolean }, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const statusSchemaId = selectStatusSchema(state).id;
          if (!statusSchemaId) {
            throw new Error('no status schema id found');
          }

          const statusesData = await fetchStatusList({
            schemaId: statusSchemaId,
            gql,
          });
          return statusesData;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPolling) {
              state.fetchedStatuses.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const { isPolling } = action.meta.arg;
            const editingRows = state.editingRows;
            const currStatuses = state.fetchedStatuses.statuses;
            const newStatuses = action.payload.statuses;

            state.fetchedStatuses.status = 'complete';
            state.fetchedStatuses.sdoId = action.payload.sdoId;
            state.fetchedStatuses.statuses = newStatuses;
            // update editing rows if polling new statuses created
            if (isPolling && !isEqual(currStatuses, newStatuses)) {
              state.editingRows = getNewRows({
                currData: currStatuses,
                newData: newStatuses,
              }).concat(editingRows);
            }
          },
          rejected: (state, _action) => {
            state.fetchedStatuses.status = 'failure';
            state.fetchedStatuses.statuses = [];
          },
        }
      ),
      pollingFetchSettingsData: createThunk((_, thunkAPI) => {
        const { signal, dispatch, getState } = thunkAPI;
        const state = getState() as RootState;
        let dispatchPromiseStatuses: DispatchPromise;
        let dispatchPromiseTags: DispatchPromise;
        const pollingMillis = state.config.settingsPollInterval ?? 12000;
        const pollingInterval = setInterval(() => {
          dispatchPromiseStatuses = dispatch(
            fetchStatuses({ isPolling: true })
          );
          dispatchPromiseTags = dispatch(fetchTags({ isPolling: true }));
        }, pollingMillis);

        signal.addEventListener('abort', () => {
          clearInterval(pollingInterval);
          dispatchPromiseStatuses?.abort();
          dispatchPromiseTags?.abort();
        });
      }),
      fetchTags: createThunk(
        async (_: { isPolling?: boolean }, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const tagSchemaId = selectTagSchema(state).id;

          if (!tagSchemaId) {
            throw new Error('no status schema id found');
          }

          const tagsData = await fetchTagsList({
            schemaId: tagSchemaId,
            gql,
          });
          return tagsData;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPolling) {
              state.fetchedTags.status = 'loading';
              state.fetchedTags.tags = [];
            }
          },
          fulfilled: (state, action) => {
            state.fetchedTags.status = 'complete';
            state.fetchedTags.tags = action.payload.tags;
            state.fetchedTags.sdoId = action.payload.sdoId;
          },
          rejected: (state, _action) => {
            // TODO: handle fetch tags failure
            state.fetchedTags.status = 'failure';
            state.fetchedTags.tags = [];
          },
        }
      ),
      saveTags: createThunk(
        async (
          payload: {
            tags: CaseTag[];
            sdoId?: string;
            isAddNew?: boolean;
          },
          thunkAPI
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const tagSchemaId = selectTagSchema(state).id;
          if (!tagSchemaId) {
            throw new Error('no status schema id found');
          }
          const sdoId = await saveTagList({
            tags: payload.tags,
            sdoId: payload.sdoId,
            tagSchemaId,
            userId: state.user.user.userId,
            gql,
          });
          return sdoId;
        },
        {
          pending: (state) => {
            state.saveTags.status = 'loading';
            state.saveTags.id = '';
          },
          fulfilled: (state, action) => {
            const { isAddNew } = action.meta.arg;
            state.saveTags.status = 'complete';
            state.saveTags.id = action.payload.id;
            state.fetchedTags.tags = action.payload.data.tags;
            state.fetchedTags.sdoId = action.payload.id;
            state.rowSelection = {};

            const message = I18nTranslate.TranslateMessage(
              isAddNew ? 'addTagSuccess' : 'saveTagsSuccess'
            );
            enqueueSnackbar(message, { variant: 'success' });
          },
          rejected: (state, action) => {
            const { isAddNew } = action.meta.arg;
            state.saveTags.status = 'failure';
            state.saveTags.id = '';
            state.saveTags.error = action.error.message;

            if (
              action.error.message &&
              action.error.message.startsWith('concurrentModificationError')
            ) {
              state.fetchedTags.sdoId = action.error.message.split('_')[1];
              state.saveTags.status = 'concurrentModificationError';
            } else {
              console.error('failed to create statuses', action.error);
              const message = I18nTranslate.TranslateMessage(
                isAddNew ? 'addTagFailure' : 'saveTagsFailure'
              );
              enqueueSnackbar(message, { variant: 'error' });
            }
          },
        }
      ),
      searchCasesByStatus: createThunk(
        async (status: string[], thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const folderContentTemplateSchemaId =
            state.caseManager.folderContentTemplateSchema.id;
          if (!folderContentTemplateSchemaId) {
            throw new Error('no folder content template schema id found');
          }

          return await searchCases({
            limit: 1000,
            offset: 0,
            statusIds: status,
            sortBy: SortBy.CaseDate,
            sortDirection: 'desc',
            folderContentTemplateSchemaId,
            gql,
          });
        },
        {
          pending: (state) => {
            state.searchCasesByStatus.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.searchCasesByStatus.status = 'complete';
            state.searchCasesByStatus.data = action.payload;
          },
          rejected: (state, action) => {
            state.searchCasesByStatus.status = 'failure';
            console.error('failed to search cases', action.error);
          },
        }
      ),
      updateReassignStatuses: create.reducer(
        (
          state,
          action: PayloadAction<{
            deletedStatusIds: string[];
            reassignStatusId: string;
            cases: CaseSearchResults['searchMedia']['jsondata']['results'];
          }>
        ) => {
          state.reassignStatuses = state.reassignStatuses.concat([
            action.payload,
          ]);
        }
      ),
      updateRowSelection: create.reducer(
        (
          state,
          action: PayloadAction<
            | SettingsSliceState['rowSelection']
            | ((
                prev: SettingsSliceState['rowSelection']
              ) => SettingsSliceState['rowSelection'])
          >
        ) => {
          if (typeof action.payload === 'function') {
            state.rowSelection = action.payload(state.rowSelection);
          } else {
            state.rowSelection = action.payload;
          }
        }
      ),
      updateEditingRows: create.reducer(
        (state, action: PayloadAction<StatusTagRow[]>) => {
          state.editingRows = action.payload;
        }
      ),
    };
  },
  selectors: {
    selectFetchedStatuses: (state) => state.fetchedStatuses.statuses,
    selectFetchedStatusesSdoId: (state) => state.fetchedStatuses.sdoId,
    selectFetchedStatusesStatus: (state) => state.fetchedStatuses.status,
    selectSaveStatusesStatus: (state) => state.saveStatuses.status,
    selectFetchedTags: (state) => state.fetchedTags.tags,
    selectFetchedTagsSdoId: (state) => state.fetchedTags.sdoId,
    selectFetchedTagsStatus: (state) => state.fetchedTags.status,
    selectSaveTagsStatus: (state) => state.saveTags.status,
    selectSearchCases: (state) => state.searchCasesByStatus.data,
    selectSearchCasesStatus: (state) => state.searchCasesByStatus.status,
    selectReassignStatuses: (state) => state.reassignStatuses,
    selectRowSelection: (state) => state.rowSelection,
    selectEditingRows: (state) => state.editingRows,
  },
});

export const {
  saveStatuses,
  fetchStatuses,
  pollingFetchSettingsData,
  fetchTags,
  saveTags,
  searchCasesByStatus,
  updateReassignStatuses,
  updateRowSelection,
  updateEditingRows,
} = settingsSlice.actions;
export const {
  selectFetchedStatuses,
  selectFetchedStatusesStatus,
  selectFetchedStatusesSdoId,
  selectSaveStatusesStatus,
  selectFetchedTags,
  selectFetchedTagsSdoId,
  selectFetchedTagsStatus,
  selectSaveTagsStatus,
  selectSearchCases,
  selectSearchCasesStatus,
  selectReassignStatuses,
  selectRowSelection,
  selectEditingRows,
} = settingsSlice.selectors;

export const { actions: settingsActions, reducer: settingsReducer } =
  settingsSlice;
