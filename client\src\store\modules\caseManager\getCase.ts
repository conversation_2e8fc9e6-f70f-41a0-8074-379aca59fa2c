import { GQLApi } from '@utils/helpers';

export async function getCase(folderId: string, gql: GQLApi) {
  const caseFolder = await gql.getFolder({ folderId });
  if (!caseFolder) {
    throw new Error('no case folder');
  }
  if (!caseFolder.contentTemplates?.[0]?.sdo) {
    throw new Error('no case folder contentTemplates');
  }

  return {
    ...caseFolder.contentTemplates[0].sdo?.data,
    sdoId: caseFolder.contentTemplates[0].sdo?.id,
  };
}
