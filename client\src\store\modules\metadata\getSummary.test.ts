import { describe, expect, it } from 'vitest';
import { pickLatestEngineResult } from './getSummary';

describe('pickLatestEngineResult', () => {
  it('should return the latest engine result', () => {
    const engineResults = [
      {
        tdoId: 'tdoId1',
        engineId: 'engineId1',
        assetId: 'assetId1',
        jsondata: {
          sourceEngineId: 'engineId1',
          modifiedDateTime: 1747258991000,
          summary: 'Summary 1',
        },
      },
      {
        tdoId: 'tdoId1',
        engineId: 'engineId2',
        assetId: 'assetId2',
        jsondata: {
          sourceEngineId: 'engineId2',
          modifiedDateTime: 1747258991002,
          summary: 'Summary 2',
        },
      },
      {
        tdoId: 'tdoId1',
        engineId: 'engineId3',
        assetId: 'assetId3',
        jsondata: {
          sourceEngineId: 'engineId3',
          modifiedDateTime: 1747258991001,
          summary: 'Summary 3',
        },
      },
    ];
    const latestEngineResult = pickLatestEngineResult(engineResults);
    expect(latestEngineResult).toEqual(engineResults[1]);
  });
});
