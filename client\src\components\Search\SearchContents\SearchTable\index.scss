.table-empty {
  height: 600px;
  position: relative;
}

.Sdk-MuiTableContainer-root {
  height: 100%;
}
.empty-table-grouped{
  width: 100%;
  border-radius: 0px 0px 0px 8px;
  padding:0px;
  border-collapse: collapse;
  height: 272px;
  }
.empty-table-ungrouped{
  width: 100%;
  border-radius: 0px 0px 0px 8px;
  padding:0px;
  border-collapse: collapse;
  height: 714px;
  }

.search-result__table {
  flex: 2;
  max-height: 100%;

  &.grouped-view {
    .table-body-wrapper {
      height: 290px;
    }
  }

  &.ungrouped-view {
    .table-body-wrapper {
      height: 714px;
      min-width: 1077px;
    }
  }
}

.search-result__card-view {
  min-width: 1077px;
  height: 100%;
  overflow: hidden;
  background: var(--background-secondary);
  border-radius: 0px 0px 8px 8px;

  .search-result__card-view-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
  }

  .search-result__card-view-content {
    border: 1px solid var(--border-color);
    overflow: hidden;
    border-top: 0px;
    padding: 20px;
    overflow-y: auto;
    border-radius: 0px 0px 8px 8px;
  }
  .search-result__card-view-footer {
    box-shadow: 0px -2px 10px 0px #0000001A; // TODO: Dark Mode
  }
}

.table-cell {
  .search-table__checkbox {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .search-table__file-name {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
    width: 100%;

    &-text {
      position: relative;
      flex: 1;
  
      &::before {
        content: '&nbsp;';
        visibility: hidden;
      }
  
      & > span {
        position: absolute;
        left: 0;
        right: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .description,
  .case-id {
    position: relative;
    width: 100%;

    &::before {
      content: '&nbsp;';
      visibility: hidden;
    }
    
    & > span {
      position: absolute;
      left: 0;
      right: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
