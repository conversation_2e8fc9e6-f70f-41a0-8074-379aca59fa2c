import './index.scss';
import FileTable from '@components/CaseManager/FileTable';
import { useDataDetailsPanel } from '@hooks';
import { I18nTranslate } from '@i18n';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useAppDispatch } from '@store/hooks';
import { selectFiles } from '@store/modules/caseDetail/slice';
import { selectCaseData, selectCases } from '@store/modules/caseManager/slice';
import {
  selectConfig,
  selectEvidenceTypeSchema,
} from '@store/modules/config/slice';
import {
  fetchMetadata,
  selectMetadataByFolderId,
} from '@store/modules/metadata/slice';
import { uploadFile } from '@utils/files';
import {
  updatePendingDeleteFileToLocalStorage,
  updatePendingMoveFileToLocalStorage,
} from '@utils/saveToLocalStorage';
import { noop } from 'lodash';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import CaseDetails from '../CaseDetails';
import CaseTableWrapper from '../CaseDetailTableWrapper';

const CaseDetailsTable = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const intl = I18nTranslate.Intl();
  const cases = useSelector(selectCases);
  const files = useSelector(selectFiles);
  const { registryIds } = useSelector(selectConfig);
  const [selected, setSelected] = useState('');
  const selectedCase = useSelector(selectCaseData);
  const evidenceTypeSchemaId = useSelector(selectEvidenceTypeSchema).id;
  const metadataByFolderId = useSelector(selectMetadataByFolderId);
  const { selectedFolderId, tdoId } = useParams();
  const [pendingMoveFileIds, setPendingMoveFileIds] = useState<
    { fileId: string; newFolderId: string; oldFolderId: string }[]
  >([]);
  const [pendingDeleteFileIds, setPendingDeleteFileIds] = useState<string[]>(
    []
  );

  useEffect(() => {
    try {
      const validDeleteFileTdoIds = updatePendingDeleteFileToLocalStorage();
      setPendingDeleteFileIds(validDeleteFileTdoIds.map((item) => item.value));
    } catch (e) {
      console.error('Unable to update deleted file tdoIds to local storage', e);
    }

    try {
      const validMoveFileTdoIds = updatePendingMoveFileToLocalStorage();
      setPendingMoveFileIds(validMoveFileTdoIds.map((item) => item.value));
    } catch (e) {
      console.error('Unable to update moved file tdoIds to local storage', e);
    }
  }, [files]);

  useEffect(() => {
    if (
      selectedFolderId &&
      evidenceTypeSchemaId &&
      (metadataByFolderId?.[selectedFolderId] === undefined ||
        metadataByFolderId[selectedFolderId]?.status === 'idle')
    ) {
      dispatch(
        fetchMetadata({ folderId: selectedFolderId, evidenceTypeSchemaId })
      );
    }
  }, [selectedFolderId, evidenceTypeSchemaId, metadataByFolderId, dispatch]);

  const handleSelect = (selectedId: string) => {
    setSelected(selectedId);

    const selectedCase = cases.data.results.find(
      (result) => result.id === selectedId
    );
    if (selectedCase) {
      setSelectedFolderId(selectedCase.folderId);
    }
  };

  const handleDoubleClick = (tdoId: string) => {
    navigate(`/case-manager/${selectedFolderId}/data-details/${tdoId}`);
  };

  const handleBackClick = () => {
    navigate('/case-manager');
  };

  const handleUploadFile = () => {
    uploadFile({
      selectedFolderId,
      selectedCaseId: selectedCase?.caseId,
      registryId: registryIds?.caseRegistryId,
      intl,
    });
  };

  useDataDetailsPanel({
    tdoId,
    navigationPath: `/case-manager/${selectedFolderId}`,
  });

  const setSelectedFolderId = (folderId: string) => {
    navigate(`/case-manager/${folderId}`);
  };

  return (
    <div className="case-details-table">
      <div className="case-details-table__header">
        <div
          style={{ cursor: 'pointer' }}
          className="case-details-table__header__title"
          onClick={handleBackClick}
        >
          <ArrowBackIcon sx={{ width: 14, height: 14 }} />
          {I18nTranslate.TranslateMessage('backToCaseManager')}
        </div>
        <div className="case-details-table__header__case-detail">
          {I18nTranslate.TranslateMessage('caseDetail')}
        </div>
      </div>
      <div className="case-details-table__content">
        <CaseTableWrapper
          caseSubtitle={selectedCase?.caseId || ''}
          data-testid="case-details-wrapper"
        >
          <FileTable
            selected={selected}
            handleSelect={handleSelect}
            handleDoubleClick={(tdoId) => handleDoubleClick(tdoId)}
            handleUploadFile={handleUploadFile}
            setSelectedFolderId={setSelectedFolderId}
            selectedFolderId={selectedFolderId ?? ''}
            setSelected={setSelected}
            pendingDeleteIds={pendingDeleteFileIds}
            setPendingDeleteIds={setPendingDeleteFileIds}
            pendingMoveFileIds={pendingMoveFileIds}
            classname="file__table"
          />
        </CaseTableWrapper>
        <CaseDetails
          folderId={selectedFolderId}
          showCaseFiles
          onShowCaseFiles={noop}
        />
      </div>
    </div>
  );
};

export default CaseDetailsTable;
