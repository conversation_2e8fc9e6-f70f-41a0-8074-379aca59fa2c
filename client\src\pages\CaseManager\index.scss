.case-manager {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 10px;
  margin-right: 15px;
  margin-bottom: 15px;
  flex-direction: column;

  .case-manager__header {
    gap: 15px;
    height: 52px;
    display: flex;
    margin-left: 21px;
    align-items: center;
    padding-bottom: 26px;

    .case-manager__title {
      gap: 10px;
      display: flex;
      font-size: 14px;
      align-items: center;
      color: var(--text-primary);
    }

    .case-manager__count {
      width: 38px;
      height: 26px;
      display: flex;
      font-size: 12px;
      border-radius: 90px;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      background: var(--chip-background-grey);
    }
  }

  .case-manager__content {
    flex: 1;
    gap: 15px;
    display: flex;
    overflow: auto;
    flex-direction: row;
  }

  .case-manager__table {
    flex: 2;
    max-height: 100%;
  }
}
