import { StatusTagRow, Visibility } from './slice';

export const getNewRows = <
  T extends {
    id: string;
    label: string;
    active: boolean;
    color?: string;
  },
>({
  currData,
  newData,
}: {
  currData: T[];
  newData: T[];
}): StatusTagRow[] => {
  const currDataMap = new Set(currData.map((item) => item.id));
  const newRows: StatusTagRow[] = [];

  for (const { id, label, color, active } of newData) {
    if (!currDataMap.has(id)) {
      newRows.push({
        id,
        name: label,
        color,
        visibility: active ? Visibility.Active : Visibility.Inactive,
      });
    }
  }

  return newRows;
};
