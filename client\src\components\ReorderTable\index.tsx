import './index.scss';
import { DeleteWithLines } from '@assets/icons';
import { I18nTranslate } from '@i18n';
import {
  Check as CheckIcon,
  ErrorOutlined as ErrorOutlinedIcon,
} from '@mui/icons-material';
import {
  Button,
  CheckboxProps,
  CircularProgress,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { OptionsType } from '@pages/Settings';
import CellColorPicker from '@pages/Settings/CellColorPicker';
import DeleteDialog from '@pages/Settings/DeleteDialog';
import { useAppDispatch } from '@store/hooks';
import {
  searchCasesByStatus,
  selectRowSelection,
  selectSaveStatusesStatus,
  selectSaveTagsStatus,
  selectSearchCasesStatus,
  StatusTagRow,
  updateRowSelection,
  Visibility,
} from '@store/modules/settings/slice';
import cn from 'classnames';
import { omit } from 'lodash';
import {
  MaterialReactTable,
  MRT_Cell,
  MRT_ColumnDef,
  MRT_Row,
} from 'material-react-table';
import { SVGProps, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { JSX } from 'react/jsx-runtime';

interface errorEditingRowInfo {
  rowId: string;
  error: string;
}

export interface ReorderTableProps {
  type?: 'Status' | 'Tag';
  className?: string;
  data: StatusTagRow[];
  editMode?: boolean;
  onChange?: (data: StatusTagRow[]) => void;
  isLoading: boolean;
  colorRow?: boolean;
  deleteRow: StatusTagRow | null;
  setDeleteRow: (row: StatusTagRow | null) => void;
  emptyState: JSX.Element;
  validateName: (newTagName: string, currentId?: string) => string;
  setUnsavedChanges: (unsaved: boolean) => void;
}

const ReorderTable = (props: ReorderTableProps) => {
  const {
    className,
    onChange,
    isLoading,
    colorRow,
    deleteRow,
    setDeleteRow,
    emptyState,
    validateName,
    setUnsavedChanges,
  } = props;
  let { data, editMode, type } = props;
  data = data ?? [];
  editMode = editMode ?? false;
  type = type ?? 'Status';

  const dispatch = useAppDispatch();
  const intl = I18nTranslate.Intl();

  const [draggingRow, setDraggingRow] = useState<MRT_Row<StatusTagRow> | null>(
    null
  );
  const [hoveredRow, setHoveredRow] = useState<Partial<
    MRT_Row<StatusTagRow>
  > | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [editingErrors, setEditingErrors] = useState<errorEditingRowInfo[]>([]);

  const saveTagsStatus = useSelector(selectSaveTagsStatus);
  const saveStatusStatus = useSelector(selectSaveStatusesStatus);
  const searchCasesStatus = useSelector(selectSearchCasesStatus);
  const rowSelection = useSelector(selectRowSelection);

  useEffect(() => {
    if (saveTagsStatus === 'complete' || saveStatusStatus === 'complete') {
      setDeleteDialogOpen(false);
    }
  }, [saveTagsStatus]);

  useEffect(() => {
    const newRows = data?.map((row) => ({
      ...row,
      selected: rowSelection[row.id],
    }));
    onChange?.(newRows);
  }, [rowSelection]);

  useEffect(() => {
    dispatch(updateRowSelection({}));
    setEditingErrors([]);
  }, [editMode]);

  const handleEditingError = (error: string, rowId: string) => {
    const existingError = editingErrors.find((err) => err.rowId === rowId);
    let newErrors = [...editingErrors];

    if (!existingError && error.length > 0) {
      // Add new error to the list
      newErrors = [
        ...editingErrors,
        {
          rowId,
          error,
        },
      ];
      setEditingErrors(newErrors);
      return;
    }

    // Remove the error if it is empty
    if (existingError && error.length === 0) {
      newErrors = editingErrors.filter((err) => err.rowId !== rowId);
      setEditingErrors(newErrors);
      return;
    }

    // Update the error with the new message
    if (existingError) {
      newErrors = editingErrors.map((err) =>
        err.rowId === rowId && error.length > 0 ? { ...err, error } : err
      );
    }
    setEditingErrors(newErrors);
  };

  const columnsStatus: MRT_ColumnDef<StatusTagRow>[] = [
    ...(colorRow
      ? [
          {
            accessorKey: 'color',
            header: intl.formatMessage({ id: 'color' }),
            muiTableHeadCellProps: {
              className: 'color-header',
            },
            maxSize: 85,
            size: 85,
            grow: false,
            Cell: ({
              cell,
              row,
            }: {
              cell: MRT_Cell<StatusTagRow>;
              row: MRT_Row<StatusTagRow>;
            }) => (
              <CellColorPicker
                color={cell.getValue<string>()}
                className="reorder-table__color-picker"
                disableEdit={!editMode}
                onChange={(color) => {
                  const rowRef = data.find((d) => d.id === row.id);
                  if (rowRef) {
                    rowRef.color = color;
                    onChange?.(data);
                  }
                }}
              />
            ),
          },
        ]
      : []),
    {
      accessorKey: 'name',
      header:
        type === 'Status'
          ? intl.formatMessage({ id: 'statusName' })
          : intl.formatMessage({ id: 'tagName' }),
      muiTableHeadCellProps: {
        className: !editMode ? `${type}-name-header` : undefined,
      },
      muiTableBodyCellProps: {
        className: !editMode ? `${type}-name-body` : undefined,
      },
      minSize: 225,
      Cell: ({ renderedCellValue, cell, row }) => {
        if (editMode) {
          const value = cell.getValue<string>();
          // find out if the row is in editingErrors
          let isError = editingErrors.some((error) => error.rowId === row.id);
          const rowRef = data.find((d) => d.id === row.id);

          return (
            <TextField
              data-testid="reorder-table-name-edit"
              error={isError}
              value={value}
              className={cn({ 'cell-deleted': rowRef?.isDeleted })}
              slotProps={
                isError
                  ? {
                      input: {
                        endAdornment: (
                          <InputAdornment position="end">
                            <ErrorOutlinedIcon className="reorder-table__error-icon" />
                          </InputAdornment>
                        ),
                      },
                    }
                  : {}
              }
              onChange={(event) => {
                const value = event.target.value;
                const validationError: string =
                  type === 'Status'
                    ? validateName(value, row.id)
                    : validateName(value);
                if (validationError) {
                  handleEditingError(validationError, row.id);
                  isError = true;
                } else {
                  handleEditingError('', row.id);
                  isError = false;
                }
                if (rowRef) {
                  if (isError) {
                    rowRef.error = true;
                  }
                  rowRef.name = value;
                  onChange?.(data);
                  if (isError || editingErrors.length > 0) {
                    setUnsavedChanges(false);
                  }
                }
              }}
            />
          );
        } else {
          return renderedCellValue;
        }
      },
    },
    {
      accessorKey: 'visibility',
      header: intl.formatMessage({ id: 'visibility' }),
      Cell: ({ cell, row }) => {
        if (editMode) {
          const rowRef = data.find((d) => d.id === row.id);
          return (
            <Select
              value={cell.getValue<StatusTagRow['visibility']>()}
              className="reorder-table__visibility-select"
              data-testid="reorder-table-visibility-edit"
              MenuProps={{
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
                transformOrigin: {
                  vertical: 'top',
                  horizontal: 'left',
                },
                slotProps: {
                  paper: { className: 'reorder-table__visibility-menu' },
                },
              }}
              onChange={(event) => {
                const value = event.target.value;
                if (rowRef) {
                  rowRef.visibility = value as StatusTagRow['visibility']; // Think this needs the type cast
                  onChange?.(data);
                }
              }}
              renderValue={(value) => renderVisibilityLabel(value)}
            >
              <MenuItem value="Active" className="menu-item">
                {rowRef?.visibility === Visibility.Active ? (
                  <CheckIcon />
                ) : (
                  <div />
                )}
                <span>{intl.formatMessage({ id: 'active' })}</span>
              </MenuItem>
              <MenuItem value="Inactive" className="menu-item">
                {rowRef?.visibility === Visibility.Inactive ? (
                  <CheckIcon />
                ) : (
                  <div />
                )}
                <span>{intl.formatMessage({ id: 'inactive' })}</span>
              </MenuItem>
            </Select>
          );
        } else {
          return renderVisibilityLabel(
            cell.getValue<StatusTagRow['visibility']>()
          );
        }
      },
    },
    {
      accessorKey: 'errorRow',
      header: '',
      maxSize: 300,
      size: 300,
      grow: false,
      Cell: ({ row }) => {
        const isError = editingErrors.find((err) => row.id === err.rowId);
        return isError && <div className="error-column">{isError.error}</div>;
      },
    },
  ];

  const renderRowActions = ({ row }: { row: MRT_Row<StatusTagRow> }) => {
    const isSearchCasesLoading =
      row.id === deleteRow?.id && searchCasesStatus === 'loading';

    const handleClickDelete = () => {
      if (!editMode) {
        return;
      }
      const rowRef = data.find((d) => d.id === row.id);
      if (rowRef) {
        setDeleteRow(rowRef);
        if (type === 'Tag') {
          setDeleteDialogOpen(true);
        } else {
          dispatch(searchCasesByStatus([rowRef.id])).then(() =>
            setDeleteDialogOpen(true)
          );
        }
      }
    };

    return [
      <Button
        key={1}
        sx={{ height: 30.25 }}
        onClick={handleClickDelete}
        data-testid="reorder-table__remove-button"
      >
        {isSearchCasesLoading ? (
          <CircularProgress size={20} />
        ) : (
          <DeleteWithLines />
        )}
      </Button>,
    ];
  };

  const handleRemoveRowSelection = () => {
    if (deleteRow && rowSelection[deleteRow.id]) {
      dispatch(updateRowSelection(omit(rowSelection, deleteRow.id)));
    }
  };

  const handleDeleteRow = () => {
    onChange?.(
      data.map((row) => {
        if (row.id === deleteRow?.id) {
          return {
            ...row,
            isDeleted: true,
          };
        }
        return row;
      })
    );
  };

  const renderVisibilityLabel = (visibilityStatus: Visibility) =>
    visibilityStatus === Visibility.Active
      ? intl.formatMessage({ id: 'active' })
      : intl.formatMessage({ id: 'inactive' });

  return (
    <div
      className={cn('reorder-table', className, {
        'edit-mode': editMode,
        'not-color-row': !colorRow,
      })}
      data-testid="reorder-table"
    >
      {isLoading && (
        <div className="is-loading">
          <CircularProgress data-testid="reorder-table-loading" />
        </div>
      )}
      {!isLoading && !data.length && emptyState}
      {!isLoading && data.length ? (
        <MaterialReactTable
          initialState={{
            columnPinning: { right: ['mrt-row-actions'] },
          }}
          state={{
            columnOrder: editMode
              ? [
                  'mrt-row-drag',
                  'mrt-row-select',
                  'color',
                  'name',
                  'visibility',
                ]
              : ['mrt-row-numbers', 'color', 'name', 'visibility'],
            draggingRow,
            hoveredRow,
            rowSelection,
          }}
          positionActionsColumn="last"
          renderRowActions={renderRowActions}
          enableRowActions={editMode}
          onRowSelectionChange={(selectedRows) =>
            dispatch(updateRowSelection(selectedRows))
          }
          muiTableBodyRowProps={(row) => {
            const rowRef = data.find((d) => d.id === row.row.id);
            return {
              className: cn({
                'row-deleted': rowRef?.isDeleted,
              }),
              'data-testid': `reorder-table-row-${row.row.id}`,
            };
          }}
          muiTableBodyCellProps={(row) => {
            const rowRef = data.find((d) => d.id === row.row.id);
            return {
              className: cn({
                'cell-deleted': rowRef?.isDeleted,
              }),
            };
          }}
          autoResetPageIndex={false}
          columns={columnsStatus}
          data={data}
          enableRowNumbers={!editMode}
          enableRowOrdering={editMode}
          enableRowSelection={editMode}
          enableColumnActions={false}
          editDisplayMode="row"
          displayColumnDefOptions={{
            'mrt-row-numbers': {
              grow: false,
              maxSize: 50,
              size: 50,
              enableResizing: true, // Enabled resizing but disabled in CSS, so it's not actually resizable
            },
            'mrt-row-select': {
              grow: false,
              size: 60,
              enableResizing: true, // Enabled resizing but disabled in CSS, so it's not actually resizable
            },
            'mrt-row-drag': {
              grow: false,
              size: 50,
              enableResizing: true, // Enabled resizing but disabled in CSS, so it's not actually resizable
            },
            'mrt-row-actions': {
              grow: false,
              size: 100,
              enableResizing: true, // Enabled resizing but disabled in CSS, so it's not actually resizable
            },
          }}
          enableSorting={false}
          getRowId={(row) => row.id}
          enableTopToolbar={false}
          enableBottomToolbar={false}
          enablePagination={false}
          enableColumnResizing
          muiSelectCheckboxProps={
            {
              'data-testid': 'row-check-box',
            } as CheckboxProps
          }
          muiRowDragHandleProps={{
            onDragEnd: () => {
              if (hoveredRow && draggingRow) {
                data.splice(
                  hoveredRow?.index ?? 0,
                  0,
                  data.splice(draggingRow.index, 1)[0]
                );
                onChange?.(data);
              }
            },
          }}
          icons={{
            DragHandleIcon: (
              props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>
            ) => (
              <svg
                className="reorder-table__drag-handle"
                data-testid="reorder-table-drag-handle"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="red"
                {...props}
              >
                <path
                  d="M7 19V17H9V19H7ZM11 19V17H13V19H11ZM15 19V17H17V19H15ZM7 15V13H9V15H7ZM11 15V13H13V15H11ZM15 15V13H17V15H15ZM7 11V9H9V11H7ZM11 11V9H13V11H11ZM15 11V9H17V11H15ZM7 7V5H9V7H7ZM11 7V5H13V7H11ZM15 7V5H17V7H15Z"
                  fill="#212121"
                />
              </svg>
            ),
          }}
          onDraggingRowChange={setDraggingRow}
          onHoveredRowChange={setHoveredRow}
        />
      ) : null}
      <DeleteDialog
        open={deleteDialogOpen}
        rows={data}
        selectedTab={type === 'Tag' ? OptionsType.tag : OptionsType.status}
        singleRow={deleteRow}
        onClose={() => {
          setDeleteDialogOpen(false);
          setDeleteRow(null);
        }}
        removeSelectedRow={handleRemoveRowSelection}
        handleDeleteRows={handleDeleteRow}
      />
    </div>
  );
};

export default ReorderTable;
