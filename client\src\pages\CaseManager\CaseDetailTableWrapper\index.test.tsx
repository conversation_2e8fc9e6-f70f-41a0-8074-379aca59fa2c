import { act, screen, waitFor } from '@testing-library/react';
import {
  dispatchCustomEvent,
  eventHistory,
} from '@utils/helpers/dispatchCustomEvent';
import { describe, expect, it } from 'vitest';
import CaseTableWrapper from '.';
import { render } from '../../../../test/render';

/* eslint-disable formatjs/no-literal-string-in-jsx */

describe('CaseTableWrapper', () => {
  it('should show bulk actions once enabled', async () => {
    render(
      <CaseTableWrapper caseSubtitle="caseSubtitle">
        {' '}
        <div />{' '}
      </CaseTableWrapper>
    );

    expect(
      screen.queryByTestId('case-subtitle-move-button')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('case-subtitle-delete-button')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('case-subtitle-filter-button')
    ).toBeInTheDocument();

    act(() => {
      dispatchCustomEvent('enable-bulk-actions', { isEnabled: true });
    });

    await waitFor(() => {
      expect(
        screen.queryByTestId('case-subtitle-move-button')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('case-subtitle-delete-button')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('case-subtitle-filter-button')
      ).toBeInTheDocument();
    });
  });

  it('should create events when move and delete buttons are clicked', async () => {
    render(
      <CaseTableWrapper caseSubtitle="caseSubtitle">
        {' '}
        <div />{' '}
      </CaseTableWrapper>
    );

    act(() => {
      dispatchCustomEvent('enable-bulk-actions', { isEnabled: true });
    });

    await waitFor(() => {
      expect(
        screen.queryByTestId('case-subtitle-move-button')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('case-subtitle-delete-button')
      ).toBeInTheDocument();
    });

    act(() => {
      screen.getByTestId('case-subtitle-move-button')?.click();
      screen.getByTestId('case-subtitle-delete-button')?.click();
    });

    await waitFor(() => {
      const events = eventHistory.map(({ eventName }) => eventName);
      expect(events).toContain('move-all-selected-files');
      expect(events).toContain('delete-all-selected-files');
    });
  });
});
