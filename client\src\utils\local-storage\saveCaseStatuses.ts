import { z } from 'zod';

const caseStatusSchema = z
  .object({
    id: z.string().nonempty(),
    statusId: z.string().optional(),
    expiresAt: z.number(),
  })
  .strict();

const caseStatusesArraySchema = z.array(caseStatusSchema);

export interface CaseStatus {
  id: string;
  statusId?: string;
  expiresAt: number;
}

const LOCAL_STORAGE_KEY = 'caseStatuses';

export const getCaseStatuses = (): CaseStatus[] => {
  const caseStatusesResult: CaseStatus[] = [];
  try {
    const caseStatuses: unknown = JSON.parse(
      localStorage.getItem(LOCAL_STORAGE_KEY) || '[]'
    );
    const validationResult = caseStatusesArraySchema.safeParse(caseStatuses);
    return validationResult.success
      ? validationResult.data
      : caseStatusesResult;
  } catch {
    return caseStatusesResult;
  }
};

export const setCaseStatuses = (statuses: CaseStatus[]): void => {
  localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(statuses));
};

export const cleanUpExpiredStatuses = (): CaseStatus[] => {
  const now = Date.now();
  const validStatuses = getCaseStatuses().filter(
    (status) => status.expiresAt > now
  );

  setCaseStatuses(validStatuses);

  return validStatuses;
};

export const addCaseStatusToLocalStorage = (newStatus: CaseStatus): void => {
  const statuses = cleanUpExpiredStatuses();
  const existingStatusIndex = statuses.findIndex(
    (status) => status.id === newStatus.id
  );
  if (existingStatusIndex !== -1) {
    const update = {
      id: statuses[existingStatusIndex].id,
      statusId: newStatus.statusId,
      expiresAt: newStatus.expiresAt,
    };

    statuses[existingStatusIndex] = update;
  } else {
    statuses.push(newStatus);
  }
  setCaseStatuses(statuses);
};
