const HIDE_IF_NO_RESULTS_KEY = 'investigate-hide-if-no-results';

const DEFAULT_HIDE_IF_NO_RESULTS = true;

export const getHideIfNoResultsLocalStorage = () => {
  const storage = localStorage.getItem(HIDE_IF_NO_RESULTS_KEY);
  let hideIfNoResults = DEFAULT_HIDE_IF_NO_RESULTS;

  try {
    // TODO: Type of hideIfNoResults should be validated
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    hideIfNoResults = storage
      ? JSON.parse(storage)
      : DEFAULT_HIDE_IF_NO_RESULTS;
  } catch {
    hideIfNoResults = DEFAULT_HIDE_IF_NO_RESULTS;
  }

  return hideIfNoResults;
};

export const setHideIfNoResultsLocalStorage = (value: boolean) => {
  localStorage.setItem(HIDE_IF_NO_RESULTS_KEY, String(value));
};
