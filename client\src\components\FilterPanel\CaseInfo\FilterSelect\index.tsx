import './index.scss';
import {
  Box,
  InputAdornment,
  Select as MuiSelect,
  SelectProps,
  styled,
} from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';
import { CaseStatus, FileStatus } from '@shared-types/types';
import { Close } from '@assets/icons';

interface Props {
  width: number;
  children: React.ReactNode;
  name: string;
  isCaseDetail?: boolean;
  statuses: CaseStatus[] | FileStatus[];
}

type FilterSelectProps = SelectProps & { width: number };

const Select = styled(MuiSelect)<FilterSelectProps>(({ width }) => ({
  width: `${width}px`,
}));

const FilterSelect = ({
  width,
  children,
  name,
  isCaseDetail,
  statuses,
}: Props) => {
  const { control, setValue } = useFormContext();
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Select
          {...field}
          className="select"
          MenuProps={{
            className: 'select-menu',
          }}
          width={width}
          disabled={isCaseDetail}
          data-testid={name}
          renderValue={(selected) => {
            const selectedOption = statuses.find(
              (status) => selected === status.id
            );

            return (
              <Box
                className="select__value"
                display="flex"
                alignItems="center"
                justifyContent="space-between"
              >
                <div>{selectedOption ? selectedOption.label : ''}</div>
                {((isCaseDetail && name === 'fileStatus') || !isCaseDetail) && (
                  <InputAdornment
                    position="end"
                    onMouseDown={(e) => {
                      e.stopPropagation();
                      setValue(name, ''); // Clear the select value
                    }}
                    data-testid={`filter-select-clear-${name}`}
                  >
                    <Close className="filter-select__clear-icon" />
                  </InputAdornment>
                )}
              </Box>
            );
          }}
        >
          {children}
        </Select>
      )}
    />
  );
};

export default FilterSelect;
