import './index.scss';
import { Calendar, Close } from '@assets/icons';
import { filterFormDefaultValues } from '@components/FilterPanel';
import { I18nTranslate } from '@i18n';
import { Box, Button, Popover, TextField } from '@mui/material';
import { FilterFormValue } from '@shared-types/types';
import { selectSearchFiles } from '@store/modules/search/slice';
import { generateDateString } from '@utils/genDateString';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { DateRange, Range } from 'react-date-range';
import { useFormContext } from 'react-hook-form';
import { useSelector } from 'react-redux';

interface Props {
  name: string;
}

const initRanges = [
  {
    startDate: undefined,
    endDate: undefined,
    key: 'selection',
  },
];

const genRangeDisplay = (
  startDate: Date = new Date(),
  endDate: Date = new Date()
) =>
  `${dayjs(startDate).format('MM/DD/YYYY')} - ${dayjs(endDate).format('MM/DD/YYYY')}`;

const FilterDate = ({ name }: Props) => {
  const { setValue, getValues } = useFormContext<FilterFormValue>();
  const searchFiles = useSelector(selectSearchFiles);

  // If the searchParams from state change, update the date range based on the new state.
  useEffect(() => {
    const theStartDate = searchFiles.searchParams?.uploadDateFilter?.startDate;
    const theEndDate = searchFiles.searchParams?.uploadDateFilter?.endDate;

    if (theStartDate && theEndDate) {
      const newStartDate = new Date(theStartDate);
      const newEndDate = new Date(theEndDate);
      setRangeValue(genRangeDisplay(newStartDate, newEndDate));
      setRanges([
        {
          startDate: newStartDate,
          endDate: newEndDate,
          key: 'selection',
        },
      ]);
    } else {
      setRangeValue('');
      setRanges(initRanges);
    }
  }, [searchFiles.searchParams]);

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [ranges, setRanges] = useState<Range[]>(initRanges);
  const [rangeValue, setRangeValue] = useState<string>('');

  const handleClearClick = (e: React.MouseEvent<SVGSVGElement>) => {
    e.stopPropagation();
    setRangeValue('');
    setRanges(initRanges);
    if (isKeyofFormValue(name)) {
      setValue(name, filterFormDefaultValues.uploadDate, { shouldDirty: true });
    }
  };

  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChangeDate = (ranges: Range) => {
    setRanges([ranges]);
  };

  const isKeyofFormValue = (key: string): key is keyof FilterFormValue =>
    key in getValues();

  const handleConfirm = () => {
    const { startDate, endDate } = ranges[0];
    if (!startDate || !endDate || !isKeyofFormValue(name)) {
      return;
    }
    setRangeValue(genRangeDisplay(startDate, endDate));
    setValue(
      name,
      {
        startDate: generateDateString(startDate, true),
        endDate: generateDateString(endDate, true),
      },
      { shouldDirty: true }
    );
    handleClose();
  };

  return (
    <>
      <TextField
        data-testid="filter-date-range"
        value={rangeValue}
        onClick={handleOpen}
        fullWidth
        slotProps={{
          input: {
            readOnly: true,
            endAdornment: (
              <Box display="flex" alignItems="center" gap="6px">
                {rangeValue && (
                  <Close onClick={handleClearClick} className="clear-icon" />
                )}
                <Calendar />
              </Box>
            ),
            className: 'text-field',
          },
        }}
      />

      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        slotProps={{
          paper: {
            sx: {
              display: 'flex',
              flexDirection: 'column',
            },
          },
        }}
      >
        <DateRange
          data-testid="date-range"
          ranges={ranges}
          onChange={(ranges) => handleChangeDate(ranges.selection)}
          showDateDisplay={false}
        />
        <Button
          fullWidth
          variant="contained"
          className="confirm-button"
          onClick={handleConfirm}
          data-testid="confirm-button-date"
        >
          {I18nTranslate.TranslateMessage('apply')}
        </Button>
      </Popover>
    </>
  );
};

export default FilterDate;
