import './index.scss';
import { CaseDetailsTable, CaseManager, Search, Settings } from '@pages/index';
import {
  createBrowserRouter,
  createRoutesFromElements,
  Navigate,
  Route,
  RouterProvider,
} from 'react-router';
import Layout from './Layout';

const App = () => {
  const router = createBrowserRouter(
    createRoutesFromElements(
      <Route path="/" element={<Layout />}>
        <Route index element={<Navigate to="/case-manager" replace />} />
        <Route path="/search" element={<Search />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/case-manager" element={<CaseManager />} />
        <Route
          path="/case-manager/:selectedFolderId"
          element={<CaseDetailsTable />}
        />
        <Route
          path="/case-manager/:selectedFolderId/data-details/:tdoId"
          element={<CaseDetailsTable />}
        />
        <Route path="/search/data-details/:tdoId" element={<Search />} />
        <Route path="*" element={<Navigate to="/case-manager" replace />} />
      </Route>
    )
  );

  return <RouterProvider router={router} />;
};

export default App;
