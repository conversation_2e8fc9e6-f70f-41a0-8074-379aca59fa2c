export const CASES_FOLDER_KEY = 'investigate-folderIds';
export const EDIT_CASE_KEY = 'investigate-editedCases';
export const EDIT_FILE_KEY = 'investigate-editedFileIds';

export function setLocalStorage<T>(key: string, value: T) {
  const now = new Date();
  const ttl = 60 * 60 * 1000;
  const item = {
    value,
    expiry: now.getTime() + ttl,
  };

  const itemStr = JSON.stringify(item);
  localStorage.setItem(key, itemStr);
}

export function getLocalStorage<T>(key: string): T | null {
  const now = new Date();
  try {
    const item = localStorage.getItem(key);
    if (!item) {
      return null;
    }

    // TODO: Type of parsed item should be validated
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const parsedItem: {
      value: T;
      expiry: number;
    } = JSON.parse(item);
    if (parsedItem && now.getTime() > parsedItem.expiry) {
      localStorage.removeItem(key);
      return null;
    }

    return parsedItem.value;
  } catch (error) {
    console.error(error);
    return null;
  }
}
