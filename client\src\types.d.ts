interface Window {
  config: {
    [key: string]: string | number | boolean | object | undefined;
    apiRoot: string;
    graphQLEndpoint: string;
    veritoneAppId: string;
    registryIds: {
      caseRegistryId: string;
      statusRegistryId: string;
      tagRegistryId: string;
      evidenceTypeRegistryId?: string;
    };
    nodeEnv: string;
    sentry?: {
      tracing: string;
      DSN: string;
    };
    sentryDSN?: string;
    settingsPollInterval?: number;
    caseStatusTagsPollInterval?: number;
    casePollingInterval?: number;
    filePollingInterval?: number;
    aiwareJSPath?: string;
    aiwareJSVersion?: string;
  };
  buildDetails: {
    hash?: string;
    date?: string;
    message?: string;
  };

  aiware?: {
    init: (config: unknown, cb?: () => void) => void;
    mountPanel: (config: unknown) => void;
    unmountPanel: (panelId: string) => void;
    mountWidget: (config: unknown, cb?: () => void) => string;
    unmountWidget: (id: string) => void;
    auth?: {
      reportAppActivity?: () => void;
    };
    on: (event: string, cb: (error: Error, data: unknown) => void) => void;
    off: (event: string) => void;
    store: {
      getState: () => {
        auth?: {
          user?: {
            preferredLanguage?: string;
          };
        };
      };
    };
  };
  isAiwareInitialized?: boolean;
  intercomSettings?: unknown;
  Intercom?: (
    event: 'trackEvent',
    name: string,
    metadata?: Record<string, unknown>
  ) => void;
  chatWithSupport?: () => void;
  openSalesforceChatWindow?: () => void;
  readonly __REDUX_DEVTOOLS_EXTENSION_COMPOSE__: unknown;
  Cypress?: boolean;
  store?: typeof store;
}

type DispatchPromise =
  | (Promise<
      // TODO: Fix this type
      // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
      | PayloadAction<
          unknown,
          string,
          {
            arg: unknown;
            requestId: string;
            requestStatus: 'rejected';
            aborted: boolean;
            condition: boolean;
          } & (
            | { rejectedWithValue: true }
            | ({ rejectedWithValue: false } & {})
          ),
          SerializedError
        >
      // TODO: Fix type
      // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
      | PayloadAction<
          unknown,
          string,
          { arg: unknown; requestId: string; requestStatus: 'fulfilled' },
          never
        >
    > & { __linterBrands: 'SafePromise' } & {
      abort: (reason?: string) => void;
      requestId: string;
      arg: unknown;
      unwrap: () => Promise<unknown>;
    })
  | undefined;

declare module '*.svg';
declare module '*.css';
declare module '*.scss';

// TODO Update these as restructuring continues
declare module '@fontsource/*';
