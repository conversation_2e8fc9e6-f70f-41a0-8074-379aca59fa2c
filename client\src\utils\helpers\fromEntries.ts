/* eslint-disable @typescript-eslint/no-explicit-any */
type Entry<K extends string | number | symbol, V> = readonly [K, V];

type FromEntries<T extends Entry<string | number | symbol, any>> = {
  [K in T[0]]: Extract<T, [K, any]>[1];
};

export function fromEntries<T extends Entry<string | number | symbol, any>>(
  entries: T[]
): FromEntries<T> {
  return Object.fromEntries(entries) as FromEntries<T>;
}
