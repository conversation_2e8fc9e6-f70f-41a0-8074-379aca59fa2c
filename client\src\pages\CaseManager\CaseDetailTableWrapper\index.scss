.case-table-wrapper {
  flex: 2;
  display: flex;
  border-radius: 8px;
  flex-direction: column;
  background: var(--background-secondary);
}

.file-table-wrapper {
  flex: 1;
  padding: 0;
  height: 100%;

  .table-container {
    margin: 0 30px;
    width: auto;
  }

  .table-content {
    border-radius: 0;
    background-color: var(--background-secondary);
  }
}

.table-header__filter-list-icon {
  color: var(--text-primary);
}

.case-subtitle__container {
  border-radius: 8px;
}

.case-subtitle__content {
  display: flex;
  align-items: center;
  gap: 18px;

  &-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-primary);
  }

  &-file-count {
    font-weight: normal;
    font-size: 13px;
    color: var(--text-primary);
  }
}

.case-subtitle__header {
  font-size: small;
}

.case-subtitle__button {
  color: var(--text-primary) !important;

  svg {
    transition: none;
    fill: var(--text-primary) !important;
  }
}

.case-subtitle__sort-button {
  width: 36px;
  height: 36px;
}

.case-subtitle__sort-icon {
  cursor: pointer;

  & > path {
    fill: var(--text-primary);
  }
}
