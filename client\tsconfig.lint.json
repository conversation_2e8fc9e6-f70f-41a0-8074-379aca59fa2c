{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2023", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-jsx", "strict": true, "noEmit": true, "skipLibCheck": true, "esModuleInterop": true, "isolatedModules": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true}, "include": ["src/**/*", "test/**/*", "vite.config.ts"], "exclude": ["dist", "node_modules"]}