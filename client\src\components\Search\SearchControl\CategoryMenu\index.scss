.category-menu {
  .category-menu__table-header,
  .category-menu__table-row {
    &.no-row-ordering {
      .Sdk-MuiCheckbox-root {
        margin: initial;
      }
    }
  }

  &__table {
    .Sdk-MuiPaper-root {
      border-radius: 0;
    }

    .Sdk-MuiTableContainer-root {
      border-radius: 0;
    }
  }

  &__table-paper {
    box-shadow: none;
  }

  .cell-header {
    padding: 10px 0;
    height: 57px;
  }

  .Mui-TableHeadCell-Content {
    height: 100%;
  }

  &__drag-cell {
    padding-right: 0;
  }

  &__checkbox-cell {
    padding: 0;
  }

  &__category-cell {
    padding-left: 0;
    padding-right: 0;
    font-weight: normal;
    font-size: 16px;
    width: 200px;
  }

  &__table-header {
    box-shadow: none;
    background-color: var(--background-secondary);
  }

  &__table-row {
    height: 40px;

    &:first-child,
    &:last-child {
      height: 8px;

      &:hover {
        background-color: transparent;
      }

      & td {
        .Sdk-MuiButtonBase-root {
          display: none;
        }
      }
    }

    &:last-child {
      & td {
        border-bottom: 1px solid var(--border-color);
      }
    }

    & td {
      border-bottom: none;
    }

    &:hover {
      background-color: var(--button-hover);

      & td {
        &::after {
          display: none;
        }
      }
    }

    .Sdk-MuiTableCell-root {
      padding-top: 0;
      padding-bottom: 0;
    }
  }

  &__action {
    height: 50px;
    background: var(--background-secondary);
    display: flex;
    align-items: center;
    gap: 18px;
    padding-left: 25px;
    padding-right: 15px;

    & svg {
      color: var(--text-primary);
    }
  }
}
