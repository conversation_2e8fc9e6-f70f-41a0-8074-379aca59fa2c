import { CustomThemeContextType } from './ThemeProvider';

export const DARK_MODE_VALUE = 'Dark';
export const LIGHT_MODE_VALUE = 'Light';

export const DEFAULT_THEME: 'Light' | 'Dark' = LIGHT_MODE_VALUE;

export const DARK_MODE_BACKGROUND = '#101010';
export const LIGHT_MODE_BACKGROUND = '#F5F5F5';

export const setDarkMode = (isDarkMode: boolean) => {
  // Map colors
  if (isDarkMode) {
    document.querySelector('body')?.classList.add('dark');
    document.querySelector('body')?.classList.remove('light');
  } else {
    document.querySelector('body')?.classList.add('light');
    document.querySelector('body')?.classList.remove('dark');
  }
};

// A function like this would be used to detect changes in the system theme, not currently implemented until there is controls in the UI to change between system/dark/light
export const detectThemeChange = () => {
  if (window.matchMedia('(prefers-color-scheme: dark)').addEventListener) {
    window
      .matchMedia('(prefers-color-scheme: dark)')
      .addEventListener('change', (e) => setDarkMode(e.matches));
  } else if (window.matchMedia('(prefers-color-scheme: dark)').addListener) {
    window
      .matchMedia('(prefers-color-scheme: dark)')
      .addListener((e) => setDarkMode(e.matches));
  }
};

export const getTheme = () => localStorage.getItem('theme') ?? DEFAULT_THEME;

export const switchCurrentTheme =
  (toggleTheme: CustomThemeContextType) => () => {
    const theme = localStorage.getItem('theme');
    const isDark = theme === DARK_MODE_VALUE;

    // This will set the AIware them to the new theme via context
    toggleTheme.toggle();

    // This will update the root element to have the new theme class (dark/light)
    setDarkMode(!isDark);

    // This will toggle the theme set in local storage
    localStorage.setItem('theme', isDark ? LIGHT_MODE_VALUE : DARK_MODE_VALUE);

    const { mountWidget, unmountWidget } = window.aiware ?? {};

    if (!mountWidget || !unmountWidget) {
      console.error('Aiware unmountWidget or mountWidget not found');
      return;
    }

    // Update app bar with new theme
    unmountWidget('APP_BAR');
    mountWidget({
      name: 'APP_BAR',
      elementId: 'app-bar',
      config: {
        title: 'Investigate',
        backgroundColor: isDark ? LIGHT_MODE_BACKGROUND : DARK_MODE_BACKGROUND,
        help: true,
        zIndex: 1000,
        notifications: true,
      },
    });
  };

export const initTheme = () => {
  const theme = localStorage.getItem('theme') ?? DEFAULT_THEME;

  const { mountWidget } = window.aiware ?? {};

  if (!mountWidget) {
    console.error('Aiware mountWidget not found');
    return;
  }

  if (theme === DARK_MODE_VALUE) {
    setDarkMode(true);
    mountWidget({
      name: 'APP_BAR',
      elementId: 'app-bar',
      config: {
        title: 'Investigate',
        backgroundColor: DARK_MODE_BACKGROUND,
        help: true,
        zIndex: 1000,
        notifications: true,
      },
    });
  } else if (theme === LIGHT_MODE_VALUE) {
    setDarkMode(false);
    mountWidget({
      name: 'APP_BAR',
      elementId: 'app-bar',
      config: {
        title: 'Investigate',
        backgroundColor: LIGHT_MODE_BACKGROUND,
        help: true,
        zIndex: 1000,
        notifications: true,
      },
    });
  }
};
