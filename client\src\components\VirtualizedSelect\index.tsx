import {
  CircularProgress,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import { ReactNode, useState } from 'react';
import { FixedSizeList as List } from 'react-window';

interface VirtualizedSelectProps {
  placeholder: string;
  options: { id: string; label: string }[];
  value: string | undefined;
  onChange: (value: string) => void;
  loading?: boolean;
  loadMoreItems?: () => void;
  hasMore?: boolean;
  color?: string;
}

const usePlaceholderStyles = makeStyles(() => ({
  placeholder: (props: { color?: string }) => ({
    color: props.color || '#9E9E9E',
  }),
}));

const Placeholder = ({
  children,
  color,
}: {
  children: ReactNode;
  color?: string;
}) => {
  const classes = usePlaceholderStyles({ color });
  return <div className={classes.placeholder}>{children}</div>;
};

const PaperProps = {
  style: {
    maxHeight: 280,
  },
};

const CircularProgressStyle = (style: React.CSSProperties) => ({
  ...style,
  display: 'flex',
  alignItems: 'flex-end',
  justifyContent: 'center',
  height: '100%',
});

export function VirtualizedSelect({
  options,
  value,
  onChange,
  loadMoreItems,
  hasMore = false,
  placeholder,
  color,
}: VirtualizedSelectProps) {
  const [open, setOpen] = useState(false);
  const itemCount = hasMore ? options.length + 1 : options.length;
  const isItemLoaded = (index: number) => index < options.length;
  const handleLoadMore = () => {
    if (hasMore && loadMoreItems) {
      loadMoreItems();
    }
  };

  const handleItemSelect = (selectedId: string) => {
    onChange(selectedId);
    setOpen(false);
  };

  const handleChangeSelect = (event: SelectChangeEvent) => {
    const newValue = event.target.value;
    handleItemSelect(newValue);
  };

  const handleRenderValue = (selected: string) => {
    if (selected === '') {
      return (
        <Placeholder color={color ?? '#9E9E9E'}>{placeholder}</Placeholder>
      );
    }
    return options.find((option) => option.id === selected)?.label || '';
  };

  return (
    <FormControl fullWidth>
      <Select
        value={value ?? ''}
        open={open}
        onOpen={() => setOpen(true)}
        onClose={() => setOpen(false)}
        onChange={handleChangeSelect}
        displayEmpty
        renderValue={handleRenderValue}
        MenuProps={{ PaperProps }}
      >
        {value && (
          <MenuItem value={value} style={{ display: 'none' }}>
            {options.find((opt) => opt.id === value)?.label || ''}
          </MenuItem>
        )}
        <List height={200} itemCount={itemCount} itemSize={50} width="100%">
          {({ index, style }) => {
            if (!isItemLoaded(index)) {
              handleLoadMore();
              return (
                <div style={CircularProgressStyle(style)}>
                  <CircularProgress size={24} />
                </div>
              );
            }
            const option = options[index];
            return (
              <MenuItem
                key={option.id}
                value={option.id}
                style={style}
                onClick={() => handleItemSelect(option.id)}
              >
                {option.label}
              </MenuItem>
            );
          }}
        </List>
      </Select>
    </FormControl>
  );
}
