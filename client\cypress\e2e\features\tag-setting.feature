Feature: Tag Settings

  Background: The user is on Settings screen
    Given The user logins successfully
    Given The user deletes tag if exists
      | tagName         |
      | Tag Test Add    |
      | Tag Test Edit   |
      | Tag Visibility  |
      | Tag Test Name   |
      | Test Name Edit  |
      | Tag Test Delete |
      | BulkVisibility1 |
      | BulkVisibility2 |
      | Bulk Delete 1   |
      | Bulk Delete 2   |
      | multipleEditTag |
      | Tag Reorder 1   |
      | Tag Reorder 2   |
    And The user is on Settings screen
    And The user is on Tag Settings screen

  @e2e @tags-settings
  Scenario: Add Tags
    When The user clicks on the Add button
    Then The user sees Add New "tag" modal
    When The user inputs "tag" name "Tag Test Add"
    And The user confirms by clicking the Add button
    Then The "1st" row of the "tag" list should be updated with name "Tag Test Add"
    And A snack notification 'Tag added successfully' confirming this has succeeded

  @e2e @tags-settings
  Scenario: Add tag in edit mode
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user clicks on the Add button
    Then The user sees Add New "tag" modal
    When The user inputs "tag" name "Tag Test Edit"
    And The user confirms by clicking the Add button
    When The user sees the new "tag" status "Active" for "Tag Test Edit" in edit mode
    And The user clicks Save Changes button
    Then The "1st" row of the "tag" list should be updated with name "Tag Test Edit"
    And A snack notification 'Tags saved successfully' confirming this has succeeded

  @e2e @tags-settings
  Scenario: Tag set individual visibility
    When The user clicks on the Add button
    Given The user creates a default tag name "Tag Visibility"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the visibility next to any status in the list and a dropdown appears
    And The user selects "Inactive" visibility
    Then The user sees the new "tag" status "Inactive" for "Tag Visibility" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The affected "tag" for "Tag Visibility" should have updated visibility "Inactive"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the visibility next to any status in the list and a dropdown appears
    And The user selects "Active" visibility
    Then The user sees the new "tag" status "Active" for "Tag Visibility" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The affected "tag" for "Tag Visibility" should have updated visibility "Active"

  @e2e @tags-settings
  Scenario: Tag change Tag name
    When The user clicks on the Add button
    Given The user creates a default tag name "Tag Test Name"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user edits the new name "Test Name Edit" of any status in the list
    Then The user sees the new "tag" status "Active" for "Test Name Edit" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The affected statuses for "tag" have updated names "Test Name Edit"

  @e2e @tag-settings
  Scenario: Tag individual delete
    When The user clicks on the Add button
    Given The user creates a default tag name "Tag Test Delete"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the delete icon next to any status in the list
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user can click Cancel to discard the edits, restoring the row to its original state
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects the delete icon next to any status in the list
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The deleted row "Tag Test Delete" are permanently removed from the list

  @e2e @tag-settings
  Scenario: Tag Bulk Set Visibility
    When The user clicks on the Add button
    Given The user creates a default tag name "BulkVisibility1"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks on the Add button
    Given The user creates a default tag name "BulkVisibility2"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 1 statuses using checkboxes
    When The user clicks the Set Visibility button
    And The user selects Inactive visibility
    When The user confirms by clicking Save Changes button
    When The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The affected "tag" for "BulkVisibility2" should have updated visibility "Inactive"
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 2 statuses using checkboxes
    When The user clicks the Set Visibility button
    And The user selects Active visibility
    When The user confirms by clicking Save Changes button
    When The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The affected "tag" for "BulkVisibility2" should have updated visibility "Active"

  @e2e @tag-settings
  Scenario: Tag Bulk Delete
    When The user clicks on the Add button
    Given The user creates a default tag name "Bulk Delete 1"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks on the Add button
    Given The user creates a default tag name "Bulk Delete 2"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 1 statuses using checkboxes
    When The user clicks the Delete button
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user can click Cancel to discard the edits, restoring the row to its original state
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user selects multiple 2 statuses using checkboxes
    When The user clicks the Delete button
    Then A confirmation dialog appears
    When The user confirms the deletion
    Then The row is greyed out in the list
    And The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The deleted row "Bulk Delete 1" are permanently removed from the list
    And The deleted row "Bulk Delete 2" are permanently removed from the list

  @e2e @tags-settings
  Scenario: Tags reorder
    When The user clicks on the Add button
    Then The user sees Add New "tag" modal
    When The user inputs "tag" name "Tag Reorder 1"
    And The user confirms by clicking the Add button
    Then The "1st" row of the "tag" list should be updated with name "Tag Reorder 1"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks on the Add button
    Then The user sees Add New "tag" modal
    When The user inputs "tag" name "Tag Reorder 2"
    And The user confirms by clicking the Add button
    Then The "1st" row of the "tag" list should be updated with name "Tag Reorder 2"
    And A snack notification 'Tag added successfully' confirming this has succeeded
    When The user clicks Edit button
    Then A new button entitled Save Changes appears and is disabled
    When The user drags the "1st" row to "3rd" row in the list
    When The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    And The "1st" row of the "tag" list should be updated with name "Tag Reorder 1"
    And The "3rd" row of the "tag" list should be updated with name "Tag Reorder 2"

  @e2e @tags-settings
  Scenario: Two users editing Tags
    When The user clicks on the Add button
    When The user creates a default tag name "multipleEditTag"
    Then A snack notification "Tag added successfully" confirming this has succeeded
    When The user changes the visibility to "Inactive"
    Then The user sees the new "tag" status "Inactive" for "multipleEditTag" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    When The user is on Tag Settings screen
    Then The affected "tag" for "multipleEditTag" should have updated visibility "Inactive"
    When The user logins as "user2"
    And The user is on Settings screen
    And The user is on Tag Settings screen
    Then The affected "tag" for "multipleEditTag" should have updated visibility "Inactive"
    When The user changes the visibility to "Active"
    Then The user sees the new "tag" status "Active" for "multipleEditTag" in edit mode
    And The user clicks Save Changes button
    Then A snack notification 'Tags saved successfully' confirming this has succeeded
    Then The affected "tag" for "multipleEditTag" should have updated visibility "Active"

  @e2e @tags-settings @keyboard-event
  Scenario: Keyboard navigation with TAB and arrow keys in tag table
    When The user clicks Edit button
    And The user presses "Tab" key in settings table
    And The user presses "Enter" key 1 time(s)
    Then The "1st" row is selected
    And The user presses "ArrowDown" key 2 time(s)
    And The user presses "Enter" key 1 time(s)
    Then The "1st" row is selected
    And The "3rd" row is selected
