# TO DO: as Development is still in progress
Feature: Search Results

  Background: The user is on the Search screen
    Given The user is on the Search screen
    When The user changes group view to 'Un-Grouped'
    Given The user is on the Search screen

  @e2e @search
  Scenario: Verify Search page
    When The user verifies that the information on the search page is correct

  @e2e @search
  Scenario: Verify search page shows init empty state on first visit
    Then The user verifies that the search page shows the init empty state
    When The user enters keyword "lucy.mp4"
    When The user clicks the search button
    Then The user sees search results

  @e2e @search
  Scenario: Verify user can not search when inputting incorrect keyword
    When The user enters keyword "#@!%^&*()"
    Then The user clicks the search button
    Then The error message "Something went wrong" should be shown

  @e2e @search
  Scenario: Verify user can search by keyword
    When The user enters keyword "lucy.mp4"
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user chooses the first case in the list
    And The user clicks on kebab icon of first row in search results
    And The user clicks on "View File" in kebab menu
    Then The user verifies that the text "lucy.mp4" is visible in the file detail view

  @e2e @search
  Scenario: Verify user can search by case information
    When The user closes the "Advanced Search" section
    When The user picks "e2e-search" from the "caseStatus" dropdown box in case information section
    And The user selects a date range from day 01 to day 02 in 'June'
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user clicks on "Reset All" button
    And The user inputs case ID "6692"
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user clicks on "Reset All" button
    And The user selects a date range from day 01 to day 02 in 'June'
    # Filter by caseStatus is not working
    And The user picks "Completed" from the "fileStatus" dropdown box in case information section
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results

  @e2e @search
  Scenario: Verify user can search by evidence type
    When The user closes the "Case Information" section
    And The user closes the "Advanced Search" section
    And The user opens the "Evidence Type" section
    And The user checks evidence type "911 Call Recording"
    Then The user clicks the search button
    # TODO: Create own Test File
    Then The user sees case id "hvu2.txt" in search results
    When The user clicks on "Reset All" button
    And The user checks the following evidence types:
      | evidenceType              |
      | 911 Call Recording        |
      | Arrest Report             |
      | Body Worn Camera          |
      | Booking Photo             |
      | Citizen Submitted Video   |
      | Crime Scene Photo         |
      | Crime Scene Video         |
      | In Car Video              |
      | Interview Audio Recording |
      | Interview Room Recording  |
      | Mobile Device Extraction  |
      | Security Camera Video     |
    Then The user clicks the search button
    Then The user sees case id "hvu2.txt" in search results

  @e2e @search
  Scenario: Verify user can search by file type
    And The user selects a date range from day 01 to day 02 in 'June'
    And The user closes the "Case Information" section
    And The user opens the "File Type" section
    # Then The user checks file type "Audio"
    Then The user checks the following file types:
      | fileType |
      | Video    |
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results

  @e2e @search
  Scenario: Verify user can reset all search field and search box
    Given The user enters keyword "Eyewitness News YouTube"
    And The user closes the "Case Information" section
    And The user opens the "Advanced Search" section
    And The user inputs the following values into the advanced search fields:
      | placeholder               | value   |
      | File Name                 | lucy    |
      | Words in Transcription    | hello   |
      | Vehicle Recognition       | Car     |
      | License Plate Recognition | ABC 123 |
      | Text Recognition          | Hello   |
    When The user closes the "Advanced Search" section
    Then The user opens the "Case Information" section
    And The user picks "status123" from the "caseStatus" dropdown box in case information section
    And The user inputs case ID "000-000-000-001"
    And The user selects a date range from day 10 to day 20 in 'May'
    And The user picks "Completed" from the "fileStatus" dropdown box in case information section
    When The user closes the "Case Information" section
    Then The user opens the "Evidence Type" section
    And The user checks the following evidence types:
      | evidenceType              |
      | 911 Call Recording        |
      | Arrest Report             |
      | Body Worn Camera          |
      | Booking Photo             |
      | Citizen Submitted Video   |
      | Crime Scene Photo         |
      | Crime Scene Video         |
      | In Car Video              |
      | Interview Audio Recording |
      | Interview Room Recording  |
      | Mobile Device Extraction  |
      | Security Camera Video     |
    Then The user closes the "Evidence Type" section
    And The user opens the "File Type" section
    Then The user checks the following file types:
      | fileType |
      | Photo    |
      | Video    |
      | Audio    |
      | Document |
      | Image    |
    Then The user closes the "File Type" section
    When The user clicks on "Reset All" button
    Then The user verifies that the search box and all search fields are reset

  @e2e @search @failed
  Scenario: Verify user can edit a file by meta data
    And The user selects a date range from day 01 to day 02 in 'June'
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    # Then The user opens menu item for file "BODYCAM_SanDiegO_police _ABC7.mp4"
    # Then The user selects "Edit metadata" in menu item

  @e2e @search
  Scenario: Verify user can add a file to case
    Given The user is on Case Management screen
    Given The user deletes the following cases if exist: "Cypress Test Search File"
    Given The user deletes the following SDOs and folders if exist:
      | folderName                             |
      | Cypress Test Search File               |
    When The user creates a default case id "Cypress Test Search File"
    Then The user sees notification "Case Created Successfully"
    And The user sees the success case modal
    When The user closes the success case modal
    And The user sees case "Cypress Test Search File" in case table list
    Given The user is on the Search screen
    And The user selects a date range from day 01 to day 02 in 'June'
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    Then The user opens menu item for file "lucy.mp4"
    Then The user clicks on "Add to Case" in kebab menu
    And The user selects the add target case "Cypress Test Search File"
    Then The user sees notification "Case Updated Successfully!"
    Given The user is on Case Management screen
    When The user opens case "Cypress Test Search File"
    And The user clicks View Case Details button
    Then The user sees file "lucy.mp4" in file table list

  @e2e @search
  Scenario: Verify user can add multiple files to case
    Given The user is on Case Management screen
    Given The user deletes the following cases if exist: "Cypress Test Search Add Multiple Files"
    Given The user deletes the following SDOs and folders if exist:
      | folderName                             |
      | Cypress Test Search Add Multiple Files |
    When The user creates a default case id "Cypress Test Search Add Multiple Files"
    Then The user sees notification "Case Created Successfully"
    And The user sees the success case modal
    When The user closes the success case modal
    And The user sees case "Cypress Test Search Add Multiple Files" in case table list
    Given The user is on the Search screen
    And The user closes the "Case Information" section
    And The user opens the "Advanced Search" section
    When The user inputs 'lucy.mp4' into the text field with placeholder 'File Name' in the advanced search section
    And The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user checks the boxes for the items named "lucy.mp4, lucy.mp4"
    And The user clicks the add button in search page
    Then The user selects the add target case "Cypress Test Search Add Multiple Files"
    Then The user sees notification "Case Updated Successfully!"
    Given The user is on Case Management screen
    When The user opens case "Cypress Test Search Add Multiple Files"
    And The user clicks View Case Details button
    Then The user sees file "lucy.mp4" in file table list

  @e2e @search @failed @skip
  Scenario: Verify user can delete a file through kebab menu
    When The user closes the "Advanced Search" section
    And The user opens the "Case Information" section
    And The user selects a date range from day 01 to day 02 in 'June'
    Then The user sees case id "lucy.mp4" in search results
    # Then The user opens menu item for file "BODYCAM_SanDiegO_police _ABC7.mp4"
    # Then The user selects "Delete" in menu item

  @e2e @search @failed @skip
  Scenario: Verify user can delete multiple files through
    # When The user selects file "Eyewitness News YouTube"
    # And The user selects file "Eyewitness News YouTube 2"
    # Then The user clicks on the delete button

  @e2e @search
  Scenario: Verify user can sort search table columns
    When The user closes the "Case Information" section
    And The user opens the "Advanced Search" section
    Then The user inputs 'lucy.mp4' into the text field with placeholder 'File Name' in the advanced search section
    And The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user hovers over the '<label>' label and see cursor "pointer"
    Given The user presses the '<label>' sort button to sort by '<sortedBy>'
    Then '<label>' is sorted by '<sortedBy>'

    Examples:
      | label       | sortedBy |
      | createdTime | a-z      |
      | fileName    | z-a      |
      | fileName    | a-z      |

  @e2e @search
  Scenario: Verify the "Rows per page" functionality on the Search Result page
    When The user closes the "Case Information" section
    And The user opens the "File Type" section
    Then The user checks the following file types:
      | fileType |
      | Video    |
    When The user clicks the search button
    When The user selects "50" rows per page
    Then The total number of rows displayed should be at most 50
    When The user selects "100" rows per page
    Then The total number of rows displayed should be at most 100

  @e2e @search
  Scenario: Verify pagination functionality on the Search Result page
    When The user closes the "Case Information" section
    And The user opens the "File Type" section
    Then The user checks the following file types:
      | fileType |
      | Video    |
    When The user clicks the search button
    When The user selects "50" rows per page
    Then The user should see page range "1-50"
    When The user clicks the next page button
    Then The user should see page range "51-100"
    When The user clicks the previous page button
    Then The user should see page range "1-50"

  @e2e @search
  Scenario: Verify list and grid view toggle functionality
    When The user closes the "Case Information" section
    And The user opens the "Advanced Search" section
    When The user inputs 'lucy.mp4' into the text field with placeholder 'File Name' in the advanced search section
    And The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user clicks the view toggle button
    Then The user verifies the current view type is "grid"
    And The user should see the grid view is active
    When The user clicks the view toggle button
    Then The user verifies the current view type is "list"
    And The user should see the list view is active
    When The user clicks the view toggle button
    Then The user verifies the current view type is "grid"
    And The user should see the grid view is active
    When The user clicks the view toggle button
    Then The user verifies the current view type is "list"
    And The user should see the list view is active

  @e2e @search
  Scenario: Verify user can search by Advanced Search
    When The user closes the "Case Information" section
    And The user opens the "Advanced Search" section
    Then The user inputs 'lucy' into the text field with placeholder 'File Name' in the advanced search section
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user clicks on "Reset All" button
    And The user inputs 'Eyewitness News YouTube' into the text field with placeholder 'Words in Transcription' in the advanced search section
    Then The user clicks the search button
    Then The user sees case id "BODYCAM_SanDiegO_police _ABC7.mp4" in search results
    When The user clicks on "Reset All" button
    When The user closes the "Advanced Search" section
    And The user opens the "Case Information" section
    And The user selects a date range from day 01 to day 02 in 'June'
    And The user opens the "Advanced Search" section
    # Vehicle Recognition is not working
    And The user inputs 'Car' into the text field with placeholder 'Vehicle Recognition' in the advanced search section
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user clicks on "Reset All" button
    When The user closes the "Advanced Search" section
    And The user selects a date range from day 01 to day 02 in 'June'
    And The user closes the "Case Information" section
    And The user opens the "Advanced Search" section
    # License Plate Recognition is not working
    And The user inputs 'none' into the text field with placeholder 'License Plate Recognition' in the advanced search section
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user clicks on "Reset All" button
    And The user inputs 'Collect' into the text field with placeholder 'Text Recognition' in the advanced search section
    Then The user clicks the search button
    Then The user sees case id "Google OCR Test" in search results

  @e2e @search @keyboard-event
  Scenario: Keyboard navigation between search result
    When The user enters keyword "lucy.mp4"
    Then The user clicks the search button
    Then The user sees case id "lucy.mp4" in search results
    When The user presses "Tab" key on the table
    Then The "1st" item in the table is selected
    When The user presses "ArrowDown" key 1 time(s)
    Then The "2nd" item in the table is selected
    When The user presses "ArrowUp" key 1 time(s)
    Then The "1st" item in the table is selected

  @e2e @search
  Scenario: Delete SDOs and folders after run
    Given The user deletes the following SDOs and folders if exist:
      | folderName                             |
      | Cypress Test Search File               |
      | Cypress Test Search Add Multiple Files |
