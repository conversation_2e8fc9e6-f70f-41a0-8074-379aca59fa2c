import {
  createContext,
  useContext,
  useMemo,
  useState,
  type FC,
  type PropsWithChildren,
} from 'react';
import {
  ThemeProvider as MuiThemeProvider,
  StyledEngineProvider,
  type Theme,
} from '@mui/material/styles';
import darkTheme from './dark';
import lightTheme from './light';
import { DARK_MODE_VALUE, getTheme } from './initTheme';

interface AIWareThemeProviderProps {
  fullHeight?: boolean;
  theme?: Theme;
  darkMode?: boolean;
}

export interface CustomThemeContextType {
  toggle: () => void;
  isDark: boolean;
}

const CustomThemeContext = createContext<CustomThemeContextType | undefined>(
  undefined
);

export const ThemeProvider: FC<PropsWithChildren<AIWareThemeProviderProps>> = (
  props
) => {
  const [isDark, setIsDark] = useState(getTheme() === DARK_MODE_VALUE);

  function toggle() {
    if (isDark) {
      setIsDark(false);
    } else {
      setIsDark(true);
    }
  }

  const theme = useMemo(() => (isDark ? darkTheme : lightTheme), [isDark]);

  return (
    <CustomThemeContext.Provider value={{ toggle, isDark }}>
      <StyledEngineProvider injectFirst>
        <MuiThemeProvider theme={theme}>{props.children}</MuiThemeProvider>
      </StyledEngineProvider>
    </CustomThemeContext.Provider>
  );
};

// Use this hook to toggle the theme in the app
export function useToggleTheme() {
  const context = useContext(CustomThemeContext);
  if (context === undefined) {
    throw new Error(
      'useCustomThemeContext must be used within an CustomThemeProvider'
    );
  }
  return context;
}

export default ThemeProvider;
