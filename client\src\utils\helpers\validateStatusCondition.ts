import { sagaIntl } from '@i18n';

export const NO_STATUS_ID = 'no-status';

export const DEFAULT_NO_CASE_STATUS = {
  id: NO_STATUS_ID,
  label: sagaIntl().formatMessage({ id: 'noStatus' }),
  active: true,
  color: '',
};

function validateStatusCondition(
  requiredStatusIds: string[],
  existingStatuses?: string[]
) {
  const includeNoStatus = requiredStatusIds.some(
    (statusId) => statusId.trim() === NO_STATUS_ID
  );

  const values = includeNoStatus
    ? existingStatuses?.map((statusId) => statusId.trim()) || []
    : requiredStatusIds.map((statusId) => statusId.trim());

  return {
    not: includeNoStatus,
    values,
  };
}

export { validateStatusCondition };
