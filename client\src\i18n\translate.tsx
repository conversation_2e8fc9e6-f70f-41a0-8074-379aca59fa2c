import {
  FormatDateOptions,
  FormattedDate,
  FormattedMessage,
  useIntl,
} from 'react-intl';
import { ComponentProps } from 'react';

const TranslateMessage = (
  id: string,
  values: ComponentProps<typeof FormattedMessage>['values'] = {}
) => <FormattedMessage id={id} values={{ ...values }} />;

const TranslateDate = (value: string | number | Date | undefined) => {
  const options: FormatDateOptions = {
    year: 'numeric',
    month: 'short',
    weekday: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  };
  return <FormattedDate value={value} {...options} />;
};

const Intl = () => useIntl();

export default { TranslateMessage, TranslateDate, Intl };
