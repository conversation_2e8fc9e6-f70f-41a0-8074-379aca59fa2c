import { CaseTag } from '@shared-types/types';
import { selectFetchedTags } from '@store/modules/settings/slice';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

export const useValidTags = (caseTagIds: string[]) => {
  const caseTagIdsSet = new Set<string>(caseTagIds);
  const fetchedTags = useSelector(selectFetchedTags);

  const [validTags, setValidTags] = useState<CaseTag[]>([]);

  useEffect(() => {
    const remainTags: CaseTag[] = [],
      caseTags: CaseTag[] = [];

    fetchedTags.forEach((tag) => {
      if (caseTagIdsSet.has(tag.id)) {
        caseTags.push(tag);
      } else if (tag.active) {
        remainTags.push(tag);
      }
    });

    setValidTags(
      [...remainTags, ...caseTags].sort((a, b) =>
        // sort allTags by label
        a.label.localeCompare(b.label)
      )
    );
  }, [fetchedTags, caseTagIds]);

  return { tags: validTags };
};
