import { CaseStatus } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function saveStatusList({
  statuses,
  statusSchemaId,
  userId,
  gql,
  sdoId,
}: {
  statuses: CaseStatus[];
  statusSchemaId: string;
  userId: string;
  gql: GQLApi;
  sdoId?: string;
}) {
  if (sdoId && sdoId !== '') {
    const latestStatusesSdo = await gql.getLatestSDO(statusSchemaId);
    if (sdoId !== latestStatusesSdo.id) {
      console.warn('On saving statuses, the statuses have been modified.');
      throw new Error(`concurrentModificationError_${latestStatusesSdo.id}`);
    }
  }

  const data = {
    statuses,
    createdBy: userId,
  };

  const newSdo = await gql.createSDO({
    schemaId: statusSchemaId,
    data,
  });

  return newSdo;
}
