import { CaseStatus } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function fetchStatusList({
  schemaId,
  gql,
}: {
  schemaId: string;
  gql: GQLApi;
}): Promise<{ statuses: CaseStatus[]; sdoId: string }> {
  const latestStatusSdo = await gql.getLatestSDO(schemaId);

  if (
    typeof latestStatusSdo.data === 'object' &&
    latestStatusSdo.data !== null &&
    'statuses' in latestStatusSdo.data
  ) {
    return {
      statuses: latestStatusSdo.data.statuses as CaseStatus[],
      sdoId: latestStatusSdo.id,
    };
  } else {
    return { statuses: [], sdoId: '' };
  }
}
